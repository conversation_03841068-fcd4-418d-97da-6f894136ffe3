"""
Example usage of ProgressDialog and AsyncOperationRunner for common operations.

This file demonstrates how to integrate the progress dialog with existing
long-running operations in the Pinnacle IO UI.
"""

import time
from typing import Callable, Any, Optional
from threading import Event
from pinnacle_io.api import PinnacleReader
from pinnacle_io.ui.widgets.progress_dialog import ProgressDialog, AsyncOperationRunner
from pinnacle_io.ui.progress import ProgressManager, ProgressState


class ProgressDialogExamples:
    """
    Example implementations showing how to use ProgressDialog and AsyncOperationRunner
    with common Pinnacle IO operations.
    """

    @staticmethod
    def load_data_with_progress(main_window, data_path: str):
        """
        Example: Load Pinnacle data with progress tracking.

        This shows how to wrap the existing load_data method with progress tracking.
        """
        runner = AsyncOperationRunner(
            parent=main_window.root,
            title="Loading Pinnacle Data...",
            cancelable=True,
            development_mode=True  # Enable timing logs
        )

        def load_operation(progress_callback: Callable, status_callback: Callable, **kwargs):
            """Operation function that loads data with progress updates."""
            try:
                # Step 1: Initialize reader
                progress_callback(10, "Initializing Pinnacle reader...")
                time.sleep(0.5)  # Simulate work
                reader = PinnacleReader(str(data_path))

                # Step 2: Load institution
                progress_callback(25, "Loading institution data...")
                time.sleep(0.3)
                institution = reader.get_institution()

                # Step 3: Load patients
                progress_callback(40, "Loading patient list...")
                time.sleep(0.2)
                # Institution already contains patient_lite_list

                # Step 4: Auto-select first patient
                if institution.patient_lite_list:
                    progress_callback(55, "Loading first patient...")
                    time.sleep(0.4)
                    patient = reader.get_patient(
                        institution=institution.institution_id,
                        patient=institution.patient_lite_list[0].patient_id
                    )

                    # Step 5: Load first plan if available
                    if patient.plan_list:
                        progress_callback(70, "Loading treatment plan...")
                        time.sleep(0.3)
                        plan = patient.plan_list[0]

                        # Step 6: Load plan data
                        progress_callback(85, "Loading plan details...")
                        trials = reader.get_trials(
                            institution=institution.institution_id,
                            patient=patient.patient_lite_list[0].patient_id,
                            plan=plan.plan_id
                        )
                        rois = reader.get_rois(
                            institution=institution.institution_id,
                            patient=patient.patient_lite_list[0].patient_id,
                            plan=plan.plan_id
                        )

                        # Step 7: Load image set
                        progress_callback(95, "Loading CT images...")
                        time.sleep(0.2)
                        image_set = reader.get_image_set(
                            institution=institution.institution_id,
                            patient=patient.patient_lite_list[0].patient_id,
                            image_set=0
                        )

                progress_callback(100, "Loading complete!")

                return {
                    'reader': reader,
                    'institution': institution,
                    'success': True
                }

            except Exception as e:
                raise Exception(f"Failed to load data: {str(e)}")

        def on_complete(result: dict):
            """Called when loading completes successfully."""
            try:
                # Update main window with loaded data
                main_window.pinnacle_reader = result['reader']
                main_window.institution = result['institution']
                main_window._load_initial_data()

                print("Data loaded successfully with progress tracking!")

            except Exception as e:
                from tkinter import messagebox
                messagebox.showerror("Post-Load Error", f"Error updating UI: {e}")

        def on_error(error: Exception):
            """Called when loading fails."""
            from tkinter import messagebox
            messagebox.showerror("Loading Error", f"Failed to load data: {error}")

        # Run the operation
        runner.run_operation(
            operation_func=load_operation,
            callback_on_complete=on_complete,
            callback_on_error=on_error,
            data_path=data_path
        )

    @staticmethod
    def export_dicom_with_progress(main_window, export_path: str):
        """
        Example: Export DICOM files with progress tracking.

        This shows how to wrap DICOM export operations with progress tracking.
        """
        runner = AsyncOperationRunner(
            parent=main_window.root,
            title="Exporting DICOM Files...",
            cancelable=True,
            development_mode=True
        )

        def export_operation(progress_callback: Callable, status_callback: Callable, **kwargs):
            """Operation function that exports DICOM with progress updates."""
            from pinnacle_io.converters.dicom_export_manager import DicomExportManager

            try:
                # Validate current selection
                if not main_window._validate_current_selection():
                    raise Exception("Invalid selection for export")

                # Get current selections
                current_patient, current_plan, current_trial = main_window._get_current_selections()

                progress_callback(5, "Validating export data...")
                time.sleep(0.1)

                if not all([current_patient, current_plan]):
                    raise Exception("Please select a patient and plan before exporting")

                # Initialize export manager
                progress_callback(15, "Initializing DICOM export manager...")
                export_manager = DicomExportManager(main_window.pinnacle_reader)

                # Check for cancellation before each major step
                if not progress_callback(20, "Preparing CT export..."):
                    raise InterruptedError("Operation cancelled by user")

                # Export each modality with progress updates
                modalities = ["CT", "RTSTRUCT", "RTPLAN", "RTDOSE"]
                modality_progress = 70 // len(modalities)  # 70% for exports, 30% for setup/finalize

                for i, modality in enumerate(modalities):
                    start_progress = 20 + (i * modality_progress)

                    if not progress_callback(start_progress, f"Exporting {modality}..."):
                        raise InterruptedError("Operation cancelled by user")

                    time.sleep(0.5)  # Simulate export time

                    # Update progress within modality export
                    mid_progress = start_progress + (modality_progress // 2)
                    if not progress_callback(mid_progress, f"Processing {modality} data..."):
                        raise InterruptedError("Operation cancelled by user")

                # Final export call
                progress_callback(90, "Finalizing export...")
                result = export_manager.export_modalities(
                    modalities=modalities,
                    institution=main_window.institution,
                    patient=current_patient,
                    plan=current_plan,
                    trial=current_trial,
                    output_path=export_path,
                )

                progress_callback(100, "Export completed!")
                return result

            except InterruptedError:
                status_callback("Export cancelled by user")
                raise
            except Exception as e:
                raise Exception(f"Export failed: {str(e)}")

        def on_complete(result):
            """Called when export completes successfully."""
            main_window._show_export_results(result, "Export")

        def on_error(error: Exception):
            """Called when export fails."""
            if isinstance(error, InterruptedError):
                print("Export cancelled by user")
            else:
                from tkinter import messagebox
                messagebox.showerror("Export Error", f"Export failed: {error}")

        # Run the operation
        runner.run_operation(
            operation_func=export_operation,
            callback_on_complete=on_complete,
            callback_on_error=on_error
        )

    @staticmethod
    def simple_progress_example(parent_window):
        """
        Simple example showing basic ProgressDialog usage.
        """
        dialog = ProgressDialog(
            parent=parent_window,
            title="Simple Operation",
            cancelable=True,
            development_mode=True
        )

        def simulate_work():
            """Simulate a long-running operation."""
            dialog.show("Starting operation...", max_progress=100)

            try:
                for i in range(100):
                    # Check for cancellation
                    if dialog.is_cancelled_flag():
                        dialog.update_status("Operation cancelled")
                        time.sleep(0.5)
                        break

                    # Simulate work
                    time.sleep(0.05)

                    # Update progress
                    if i % 10 == 0:
                        if not dialog.update_progress(i + 1, f"Processing step {i + 1}..."):
                            break  # Cancelled
                    else:
                        if not dialog.update_progress(i + 1):
                            break  # Cancelled

                if not dialog.is_cancelled_flag():
                    dialog.update_progress(100, "Operation completed!")
                    time.sleep(1)

            finally:
                dialog.hide()

        # Run in separate thread
        import threading
        thread = threading.Thread(target=simulate_work, daemon=True)
        thread.start()

    @staticmethod
    def multi_stage_progress_example(main_window):
        """
        Example showing multi-stage progress management using ProgressManager.

        This demonstrates how different components can coordinate progress
        across multiple stages of a complex operation.
        """
        def simulate_multi_stage_operation():
            """Simulate a multi-stage data loading operation."""

            # Start progress operation
            progress_manager = main_window.start_progress_operation(
                operation_id="multi_stage_demo",
                title="Multi-Stage Data Loading Demo",
                cancelable=True
            )

            try:
                # Show initial dialog
                progress_manager.show("Initializing multi-stage operation...", max_progress=100)

                # Stage 1: MainWindow-level operations (40% weight)
                progress_manager.register_stage("main_window", weight=40, name="MainWindow Operations")
                progress_manager.set_current_stage("main_window")

                # Simulate MainWindow operations
                for i in range(5):
                    if progress_manager.is_cancelled_flag():
                        break

                    stage_progress = (i + 1) * 20  # 20, 40, 60, 80, 100
                    status = f"MainWindow step {i + 1}/5: Loading basic data..."

                    if not progress_manager.update_progress(stage_progress, status):
                        break  # Cancelled

                    time.sleep(0.3)

                progress_manager.complete_stage("main_window")

                if progress_manager.is_cancelled_flag():
                    return

                # Stage 2: RightSidebar-level operations (35% weight)
                progress_manager.register_stage("right_sidebar", weight=35, name="RightSidebar Processing")
                progress_manager.set_current_stage("right_sidebar")

                # Simulate RightSidebar operations
                sidebar_steps = ["ROI processing", "POI processing", "Trial coordination"]
                for i, step_name in enumerate(sidebar_steps):
                    if progress_manager.is_cancelled_flag():
                        break

                    stage_progress = ((i + 1) / len(sidebar_steps)) * 100
                    status = f"RightSidebar: {step_name}..."

                    if not progress_manager.update_progress(stage_progress, status):
                        break

                    time.sleep(0.4)

                progress_manager.complete_stage("right_sidebar")

                if progress_manager.is_cancelled_flag():
                    return

                # Stage 3: Individual Panel operations (25% weight)
                progress_manager.register_stage("panels", weight=25, name="Individual Panels")
                progress_manager.set_current_stage("panels")

                # Simulate panel operations
                panels = ["ROI Panel", "POI Panel", "Beams Panel", "Dose Panel"]
                for i, panel_name in enumerate(panels):
                    if progress_manager.is_cancelled_flag():
                        break

                    stage_progress = ((i + 1) / len(panels)) * 100
                    status = f"{panel_name}: Processing visualization data..."

                    if not progress_manager.update_progress(stage_progress, status):
                        break

                    time.sleep(0.3)

                progress_manager.complete_stage("panels")

                if not progress_manager.is_cancelled_flag():
                    progress_manager.complete_operation(success=True, final_message="Multi-stage operation completed successfully!")
                else:
                    progress_manager.complete_operation(success=False, final_message="Operation cancelled by user")

            except Exception as e:
                progress_manager.complete_operation(success=False, final_message=f"Operation failed: {str(e)}")

            # Note: In development mode, dialog stays open for timing review

        # Run in separate thread
        import threading
        thread = threading.Thread(target=simulate_multi_stage_operation, daemon=True)
        thread.start()

    @staticmethod
    def progress_state_awareness_example(main_window):
        """
        Example showing progress state awareness and operation coordination.

        This demonstrates how components can detect existing progress operations
        and either join them or wait for completion.
        """
        def simulate_aware_operation():
            """Simulate an operation that's aware of existing progress."""

            # Check if progress is already active
            existing_manager = main_window.get_progress_manager()

            if existing_manager and existing_manager.is_active():
                print("Another progress operation is already active!")
                print(f"Active operation: {existing_manager.operation_id}")

                # We could either:
                # 1. Join the existing operation as a new stage
                # 2. Wait for completion
                # 3. Queue for later execution

                # Example: Join as a new stage
                existing_manager.register_stage("additional_work", weight=10, name="Additional Processing")
                existing_manager.set_current_stage("additional_work")

                # Do our work within the existing operation
                for i in range(3):
                    if existing_manager.is_cancelled_flag():
                        break

                    stage_progress = ((i + 1) / 3) * 100
                    status = f"Additional work step {i + 1}/3..."

                    if not existing_manager.update_progress(stage_progress, status):
                        break

                    time.sleep(0.5)

                existing_manager.complete_stage("additional_work")
                print("Additional work completed within existing operation")

            else:
                # No active operation, start our own
                progress_manager = main_window.start_progress_operation(
                    operation_id="aware_operation",
                    title="State-Aware Operation",
                    cancelable=True
                )

                try:
                    progress_manager.show("Starting state-aware operation...", max_progress=100)

                    # Single stage operation
                    for i in range(10):
                        if progress_manager.is_cancelled_flag():
                            break

                        progress = (i + 1) * 10
                        status = f"Processing item {i + 1}/10..."

                        if not progress_manager.update_progress(progress, status):
                            break

                        time.sleep(0.2)

                    if not progress_manager.is_cancelled_flag():
                        progress_manager.complete_operation(success=True, final_message="State-aware operation completed!")
                    else:
                        progress_manager.complete_operation(success=False, final_message="Operation cancelled")

                except Exception as e:
                    progress_manager.complete_operation(success=False, final_message=f"Operation failed: {str(e)}")

        # Run in separate thread
        import threading
        thread = threading.Thread(target=simulate_aware_operation, daemon=True)
        thread.start()

    @staticmethod
    def realistic_pinnacle_loading_example(main_window, data_path: str):
        """
        Realistic example showing how ProgressManager would be used in actual Pinnacle data loading.

        This mirrors the actual data loading workflow described in the README.
        """
        def simulate_realistic_loading():
            """Simulate the actual Pinnacle data loading workflow with proper stage coordination."""

            progress_manager = main_window.start_progress_operation(
                operation_id="pinnacle_data_load",
                title="Loading Pinnacle Data",
                cancelable=True
            )

            try:
                progress_manager.show("Initializing Pinnacle data loading...", max_progress=100)

                # Stage 1: MainWindow Initial Data Loading (40% of total)
                progress_manager.register_stage("main_window_init", weight=40, name="Initial Data Loading")
                progress_manager.set_current_stage("main_window_init")

                # Simulate _load_initial_data method
                if not progress_manager.update_progress(20, "Loading institution data..."):
                    return
                time.sleep(0.3)

                if not progress_manager.update_progress(40, "Populating patient list..."):
                    return
                time.sleep(0.2)

                if not progress_manager.update_progress(60, "Auto-selecting first patient..."):
                    return
                time.sleep(0.3)

                if not progress_manager.update_progress(80, "Loading patient data..."):
                    return
                time.sleep(0.4)

                if not progress_manager.update_progress(100, "Patient selection completed"):
                    return
                time.sleep(0.2)

                progress_manager.complete_stage("main_window_init")

                # Stage 2: Plan Selection and Data Loading (35% of total)
                progress_manager.register_stage("plan_loading", weight=35, name="Plan Data Processing")
                progress_manager.set_current_stage("plan_loading")

                # Simulate on_plan_selected method
                if not progress_manager.update_progress(25, "Loading trials data..."):
                    return
                time.sleep(0.5)

                if not progress_manager.update_progress(50, "Loading ROIs data..."):
                    return
                time.sleep(0.4)

                if not progress_manager.update_progress(75, "Loading points data..."):
                    return
                time.sleep(0.3)

                if not progress_manager.update_progress(100, "Loading CT image set..."):
                    return
                time.sleep(0.6)

                progress_manager.complete_stage("plan_loading")

                # Stage 3: UI Component Updates (25% of total)
                progress_manager.register_stage("ui_updates", weight=25, name="UI Component Updates")
                progress_manager.set_current_stage("ui_updates")

                # Simulate RightSidebar and panel updates
                ui_components = [
                    ("CT Viewer", "Loading CT visualization..."),
                    ("ROI Panel", "Processing ROI contours..."),
                    ("POI Panel", "Setting up point displays..."),
                    ("Beams Panel", "Calculating beam geometry..."),
                    ("Dose Panel", "Processing dose distribution...")
                ]

                for i, (component, status) in enumerate(ui_components):
                    if progress_manager.is_cancelled_flag():
                        break

                    component_progress = ((i + 1) / len(ui_components)) * 100

                    if not progress_manager.update_progress(component_progress, f"{component}: {status}"):
                        break

                    time.sleep(0.3)

                progress_manager.complete_stage("ui_updates")

                # Complete the overall operation
                if not progress_manager.is_cancelled_flag():
                    progress_manager.complete_operation(
                        success=True,
                        final_message="Pinnacle data loaded successfully! All components ready."
                    )
                else:
                    progress_manager.complete_operation(
                        success=False,
                        final_message="Data loading cancelled by user"
                    )

            except Exception as e:
                progress_manager.complete_operation(
                    success=False,
                    final_message=f"Data loading failed: {str(e)}"
                )

        # Run in separate thread
        import threading
        thread = threading.Thread(target=simulate_realistic_loading, daemon=True)
        thread.start()


def integrate_with_main_window(main_window):
    """
    Example of how to integrate progress dialogs into MainWindow methods.

    This would replace existing methods with progress-tracked versions.
    """

    # Store original methods
    original_load_data = main_window.load_data
    original_save_dicom = main_window._save_dicom_files

    def load_data_with_progress(data_path):
        """Replace load_data with progress-tracked version."""
        ProgressDialogExamples.load_data_with_progress(main_window, data_path)

    def save_dicom_with_progress(save_dir: str):
        """Replace _save_dicom_files with progress-tracked version."""
        ProgressDialogExamples.export_dicom_with_progress(main_window, save_dir)

    # Replace methods
    main_window.load_data = load_data_with_progress
    main_window._save_dicom_files = save_dicom_with_progress

    # Add method to show simple progress example
    main_window.show_progress_example = lambda: ProgressDialogExamples.simple_progress_example(main_window.root)

    # Add ProgressManager examples
    main_window.show_multi_stage_example = lambda: ProgressDialogExamples.multi_stage_progress_example(main_window)
    main_window.show_progress_awareness_example = lambda: ProgressDialogExamples.progress_state_awareness_example(main_window)
    main_window.show_realistic_loading_example = lambda data_path: ProgressDialogExamples.realistic_pinnacle_loading_example(main_window, data_path)

    return main_window


class ProgressManagerUsageGuide:
    """
    Usage guide and best practices for ProgressManager and ProgressState.

    This class provides documentation and examples of how to properly use
    the new progress management system in the Pinnacle IO UI.
    """

    @staticmethod
    def print_usage_guide():
        """Print comprehensive usage guide for ProgressManager."""
        guide = """
=== ProgressManager Usage Guide ===

The ProgressManager system provides centralized progress tracking across multiple
UI components and data loading stages. Here's how to use it effectively:

## Basic Usage Pattern

1. **Start Operation**: Initialize progress manager for your operation
   ```python
   progress_manager = main_window.start_progress_operation(
       operation_id="my_operation",
       title="My Operation Title",
       cancelable=True
   )
   ```

2. **Show Dialog**: Display the progress dialog
   ```python
   progress_manager.show("Starting operation...", max_progress=100)
   ```

3. **Register Stages**: Define the stages of your operation
   ```python
   progress_manager.register_stage("stage1", weight=40, name="Data Loading")
   progress_manager.register_stage("stage2", weight=35, name="Processing")
   progress_manager.register_stage("stage3", weight=25, name="UI Updates")
   ```

4. **Execute Stages**: Process each stage with progress updates
   ```python
   progress_manager.set_current_stage("stage1")
   for i in range(steps):
       if progress_manager.is_cancelled_flag():
           break

       stage_progress = ((i + 1) / steps) * 100
       if not progress_manager.update_progress(stage_progress, f"Step {i+1}..."):
           break  # User cancelled

   progress_manager.complete_stage("stage1")
   ```

5. **Complete Operation**: Finish the operation
   ```python
   if not progress_manager.is_cancelled_flag():
       progress_manager.complete_operation(success=True, final_message="Completed!")
   ```

## Multi-Component Coordination

When multiple components need to participate in the same operation:

1. **Check for Active Operations**:
   ```python
   existing_manager = main_window.get_progress_manager()
   if existing_manager and existing_manager.is_active():
       # Join existing operation
       existing_manager.register_stage("my_stage", weight=15)
       # ... do work within existing operation
   else:
       # Start new operation
       progress_manager = main_window.start_progress_operation(...)
   ```

2. **Stage Weight Management**:
   - Weights are automatically normalized to sum to 100%
   - Use weights that reflect the relative time/complexity of each stage
   - Default weights: [40, 35, 25] for MainWindow, RightSidebar, Panels

3. **Cancellation Handling**:
   - Always check `progress_manager.is_cancelled_flag()` in loops
   - Check return value of `update_progress()` (returns False if cancelled)
   - Complete operation with appropriate status on cancellation

## Development Mode Features

When development_mode=True (automatically enabled in ProgressManager):
- Detailed timing logs with step-by-step analysis
- Dialog remains open after completion for review
- Copy and save timing report functionality
- Console output with timing information

## Best Practices

1. **Thread Safety**: Always run long operations in background threads
2. **Regular Updates**: Update progress frequently to keep UI responsive
3. **Meaningful Messages**: Provide clear, specific status messages
4. **Error Handling**: Always use try/except and complete_operation() in finally
5. **Stage Completion**: Always call complete_stage() when stage finishes
6. **Operation IDs**: Use unique, descriptive operation IDs
7. **Weight Balance**: Ensure stage weights reflect actual work distribution

## Integration with Existing Code

The ProgressManager integrates seamlessly with existing ProgressDialog:
- Same cancellation mechanisms
- Same timing and logging features
- Same development mode enhancements
- Additional multi-stage coordination capabilities

For complex operations like Pinnacle data loading, use ProgressManager.
For simple single-stage operations, ProgressDialog is still appropriate.
"""
        print(guide)

    @staticmethod
    def demonstrate_stage_coordination():
        """Demonstrate how stages coordinate in a realistic scenario."""
        print("\n=== Stage Coordination Example ===")
        print("This shows how the three-stage Pinnacle loading would work:")
        print("")
        print("Stage 1 (40%): MainWindow Operations")
        print("  ├─ Load institution (8% of total)")
        print("  ├─ Load patients (16% of total)")
        print("  └─ Initial setup (16% of total)")
        print("")
        print("Stage 2 (35%): Plan Data Loading")
        print("  ├─ Load trials (8.75% of total)")
        print("  ├─ Load ROIs (8.75% of total)")
        print("  ├─ Load points (8.75% of total)")
        print("  └─ Load CT images (8.75% of total)")
        print("")
        print("Stage 3 (25%): UI Component Updates")
        print("  ├─ CT Viewer (5% of total)")
        print("  ├─ ROI Panel (5% of total)")
        print("  ├─ POI Panel (5% of total)")
        print("  ├─ Beams Panel (5% of total)")
        print("  └─ Dose Panel (5% of total)")
        print("")
        print("Total: 100% with smooth progress across all stages")

# Usage demonstration
if __name__ == "__main__":
    # Print usage guide when module is run directly
    ProgressManagerUsageGuide.print_usage_guide()
    ProgressManagerUsageGuide.demonstrate_stage_coordination()