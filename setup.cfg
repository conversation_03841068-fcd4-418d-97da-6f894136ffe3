[metadata]
name = pinnacle_io
version = 0.1.0
description = A Python library for reading and writing Pinnacle radiotherapy treatment planning files
long_description = file: README.md
long_description_content_type = text/markdown
author = pinnacle_io Contributors
license = MIT
license_file = LICENSE
classifiers =
    Development Status :: 3 - Alpha
    Intended Audience :: Healthcare Industry
    Intended Audience :: Science/Research
    License :: OSI Approved :: MIT License
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Topic :: Scientific/Engineering :: Medical Science Apps.

[options]
packages = find:
python_requires = >=3.9
install_requires =
    sqlalchemy>=2.0.0
    pydantic>=2.0.0
    numpy>=1.21.0

[options.extras_require]
ui =
    PyQt6>=6.0.0
dev =
    pytest>=7.0.0
    pytest-cov>=4.0.0
    black>=23.0.0
    isort>=5.0.0
    mypy>=1.0.0

[options.packages.find]
exclude =
    tests
    examples
