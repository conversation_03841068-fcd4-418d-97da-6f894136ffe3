"""
Reader for Pinnacle Plan files.
"""

import os
from typing import List, Any
from pinnacle_io.models import Plan, Patient
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.patient_setup_reader import PatientSetupReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class PlanReader(BasePinnacleReader):
    """
    Reader for Pinnacle Plan files.
    """

    @staticmethod
    def read_from_ids(institution_id: int, patient_id: int, mount_id: int = 0, file_service: Any = None) -> List[Plan]:
        """
        Read Pinnacle Plan data using ID-based loading and create Plan models.

        Args:
            institution_id: Institution ID number
            patient_id: Patient ID number
            mount_id: Mount ID number (default: 0)
            file_service: File service object with open_file method

        Returns:
            List of Plan models populated with data from the Patient file

        Usage:
            plans = PlanReader.read_from_ids(1, 1, 0, file_service=file_service)
        """
        # Construct standard Pinnacle path format
        patient_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}"
        # Delegate to path-based method
        return PlanReader.read_from_path(patient_path, file_service)

    @staticmethod
    def read_from_path(patient_path: str, file_service: Any = None) -> List[Plan]:
        """
        Read a Pinnacle Patient file and return the Plan models.
        The patient setup information is also processed and attached to the Plan models.

        Args:
            patient_path: Path to the Pinnacle Patient file or directory
            file_service: File service object with open_file method

        Returns:
            List of Plan models populated with data from the file

        Usage:
            plans = PlanReader.read_from_path("/path/to/Patient_0/Patient")
            plans = PlanReader.read_from_path("/path/to/Patient_0", file_service=file_service)
        """
        # Resolve file path and name
        file_path, file_name = PlanReader._resolve_file_path(patient_path, "Patient")

        # Read file content using base class utility
        content_lines = PlanReader._read_file_lines(file_path, file_name, file_service)
        plans = PlanReader.parse(content_lines)

        # Try to read patient setup for each plan - use the same file service or create one if needed
        service = PlanReader._get_file_service(file_service, file_path)
        for i, plan in enumerate(plans):
            try:
                plan_dir = os.path.join(file_path, f"Plan_{i}")
                patient_setup = PatientSetupReader.read_from_path(plan_dir, file_service=service)
                plan.set_patient_position(patient_setup)  # type: ignore
            except FileNotFoundError:
                # Patient setup is optional
                pass

        return plans

    @staticmethod
    def read(patient_path: str, file_service: Any = None) -> List[Plan]:
        """
        Read a Pinnacle Patient file and return the Plan models.

        DEPRECATED: Use read_from_path() instead. This method is kept for backward compatibility.

        Args:
            patient_path: Path to the Pinnacle Patient file or directory
            file_service: File service object with open_file method

        Returns:
            List of Plan models populated with data from the file
        """
        return PlanReader.read_from_path(patient_path, file_service)

    @staticmethod
    def parse(content_lines: list[str]) -> List[Plan]:
        """
        Parse a Pinnacle Patient file content and create Plan models.
        The patient setup information is NOT processed by this method.

        Args:
            content_lines: Lines from a Pinnacle Patient file

        Returns:
            List of Plan models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        patient = Patient(**data)
        return patient.plan_list
