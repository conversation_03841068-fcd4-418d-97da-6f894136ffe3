"""
Utility functions for parsing Pinnacle directory structures.
"""

import re
from pathlib import Path
from typing import Optional, Tuple


class PinnaclePathInfo:
    """Information extracted from a Pinnacle directory path."""

    def __init__(self,
                 archive_root: Path,
                 institution_id: Optional[int] = None,
                 patient_id: Optional[int] = None,
                 plan_id: Optional[int] = None):
        """
        Initialize Pinnacle path information.

        Args:
            archive_root: Root directory containing Institution folders
            institution_id: Institution ID (extracted from Institution_#### folder)
            patient_id: Patient ID (extracted from Patient_#### folder)
            plan_id: Plan ID (extracted from Plan_## folder)
        """
        self.archive_root = archive_root
        self.institution_id = institution_id
        self.patient_id = patient_id
        self.plan_id = plan_id

    def __str__(self):
        """String representation of path info."""
        parts = [f"archive_root={self.archive_root}"]
        if self.institution_id is not None:
            parts.append(f"institution={self.institution_id}")
        if self.patient_id is not None:
            parts.append(f"patient={self.patient_id}")
        if self.plan_id is not None:
            parts.append(f"plan={self.plan_id}")
        return f"PinnaclePathInfo({', '.join(parts)})"


def parse_pinnacle_path(path: str) -> PinnaclePathInfo:
    """
    Parse a Pinnacle directory path to extract archive root and IDs.

    Pinnacle data structure:
    /path/to/archive_root/Institution_####/Mount_0/Patient_####/Plan_##

    Args:
        path: Directory path (can be any level in the hierarchy)

    Returns:
        PinnaclePathInfo with archive root and extracted IDs

    Examples:
        >>> info = parse_pinnacle_path("/data/Institution_1/Mount_0/Patient_2/Plan_3")
        >>> print(info.archive_root)  # /data
        >>> print(info.institution_id)  # 1
        >>> print(info.patient_id)  # 2
        >>> print(info.plan_id)  # 3
    """
    path_obj = Path(path).resolve()
    parts = path_obj.parts

    # Find Institution folder in the path
    institution_idx = None
    institution_id = None

    for i, part in enumerate(parts):
        match = re.match(r'^Institution_(\d+)$', part)
        if match:
            institution_idx = i
            institution_id = int(match.group(1))
            break

    if institution_idx is None:
        # No Institution folder found, assume the given path is the archive root
        return PinnaclePathInfo(archive_root=path_obj)

    # Archive root is the parent of the Institution folder
    archive_root = Path(*parts[:institution_idx])

    # Look for Patient and Plan folders after Institution
    patient_id = None
    plan_id = None

    remaining_parts = parts[institution_idx + 1:]

    for part in remaining_parts:
        # Look for Patient_#### pattern
        patient_match = re.match(r'^Patient_(\d+)$', part)
        if patient_match:
            patient_id = int(patient_match.group(1))
            continue

        # Look for Plan_## pattern
        plan_match = re.match(r'^Plan_(\d+)$', part)
        if plan_match:
            plan_id = int(plan_match.group(1))
            continue

    return PinnaclePathInfo(
        archive_root=archive_root,
        institution_id=institution_id,
        patient_id=patient_id,
        plan_id=plan_id
    )


def is_pinnacle_archive_root(path: str) -> bool:
    """
    Check if the given path appears to be a Pinnacle archive root.

    Args:
        path: Directory path to check

    Returns:
        True if the path contains Institution_#### subdirectories
    """
    path_obj = Path(path)

    if not path_obj.is_dir():
        return False

    # Look for Institution_#### directories
    for item in path_obj.iterdir():
        if item.is_dir() and re.match(r'^Institution_\d+$', item.name):
            return True

    return False