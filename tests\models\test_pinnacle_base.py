"""
Tests for the base SQLAlchemy models.
"""

from datetime import datetime, timezone

from sqlalchemy.orm import Session

from pinnacle_io.models.types import JsonList, VoxelSize, Coordinate, Dimension
from tests.conftest import _TestModel, _TestVersionedModel


def test_pinnacle_base_initialization():
    """Test basic initialization of PinnacleBase model."""
    # Create a new instance
    model = _TestModel(name="Test Model", description="Test Description")

    # Check attributesf
    assert model.name == "Test Model"
    assert model.description == "Test Description"
    assert model.id is None  # Not persisted yet
    assert model.created_at is not None
    assert model.updated_at is not None
    assert model.created_at.tzinfo is not None  # Timezone-aware
    assert model.updated_at.tzinfo is not None  # Timezone-aware
    assert model.created_at.tzinfo == timezone.utc  # UTC timezone
    assert model.updated_at.tzinfo == timezone.utc  # UTC timezone


def test_pinnacle_base_save_to_database(db_session: Session):
    """Test saving PinnacleBase model to database."""
    # Create and save model
    model = _TestModel(name="Database Test")
    db_session.add(model)
    db_session.commit()

    # Verify ID was assigned
    assert model.id is not None

    # Retrieve from database
    db_model = db_session.query(_TestModel).filter_by(id=model.id).first()
    assert db_model is not None
    assert db_model.name == "Database Test"


def test_json_list_type():
    """Test the JsonList custom type."""
    # Test with list
    test_list = [1, 2, 3, "test"]
    json_list = JsonList()

    # Test process_bind_param
    json_str = json_list.process_bind_param(test_list, None)
    assert isinstance(json_str, str)

    # Test process_result_value
    result = json_list.process_result_value(json_str, None)
    assert result == test_list

    # Test with None
    assert json_list.process_bind_param(None, None) is None
    assert json_list.process_result_value(None, None) is None


def test_voxel_size_initialization():
    """Test VoxelSize class initialization and methods."""
    # Test initialization with values
    vs = VoxelSize(1.0, 2.0, 3.0)
    assert vs.x == 1.0
    assert vs.y == 2.0
    assert vs.z == 3.0

    # Test volume calculation
    assert vs.volume() == 6.0

    # Test validation (negative values not allowed)
    try:
        VoxelSize(-1.0, 1.0, 1.0)
        assert False, "Should raise ValueError for negative values"
    except ValueError:
        pass


def test_dimension_initialization():
    """Test Dimension class initialization and methods."""
    # Test initialization with values
    dim = Dimension(10, 20, 30)
    assert dim.x == 10
    assert dim.y == 20
    assert dim.z == 30

    # Test num_voxels calculation
    assert dim.num_voxels() == 10 * 20 * 30

    # Test validation (non-positive values not allowed)
    try:
        Dimension(0, 1, 1)
        assert False, "Should raise ValueError for non-positive values"
    except ValueError:
        pass


def test_coordinate_operations():
    """Test Coordinate class operations."""
    c1 = Coordinate(1.0, 2.0, 3.0)
    c2 = Coordinate(4.0, 5.0, 6.0)

    # Test distance calculation
    distance = c1.distance_to(c2)
    expected_distance = (3.0**2 + 3.0**2 + 3.0**2) ** 0.5
    assert abs(distance - expected_distance) < 1e-10

    # Test addition
    c3 = c1 + c2
    assert c3.x == 5.0
    assert c3.y == 7.0
    assert c3.z == 9.0

    # Test scalar multiplication
    c4 = c1 * 2
    assert c4.x == 2.0
    assert c4.y == 4.0
    assert c4.z == 6.0


def test_pinnacle_base_to_dict_basic():
    """Test basic to_dict functionality with regular columns."""
    model = _TestModel(name="Test Model", description="Test Description")
    
    result = model.to_dict(include_relationships=False)
    
    # Check that result is a dictionary
    assert isinstance(result, dict)
    
    # Check that database column names are used as keys
    assert "Name" in result
    assert "Description" in result
    assert "ID" in result
    assert "CreatedAt" in result
    assert "UpdatedAt" in result
    
    # Check values
    assert result["Name"] == "Test Model"
    assert result["Description"] == "Test Description"
    assert result["ID"] is None  # Not persisted yet
    assert isinstance(result["CreatedAt"], datetime)
    assert isinstance(result["UpdatedAt"], datetime)


def test_pinnacle_base_to_dict_with_none_values():
    """Test to_dict with None values."""
    model = _TestModel(name="Test Model", description=None)
    
    result = model.to_dict(include_relationships=False)
    
    assert result["Name"] == "Test Model"
    assert result["Description"] is None


def test_pinnacle_base_to_dict_with_relationships():
    """Test to_dict with relationship fields."""
    # Create parent model with relationships
    parent = _TestModel(name="Parent Model")
    
    # Create child models
    child1 = _TestModel(name="Child 1")
    child2 = _TestModel(name="Child 2")
    
    # Set up relationships (assuming _TestModel has a children relationship)
    if hasattr(parent, 'children'):
        parent.children = [child1, child2]
    
    result = parent.to_dict(include_relationships=True)
    
    # Check that relationship fields are converted to PascalCase with List suffix
    if hasattr(parent, 'children'):
        assert "ChildrenList" in result
        assert isinstance(result["ChildrenList"], list)
        assert len(result["ChildrenList"]) == 2
        
        # Check that child objects are converted to dicts
        for child_dict in result["ChildrenList"]:
            assert isinstance(child_dict, dict)
            assert "Name" in child_dict
            assert "ID" in child_dict


def test_pinnacle_base_to_dict_exclude_relationships():
    """Test to_dict with relationships excluded."""
    parent = _TestModel(name="Parent Model")
    child = _TestModel(name="Child")
    
    if hasattr(parent, 'children'):
        parent.children = [child]
    
    result = parent.to_dict(include_relationships=False)
    
    # Relationship fields should not be included
    assert "ChildrenList" not in result
    
    # Regular fields should still be present
    assert "Name" in result
    assert result["Name"] == "Parent Model"


def test_pinnacle_base_to_dict_empty_relationships():
    """Test to_dict with empty relationship lists."""
    model = _TestModel(name="Test Model")
    
    if hasattr(model, 'children'):
        model.children = []
    
    result = model.to_dict(include_relationships=True)
    
    if hasattr(model, 'children'):
        assert "ChildrenList" in result
        assert result["ChildrenList"] == []


def test_pinnacle_base_to_dict_none_relationships():
    """Test to_dict with None relationship values."""
    model = _TestModel(name="Test Model")
    
    if hasattr(model, 'parent'):
        model.parent = None
    
    result = model.to_dict(include_relationships=True)
    
    if hasattr(model, 'parent'):
        assert "ParentList" in result
        assert result["ParentList"] is None


def test_pinnacle_base_to_dict_one_to_one_relationship():
    """Test to_dict with one-to-one relationships."""
    model = _TestModel(name="Test Model")
    related = _TestModel(name="Related Model")
    
    # Assuming there's a one-to-one relationship field
    if hasattr(model, 'related_model'):
        model.related_model = related
    
    result = model.to_dict(include_relationships=True)
    
    if hasattr(model, 'related_model'):
        assert "RelatedModelList" in result
        assert isinstance(result["RelatedModelList"], dict)
        assert result["RelatedModelList"]["Name"] == "Related Model"


def test_pinnacle_base_to_dict_versioned_model():
    """Test to_dict with VersionedBase model."""
    model = _TestVersionedModel(
        name="Versioned Model",
        write_version=1,
        create_version=1,
        login_name="test_user"
    )
    
    result = model.to_dict(include_relationships=False)
    
    # Check base fields
    assert result["Name"] == "Versioned Model"
    
    # Check versioned fields (assuming they map to database columns)
    assert "WriteVersion" in result # or "write_version" in result
    assert "CreateVersion" in result # or "create_version" in result
    assert "VersionLoginName" in result # or "login_name" in result


def test_pinnacle_base_to_dict_different_data_types():
    """Test to_dict with various data types."""
    # Create model with different field types
    model = _TestModel(name="Type Test")
    
    # Set various field types if they exist on the model
    if hasattr(model, 'integer_field'):
        model.integer_field = 42
    if hasattr(model, 'float_field'):
        model.float_field = 3.14
    if hasattr(model, 'boolean_field'):
        model.boolean_field = True
    if hasattr(model, 'datetime_field'):
        model.datetime_field = datetime.now(timezone.utc)
    
    result = model.to_dict(include_relationships=False)
    
    # Check that different types are preserved
    assert isinstance(result["Name"], str)
    assert isinstance(result["CreatedAt"], datetime)
    assert isinstance(result["UpdatedAt"], datetime)
    
    if hasattr(model, 'integer_field'):
        assert isinstance(result.get("IntegerField", result.get("integer_field")), int)
    if hasattr(model, 'float_field'):
        assert isinstance(result.get("FloatField", result.get("float_field")), float)
    if hasattr(model, 'boolean_field'):
        assert isinstance(result.get("BooleanField", result.get("boolean_field")), bool)


def test_pinnacle_base_to_dict_recursive_prevention():
    """Test that to_dict prevents infinite recursion in relationships."""
    parent = _TestModel(name="Parent")
    child = _TestModel(name="Child")
    
    # Set up bidirectional relationship if supported
    if hasattr(parent, 'children') and hasattr(child, 'parent'):
        parent.children = [child]
        child.parent = parent
    
    # This should not cause infinite recursion
    result = parent.to_dict(include_relationships=True)
    
    assert isinstance(result, dict)
    assert result["Name"] == "Parent"
    
    if hasattr(parent, 'children'):
        assert "ChildrenList" in result
        child_dict = result["ChildrenList"][0]
        assert child_dict["Name"] == "Child"
        # Child's parent relationship should not be included (include_relationships=False for nested)
        assert "ParentList" not in child_dict
