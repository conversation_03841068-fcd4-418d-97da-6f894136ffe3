"""
SQLAlchemy model for Pinnacle Beam data.

This module provides the Beam data model for representing treatment beams in Pinnacle,
including all beam-specific parameters, relationships to control points, and associated
beam modifiers like wedges and MLCs.
"""

from __future__ import annotations
from typing import TYPE_CHECKING, TypeVar, Any, override

from sqlalchemy import Column, String, Integer, ForeignKey, Float
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.control_point import ControlPoint
    from pinnacle_io.models.trial import Trial
    from pinnacle_io.models.dose import Dose, MaxDosePoint
    from pinnacle_io.models.compensator import Compensator
    from pinnacle_io.models.dose_engine import DoseEngine
    from pinnacle_io.models.monitor_unit_info import MonitorUnitInfo
    from pinnacle_io.models.cp_manager import CPManager

# Type variable for the Beam class to support better type hints
B = TypeVar("B", bound="Beam")


# --- DisplayList model ---
class DisplayList(PinnacleBase):
    __tablename__ = "DisplayList"

    name: Mapped[str | None] = Column("Name", String, nullable=True)
    color: Mapped[str | None] = Column("Color", String, nullable=True)
    dash_color_2d: Mapped[str | None] = Column("DashColor2d", String, nullable=True)
    display_transparently: Mapped[int | None] = Column(
        "DisplayTransparently", Integer, nullable=True
    )
    is_2d_dash_on: Mapped[int | None] = Column("Is2dDashOn", Integer, nullable=True)
    on_off_2d: Mapped[str | None] = Column("OnOff2d", String, nullable=True)
    wire_on_off: Mapped[str | None] = Column("WireOnOff", String, nullable=True)
    path_on_off: Mapped[str | None] = Column("PathOnOff", String, nullable=True)
    solid_on_off: Mapped[str | None] = Column("SolidOnOff", String, nullable=True)
    wash_on_off: Mapped[str | None] = Column("WashOnOff", String, nullable=True)

    beam_id: Mapped[int | None] = Column(
        "BeamID", Integer, ForeignKey("Beam.ID"), nullable=True
    )
    beam: Mapped["Beam | None"] = relationship(
        "Beam",
        back_populates="display_list",
        lazy="joined",
    )


class Beam(PinnacleBase):
    """
    Model representing a treatment beam in Pinnacle.

    This class stores all beam-specific information including machine parameters,
    control points, and beam modifiers needed for treatment planning and delivery.
    It serves as the central model for beam data in the Pinnacle I/O system.

    Attributes:
        id (int): Primary key
        name (str): Name of the beam
        beam_number (int): Beam number in the treatment plan
        isocenter_name (str): Name of the isocenter used by this beam
        modality (str): Beam modality (e.g., "PHOTON", "ELECTRON")
        machine_energy_name (str): Name of the machine energy for this beam
        monitor_units (float): Number of monitor units for this beam

    Relationships:
        trial (Trial): Parent trial that this beam belongs to
        control_point_list (list[ControlPoint]): List of control points defining this beam
        cp_manager (CPManager): Manager for control point data
        dose (Dose): Dose information for this beam
        dose_engine (DoseEngine): Dose calculation engine settings
        max_dose_point (MaxDosePoint): Point of maximum dose
        monitor_unit_info (MonitorUnitInfo): Monitor unit calculation details
        compensator (Compensator): Associated compensator if any
    """

    __tablename__: str = "Beam"

    # Identification and basic info
    name: Mapped[str | None] = Column("Name", String, nullable=True)
    beam_number: Mapped[int | None] = Column("BeamNumber", Integer, nullable=True)
    isocenter_name: Mapped[str | None] = Column("IsocenterName", String, nullable=True)

    # Prescription and dose
    prescription_name: Mapped[str | None] = Column(
        "PrescriptionName", String, nullable=True
    )
    use_poi_for_prescription_point: Mapped[int | None] = Column(
        "UsePoiForPrescriptionPoint", Integer, nullable=True
    )
    prescription_point_name: Mapped[str | None] = Column(
        "PrescriptionPointName", String, nullable=True
    )
    prescription_point_depth: Mapped[float | None] = Column(
        "PrescriptionPointDepth", Float, nullable=True
    )
    prescription_point_x_offset: Mapped[float | None] = Column(
        "PrescriptionPointXOffset", Float, nullable=True
    )
    prescription_point_y_offset: Mapped[float | None] = Column(
        "PrescriptionPointYOffset", Float, nullable=True
    )
    specify_dose_per_mu_at_prescription_point: Mapped[int | None] = Column(
        "SpecifyDosePerMuAtPrescriptionPoint", Integer, nullable=True
    )
    dose_per_mu_at_prescription_point: Mapped[float | None] = Column(
        "DosePerMuAtPrescriptionPoint", Float, nullable=True
    )
    machine_name_and_version: Mapped[str | None] = Column(
        "MachineNameAndVersion", String, nullable=True
    )
    modality: Mapped[str | None] = Column("Modality", String, nullable=True)
    machine_energy_name: Mapped[str | None] = Column(
        "MachineEnergyName", String, nullable=True
    )
    desired_localizer_name: Mapped[str | None] = Column(
        "DesiredLocalizerName", String, nullable=True
    )
    actual_localizer_name: Mapped[str | None] = Column(
        "ActualLocalizerName", String, nullable=True
    )
    display_laser_motion: Mapped[str | None] = Column(
        "DisplayLaserMotion", String, nullable=True
    )
    set_beam_type: Mapped[str | None] = Column("SetBeamType", String, nullable=True)
    prev_beam_type: Mapped[str | None] = Column("PrevBeamType", String, nullable=True)
    computation_version: Mapped[str | None] = Column(
        "ComputationVersion", String, nullable=True
    )
    extend_past_target: Mapped[float | None] = Column(
        "ExtendPastTarget", Float, nullable=True
    )
    extend_block_plane_past_target: Mapped[float | None] = Column(
        "ExtendBlockPlanePastTarget", Float, nullable=True
    )
    extend_arc_past_target: Mapped[float | None] = Column(
        "ExtendArcPastTarget", Float, nullable=True
    )
    bev_rotation_angle: Mapped[float | None] = Column(
        "BevRotationAngle", Float, nullable=True
    )
    bev_is_parallel: Mapped[int | None] = Column(
        "BevIsParallel", Integer, nullable=True
    )
    rotation_indicator_offset: Mapped[float | None] = Column(
        "RotationIndicatorOffset", Float, nullable=True
    )
    imrt_filter: Mapped[str | None] = Column("ImrtFilter", String, nullable=True)
    imrt_wedge: Mapped[str | None] = Column("ImrtWedge", String, nullable=True)
    imrt_direction: Mapped[str | None] = Column("ImrtDirection", String, nullable=True)
    imrt_parameter_type: Mapped[str | None] = Column(
        "ImrtParameterType", String, nullable=True
    )
    prev_imrt_parameter_type: Mapped[str | None] = Column(
        "PrevImrtParameterType", String, nullable=True
    )
    philips_mlc_treatment: Mapped[str | None] = Column(
        "PhilipsMlcTreatment", String, nullable=True
    )
    philips_mlc_beam_number: Mapped[str | None] = Column(
        "PhilipsMlcBeamNumber", String, nullable=True
    )
    toshiba_mlc_plan_number: Mapped[str | None] = Column(
        "ToshibaMlcPlanNumber", String, nullable=True
    )
    toshiba_mlc_beam_number_string: Mapped[str | None] = Column(
        "ToshibaMlcBeamNumberString", String, nullable=True
    )
    use_mlc: Mapped[int | None] = Column("UseMlc", Integer, nullable=True)
    clip_mlc_display: Mapped[int | None] = Column(
        "ClipMlcDisplay", Integer, nullable=True
    )
    solid_mlc_display: Mapped[int | None] = Column(
        "SolidMlcDisplay", Integer, nullable=True
    )
    dynamic_blocks: Mapped[int | None] = Column("DynamicBlocks", Integer, nullable=True)
    display_2d: Mapped[int | None] = Column("Display2d", Integer, nullable=True)
    display_3d: Mapped[int | None] = Column("Display3d", Integer, nullable=True)
    circular_field_diameter: Mapped[float | None] = Column(
        "CircularFieldDiameter", Float, nullable=True
    )
    electron_applicator_name: Mapped[str | None] = Column(
        "ElectronApplicatorName", String, nullable=True
    )
    ssd: Mapped[float | None] = Column("Ssd", Float, nullable=True)
    avg_ssd: Mapped[float | None] = Column("AvgSsd", Float, nullable=True)
    ssd_valid: Mapped[int | None] = Column("SsdValid", Integer, nullable=True)
    left_auto_surround_margin: Mapped[float | None] = Column(
        "LeftAutoSurroundMargin", Float, nullable=True
    )
    right_auto_surround_margin: Mapped[float | None] = Column(
        "RightAutoSurroundMargin", Float, nullable=True
    )
    top_auto_surround_margin: Mapped[float | None] = Column(
        "TopAutoSurroundMargin", Float, nullable=True
    )
    bottom_auto_surround_margin: Mapped[float | None] = Column(
        "BottomAutoSurroundMargin", Float, nullable=True
    )
    auto_surround: Mapped[int | None] = Column("AutoSurround", Integer, nullable=True)
    blocking_mask_pixel_size: Mapped[float | None] = Column(
        "BlockingMaskPixelSize", Float, nullable=True
    )
    blocking_mask_cutoff_area: Mapped[float | None] = Column(
        "BlockingMaskCutoffArea", Float, nullable=True
    )
    block_and_tray_factor: Mapped[float | None] = Column(
        "BlockAndTrayFactor", Float, nullable=True
    )
    tray_number: Mapped[str | None] = Column("TrayNumber", String, nullable=True)
    block_export_name: Mapped[str | None] = Column(
        "BlockExportName", String, nullable=True
    )
    block_cutter_format: Mapped[str | None] = Column(
        "BlockCutterFormat", String, nullable=True
    )
    block_jaw_overlap: Mapped[int | None] = Column(
        "BlockJawOverlap", Integer, nullable=True
    )
    tray_factor: Mapped[float | None] = Column("TrayFactor", Float, nullable=True)
    compensator_scale_factor: Mapped[float | None] = Column(
        "CompensatorScaleFactor", Float, nullable=True
    )
    degrees_between_subbeams_for_dose_calc: Mapped[float | None] = Column(
        "DegreesBetweenSubbeamsForDoseCalc", Float, nullable=True
    )
    irreg_prescription_point_name: Mapped[str | None] = Column(
        "IrregPrescriptionPointName", String, nullable=True
    )
    irreg_specify_monitor_units: Mapped[int | None] = Column(
        "IrregSpecifyMonitorUnits", Integer, nullable=True
    )
    irreg_point_prescription_dose: Mapped[float | None] = Column(
        "IrregPointPrescriptionDose", Float, nullable=True
    )
    irreg_point_monitor_units: Mapped[float | None] = Column(
        "IrregPointMonitorUnits", Float, nullable=True
    )
    irreg_point_actual_monitor_units: Mapped[float | None] = Column(
        "IrregPointActualMonitorUnits", Float, nullable=True
    )
    irreg_point_prescribe_overall: Mapped[int | None] = Column(
        "IrregPointPrescribeOverall", Integer, nullable=True
    )
    irreg_point_number_of_fractions: Mapped[int | None] = Column(
        "IrregPointNumberOfFractions", Integer, nullable=True
    )
    photon_model_description: Mapped[str | None] = Column(
        "PhotonModelDescription", String, nullable=True
    )
    avg_tar: Mapped[float | None] = Column("AvgTar", Float, nullable=True)
    stereo_dose_per_mu_lookup: Mapped[int | None] = Column(
        "StereoDosePerMuLookup", Integer, nullable=True
    )
    stereo_dose_di_value: Mapped[float | None] = Column(
        "StereoDoseDiValue", Float, nullable=True
    )
    blocks_are_locked: Mapped[int | None] = Column(
        "BlocksAreLocked", Integer, nullable=True
    )
    is_proton_beam_locked: Mapped[int | None] = Column(
        "IsProtonBeamLocked", Integer, nullable=True
    )
    rely_on_bolus_names: Mapped[str | None] = Column(
        "RelyOnBolusNames", String, nullable=True
    )
    dose_volume: Mapped[str | None] = Column("DoseVolume", String, nullable=True)
    dose_var_volume: Mapped[str | None] = Column("DoseVarVolume", String, nullable=True)
    weight: Mapped[float | None] = Column("Weight", Float, nullable=True)
    is_weight_locked: Mapped[int | None] = Column(
        "IsWeightLocked", Integer, nullable=True
    )
    monitor_units_valid: Mapped[int | None] = Column(
        "MonitorUnitsValid", Integer, nullable=True
    )
    monitor_units_approximate: Mapped[int | None] = Column(
        "MonitorUnitsApproximate", Integer, nullable=True
    )
    field_id: Mapped[str | None] = Column("FieldId", String, nullable=True)
    speed_up_collimator: Mapped[int | None] = Column(
        "SpeedUpCollimator", Integer, nullable=True
    )
    speed_up_virt_flouro: Mapped[int | None] = Column(
        "SpeedUpVirtFlouro", Integer, nullable=True
    )
    display_max_leaf_motion: Mapped[int | None] = Column(
        "DisplayMaxLeafMotion", Integer, nullable=True
    )
    beam_was_split: Mapped[int | None] = Column("BeamWasSplit", Integer, nullable=True)
    dose_rate: Mapped[float | None] = Column("DoseRate", Float, nullable=True)
    is_copy_oppose_allowed: Mapped[int | None] = Column(
        "IsCopyOpposeAllowed", Integer, nullable=True
    )
    vertical_jaw_sync: Mapped[int | None] = Column(
        "VerticalJawSync", Integer, nullable=True
    )
    scenario_dose_volume0: Mapped[str | None] = Column(
        "ScenarioDoseVolume0", String, nullable=True
    )
    scenario_dose_volume1: Mapped[str | None] = Column(
        "ScenarioDoseVolume1", String, nullable=True
    )
    scenario_dose_volume2: Mapped[str | None] = Column(
        "ScenarioDoseVolume2", String, nullable=True
    )
    scenario_dose_volume3: Mapped[str | None] = Column(
        "ScenarioDoseVolume3", String, nullable=True
    )
    scenario_dose_volume4: Mapped[str | None] = Column(
        "ScenarioDoseVolume4", String, nullable=True
    )
    scenario_dose_volume5: Mapped[str | None] = Column(
        "ScenarioDoseVolume5", String, nullable=True
    )
    scenario_dose_volume6: Mapped[str | None] = Column(
        "ScenarioDoseVolume6", String, nullable=True
    )
    scenario_dose_volume7: Mapped[str | None] = Column(
        "ScenarioDoseVolume7", String, nullable=True
    )
    deserialization_completed: Mapped[int | None] = Column(
        "DeserializationCompleted", Integer, nullable=True
    )

    # Derived fields (not in the Pinnacle plan.Trial file but still useful)
    _monitor_units: Mapped[float | None] = Column(
        "MonitorUnits", Float, nullable=True, default=0.0
    )

    # Relationships
    trial_id: Mapped[int | None] = Column("TrialID", Integer, ForeignKey("Trial.ID"))
    trial: Mapped["Trial | None"] = relationship(
        "Trial",
        back_populates="beam_list",
        lazy="joined",  # Optimize loading as trial info is frequently needed with beam
    )

    # Control points are managed by CPManager - access via property below
    display_list: Mapped[list["DisplayList"]] = relationship(
        "DisplayList",
        back_populates="beam",
        cascade="all, delete-orphan",
        lazy="selectin",  # Optimize loading for collections that are frequently accessed
    )

    # One-to-one relationships
    compensator: Mapped["Compensator | None"] = relationship(
        "Compensator",
        back_populates="beam",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="select",  # Load on demand as compensators are optional
    )
    cp_manager: Mapped["CPManager | None"] = relationship(
        "CPManager",
        back_populates="beam",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="selectin",  # Optimize loading as CP manager is frequently accessed
    )
    dose: Mapped["Dose | None"] = relationship(
        "Dose",
        back_populates="beam",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="select",  # Load on demand as dose data can be large
    )
    dose_engine: Mapped["DoseEngine | None"] = relationship(
        "DoseEngine",
        back_populates="beam",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="select",  # Load on demand as dose engine settings are optional
    )
    max_dose_point: Mapped["MaxDosePoint | None"] = relationship(
        "MaxDosePoint",
        back_populates="beam",
        uselist=False,
        cascade="save-update, merge",  # Aggregation relationship - don't delete when beam is deleted
        lazy="select",  # Load on demand as max dose point is optional
    )
    monitor_unit_info: Mapped["MonitorUnitInfo | None"] = relationship(
        "MonitorUnitInfo",
        back_populates="beam",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="selectin",  # Optimize loading as MU info is frequently needed for calculations
    )

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize a new Beam instance.

        Args:
            **kwargs: Arbitrary keyword arguments for setting attributes.
                Common attributes include:
                - name: Name of the beam
                - beam_number: Number of the beam in the plan
                - isocenter_name: Name of the isocenter
                - modality: Beam modality (e.g., "PHOTON", "ELECTRON")
                - machine_energy_name: Name of the machine energy
                - monitor_units: Number of monitor units

        Note:
            This constructor is typically called by SQLAlchemy during object loading.
            For creating new beams programmatically, consider using the appropriate
            factory methods or the Trial.add_beam() method.
        """
        super().__init__(**kwargs)

    @override
    def __repr__(self) -> str:
        """
        Return a string representation of this beam.

        Returns:
            str: A string containing the beam's ID, number, name, and modality.
        """
        return (
            f"<Beam(id={self.id}, beam_number={self.beam_number}, name='{self.name}')>"
        )

    def add_control_point(self, control_point: "ControlPoint") -> None:
        """
        Add a control point to this beam.

        Args:
            control_point: Control point to add.
        """
        if self.cp_manager:
            self.cp_manager.control_point_list.append(control_point)

    def get_control_point(self, index: int) -> "ControlPoint | None":
        """
        Get a control point by its index.

        Args:
            index: Control point index to retrieve.

        Returns:
            Control point with the specified index, or None if not found.
        """
        if not self.cp_manager:
            return None

        for cp in self.cp_manager.control_point_list:
            if cp.index == index:
                return cp

        return None

    def is_arc(self) -> bool:
        """
        Check if this beam is an arc.

        Returns:
            True if this beam is an arc, False otherwise.
        """
        if not self.cp_manager:
            return False

        if len(self.cp_manager.control_point_list) < 2:
            return False

        first_cp = self.cp_manager.control_point_list[0]
        second_cp = self.cp_manager.control_point_list[1]

        if first_cp.gantry is None or second_cp.gantry is None:
            return False

        return abs(first_cp.gantry - second_cp.gantry) > 1.0

    @property
    def is_cw(self) -> bool | None:
        """
        Check if this beam is a clockwise arc.

        Returns:
            None if this beam is not an arc, True if this beam is a clockwise arc, False otherwise.
        """
        if (
            not self.is_arc()
            or self.cp_manager is None
            or self.cp_manager.gantry_is_ccw is None
        ):
            return None
        return not bool(self.cp_manager.gantry_is_ccw)

    @property
    def is_ccw(self) -> bool | None:
        """
        Check if this beam is a counter-clockwise arc.

        Returns:
            None if this beam is not an arc, True if this beam is a counter-clockwise arc, False otherwise.
        """
        if (
            not self.is_arc()
            or self.cp_manager is None
            or self.cp_manager.gantry_is_ccw is None
        ):
            return None
        return bool(self.cp_manager.gantry_is_ccw)

    def has_wedge(self) -> bool:
        """
        Check if this beam has a wedge.

        Returns:
            True if this beam has a wedge, False otherwise.
        """
        if not self.cp_manager:
            return False

        for cp in self.cp_manager.control_point_list:
            if cp.wedge_context and cp.wedge_context.wedge_name:
                return True

        return False

    def has_mlc(self) -> bool:
        """
        Check if this beam has MLC positions.

        Returns:
            True if any control point has MLC positions, False otherwise.
        """
        if not self.cp_manager:
            return False

        for cp in self.cp_manager.control_point_list:
            if cp.mlc_leaf_positions is not None:
                return cp.mlc_leaf_positions.size > 0
        return False

    def compute_monitor_units(self, pdd: float) -> float:
        """
        Compute the monitor units for this beam.

        Args:
            pdd: Percent depth dose at 10cm for the current machine and beam energy

        Returns:
            Computed monitor units.
        """
        if self.monitor_unit_info is None:
            raise ValueError("Monitor unit information is not available.")

        # From Pinnacle:
        # Dose at Ref Pt/Fraction = MU * ND * OFc * TTF * (D/MU)cal
        # where:
        #    MU = monitor units
        #    ND = normalization dose at reference point
        #    OFc = collimator output factor
        #    TTF = total transmission fraction. Typically 1.000
        #    (D/MU)cal = dose per monitor unit at calibration depth (e.g., PDD10)
        # TODO: Simplify using the MU Info Normalized Dose. See PyMedPhys
        beam_dose = self.monitor_unit_info.prescription_dose
        norm_dose = self.monitor_unit_info.normalized_dose
        output_factor = self.monitor_unit_info.collimator_output_factor
        transmission_factor = self.monitor_unit_info.total_transmission_fraction
        if (
            beam_dose is None
            or norm_dose is None
            or output_factor is None
            or transmission_factor is None
        ):
            raise ValueError("Monitor unit information is not available.")

        return beam_dose / (norm_dose * output_factor * transmission_factor * pdd)

    @property
    def dose_volume_file(self) -> str:
        """
        Get the dose volume file name.

        Returns:
            Dose volume file name, e.g., plan.Trial.binary.001
        """
        if self.dose_volume is None:
            raise ValueError("Dose volume is not available.")

        return f"plan.Trial.binary.{self.dose_volume.split(':')[1].zfill(3)}"

    @property
    def dose_var_volume_file(self) -> str:
        """
        Get the dose volume file name.

        Returns:
            Dose volume file name, e.g., plan.Trial.binary.001
        """
        if self.dose_var_volume is None:
            raise ValueError("Dose var volume is not available.")

        return f"plan.Trial.binary.{self.dose_var_volume.split(':')[1].zfill(3)}"

    @property
    def color(self) -> str | None:
        """
        Get the color of the beam.

        Returns:
            Color of the beam, or None if not available.
        """
        if not self.display_list:
            return None
        return self.display_list[0].color

    @property
    def control_point_list(self) -> list["ControlPoint"]:
        """
        Access control points via CPManager (matches Pinnacle hierarchy).

        Returns:
            List of control points managed by this beam's CPManager.
            Returns empty list if no CPManager exists.
        """
        return self.cp_manager.control_point_list if self.cp_manager else []
