# Progress Dialog Widget

A comprehensive modal progress dialog system for tracking long-running asynchronous operations in the Pinnacle IO UI application.

## Features

- **Modal Progress Dialog**: Non-intrusive progress tracking with visual progress bar
- **Production Mode**: Clean, minimal UI with one-line status text and progress bar
- **Development Mode**: Enhanced UI with scrolling timing log for performance benchmarking
- **Cancellation Support**: Optional cancel button for interrupting operations
- **Async Operation Runner**: High-level wrapper for running operations with automatic progress management
- **Timing & Benchmarking**: Detailed timing logs for performance analysis
- **Thread-Safe**: Safe communication between background threads and UI thread

## Components

### ProgressDialog Class

Core modal dialog widget for progress tracking.

```python
from pinnacle_io.ui.widgets import ProgressDialog

dialog = ProgressDialog(
    parent=main_window.root,
    title="Loading Data...",
    cancelable=True,
    development_mode=True
)
```

**Key Features:**
- Modal dialog that blocks interaction with parent window
- Progress bar with percentage display
- Status text updates
- Optional cancel button
- Development mode with timing logs
- Thread-safe UI updates

### AsyncOperationRunner Class

High-level wrapper for running operations with progress tracking.

```python
from pinnacle_io.ui.widgets import AsyncOperationRunner

runner = AsyncOperationRunner(
    parent=main_window.root,
    title="Processing...",
    cancelable=True,
    development_mode=True
)

def my_operation(progress_callback, status_callback, **kwargs):
    # Your operation code here
    progress_callback(50, "Halfway complete...")
    return result

runner.run_operation(
    operation_func=my_operation,
    callback_on_complete=lambda result: print("Done!"),
    callback_on_error=lambda error: print(f"Error: {error}")
)
```

## Usage Examples

### Basic Progress Dialog

```python
import threading
from pinnacle_io.ui.widgets import ProgressDialog

def show_simple_progress():
    dialog = ProgressDialog(
        parent=main_window.root,
        title="Simple Operation",
        cancelable=False
    )

    def work():
        dialog.show("Starting...", max_progress=100)

        for i in range(100):
            time.sleep(0.05)  # Simulate work

            if i % 10 == 0:
                dialog.update_progress(i + 1, f"Processing {i + 1}%...")
            else:
                dialog.update_progress(i + 1)

        dialog.update_status("Complete!")
        time.sleep(1)
        dialog.hide()

    threading.Thread(target=work, daemon=True).start()
```

### Cancelable Operation

```python
def show_cancelable_progress():
    dialog = ProgressDialog(
        parent=main_window.root,
        title="Long Operation",
        cancelable=True,
        development_mode=True
    )

    def work():
        dialog.show("Starting long operation...", max_progress=200)

        for i in range(200):
            # Check for cancellation
            if dialog.is_cancelled_flag():
                dialog.update_status("Cancelling...")
                time.sleep(0.5)
                break

            time.sleep(0.1)  # Simulate work

            # Update progress and check return value
            if not dialog.update_progress(i + 1, f"Step {i + 1}/200"):
                break  # Operation was cancelled

        if not dialog.is_cancelled_flag():
            dialog.update_progress(200, "Operation completed!")
            time.sleep(1)

        dialog.hide()

    threading.Thread(target=work, daemon=True).start()
```

### Using AsyncOperationRunner

```python
def load_data_async():
    runner = AsyncOperationRunner(
        parent=main_window.root,
        title="Loading Pinnacle Data",
        cancelable=True,
        development_mode=True
    )

    def load_operation(progress_callback, status_callback, data_path=None):
        # Step 1
        progress_callback(20, "Initializing reader...")
        reader = PinnacleReader(data_path)

        # Step 2
        progress_callback(40, "Loading institution...")
        institution = reader.get_institution()

        # Step 3
        progress_callback(60, "Loading patients...")
        # Process patients...

        # Step 4
        progress_callback(80, "Loading plans...")
        # Process plans...

        # Step 5
        progress_callback(100, "Loading complete!")

        return {'reader': reader, 'institution': institution}

    def on_success(result):
        print(f"Loaded data successfully: {result}")
        # Update UI with loaded data

    def on_error(error):
        print(f"Failed to load data: {error}")
        messagebox.showerror("Error", str(error))

    runner.run_operation(
        operation_func=load_operation,
        callback_on_complete=on_success,
        callback_on_error=on_error,
        data_path="/path/to/data"
    )
```

## Development Mode

Enable development mode for detailed timing and benchmarking:

### Environment Variable
```bash
export PINNACLE_IO_DEV=1
```

### Explicit Parameter
```python
dialog = ProgressDialog(
    parent=parent,
    title="Debug Operation",
    development_mode=True  # Force development mode
)
```

**Development Mode Features:**
- Scrolling text area with timing logs
- Step-by-step timing measurements
- Total operation time tracking
- Console output of timing information
- Resizable dialog window
- Detailed timing report generation

### Getting Timing Reports

```python
# After operation completes
timing_report = dialog.get_timing_report()
print(timing_report)
```

Example output:
```
=== Timing Report ===
[14:30:15.123] START: Dialog shown
[14:30:15.245] +0.122s: Initializing reader... (2/100)
[14:30:15.367] +0.122s: Loading institution... (3/100)
[14:30:15.489] +0.122s: Loading patients... (4/100)
[14:30:15.611] +0.122s: Operation completed (5/100)

Total execution time: 0.488 seconds
```

## Integration with MainWindow

### Replace Existing Methods

```python
from pinnacle_io.ui.widgets.progress_dialog_example import integrate_with_main_window

# Apply progress dialog integration
main_window = integrate_with_main_window(main_window)

# Now existing methods use progress dialogs
main_window.load_data("/path/to/data")  # Shows progress dialog
main_window._save_dicom_files("/export/path")  # Shows progress dialog
```

### Manual Integration

```python
# In MainWindow class
def load_data_with_progress(self, data_path):
    """Load data with progress tracking."""
    runner = AsyncOperationRunner(
        parent=self.root,
        title="Loading Pinnacle Data...",
        cancelable=True
    )

    def load_operation(progress_callback, status_callback):
        # Your existing load_data logic with progress updates
        progress_callback(25, "Loading institution...")
        # ... existing code ...
        progress_callback(100, "Loading complete!")
        return success_data

    def on_complete(result):
        # Update UI with loaded data
        self.pinnacle_reader = result['reader']
        # ... update UI components ...

    runner.run_operation(
        operation_func=load_operation,
        callback_on_complete=on_complete,
        callback_on_error=lambda e: messagebox.showerror("Error", str(e))
    )
```

## Testing

### Run Test Script

```bash
cd src/pinnacle_io/ui/widgets/
python test_progress_dialog.py
```

The test script provides interactive testing of all features:
- Simple progress dialog
- Cancelable operations
- Async operation runner
- Development mode with timing
- Fast operations handling

### Test Features

1. **Simple Progress Test**: Basic progress dialog without cancellation
2. **Cancelable Progress Test**: Dialog with cancel button functionality
3. **Async Operation Test**: AsyncOperationRunner with callbacks
4. **Development Mode Test**: Enhanced dialog with timing logs
5. **Fast Operation Test**: Ensures proper handling of quick operations

## API Reference

### ProgressDialog Methods

#### `__init__(parent, title, cancelable=False, development_mode=None)`
Initialize progress dialog.

#### `show(status="Starting...", max_progress=100)`
Show the dialog with initial status and maximum progress value.

#### `hide()`
Hide and destroy the dialog.

#### `update_progress(progress, status="") -> bool`
Update progress bar and optionally status. Returns False if cancelled.

#### `update_status(status) -> bool`
Update status text only. Returns False if cancelled.

#### `is_cancelled_flag() -> bool`
Check if cancellation was requested.

#### `set_cancel_callback(callback)`
Set function to call when cancel is requested.

#### `get_timing_report() -> str`
Get formatted timing report for benchmarking.

### AsyncOperationRunner Methods

#### `__init__(parent, title, cancelable=False, development_mode=None)`
Initialize async operation runner.

#### `run_operation(operation_func, callback_on_complete=None, callback_on_error=None, **kwargs)`
Run operation asynchronously with progress tracking.

**Operation Function Signature:**
```python
def operation_func(progress_callback, status_callback, **kwargs):
    # progress_callback(progress_value, optional_status) -> bool
    # status_callback(status_message) -> bool
    # Return value passed to callback_on_complete
    pass
```

## Thread Safety

The progress dialog system is designed to be thread-safe:

- **UI Updates**: All UI updates are scheduled on the main thread using `parent.after_idle()`
- **Cancellation**: Thread-safe cancellation flag using `threading.Event`
- **Callbacks**: Completion and error callbacks are executed on the main thread
- **Exception Handling**: Proper exception handling across thread boundaries

## Environment Variables

- `PINNACLE_IO_DEV`: Set to `1`, `true`, `yes`, or `on` to enable development mode by default

## Dependencies

- `tkinter`: Standard GUI toolkit
- `ttkbootstrap`: Modern themed widgets
- `threading`: Thread management for async operations
- `datetime`: Timing and benchmarking
- `typing`: Type hints for better code quality

## Best Practices

1. **Always use threading**: Never run long operations on the main GUI thread
2. **Check cancellation**: Regular checks for `is_cancelled_flag()` or progress callback return values
3. **Meaningful status updates**: Provide clear, specific status messages for user feedback
4. **Error handling**: Always implement proper error handling and user feedback
5. **Resource cleanup**: Ensure proper cleanup even if operations are cancelled
6. **Development mode**: Use development mode during testing and debugging for timing insights

---

# Multi-Stage Data Import Progress Tracking

## Overview

The Pinnacle IO UI application involves complex, multi-stage data loading processes that span across multiple files and UI components. The data import workflow follows this typical pattern:

```
MainWindow → RightSidebar → Individual Panels (ROI, POI, Beams, Dose)
```

Each stage involves significant processing time and multiple API calls to the PinnacleReader, making it essential to provide comprehensive progress tracking across the entire chain.

## Multi-Stage Data Import Process Flow

### Stage 1: MainWindow Initial Data Loading
**File**: `src/pinnacle_io/ui/main_window.py`

```python
# _load_initial_data method
def _load_initial_data(self):
    # Progress: 0-20%
    institution = self.pinnacle_reader.get_institution()
    # Progress: 20-40%
    # Populate patients and auto-select

# on_patient_selected method
def on_patient_selected(self, patient_id: int):
    # Progress: 0-30%
    patient = self.pinnacle_reader.get_patient(institution=..., patient=patient_id)
    # Progress: 30-60%
    # Populate plans and auto-select

# on_plan_selected method
def on_plan_selected(self, patient_id: int, plan: Plan):
    # Progress: 0-25%
    trials = self.pinnacle_reader.get_trials(institution=..., patient=..., plan=...)
    # Progress: 25-50%
    rois = self.pinnacle_reader.get_rois(institution=..., patient=..., plan=...)
    # Progress: 50-75%
    points = self.pinnacle_reader.get_points(institution=..., patient=..., plan=...)
    # Progress: 75-85%
    image_set = self.pinnacle_reader.get_image_set(institution=..., patient=..., image_set=0)
    # Progress: 85-100%
    # Continue to Stage 2...
```

### Stage 2: RightSidebar Data Distribution
**File**: `src/pinnacle_io/ui/widgets/right_sidebar.py`

```python
# These methods are called from MainWindow and trigger Stage 3
def update_plan_info(self, plan, rois, points):
    # Coordinate transformations and panel loading
    self.roi_panel.load_data(rois, patient_position=patient_position)
    self.poi_panel.load_data(points, patient_position=patient_position)

def update_trial_info(self, trial):
    # Beam processing and panel loading
    self.beams_panel.load_data(trial.beam_list, self.current_points, patient_position=...)

def update_dose_info(self, dose, patient_position=None):
    # Dose processing and panel loading
    self.dose_panel.load_data(dose, patient_position=..., prescription_isodose_cgy=...)
```

### Stage 3: Individual Panel Data Processing
**Files**: `roi_panel.py`, `poi_panel.py`, `beams_panel.py`, `dose_panel.py`

Each panel performs intensive operations:
- **ROI Panel**: Coordinate transformations, curve processing, contour generation
- **POI Panel**: Point transformations, visibility setup, coordinate mapping
- **Beams Panel**: Isocenter calculations, beam visualization data, coordinate transforms
- **Dose Panel**: Dose grid analysis, isodose contour generation, slice calculations

## Implementation Strategy for Multi-Stage Progress Tracking

### Shared Progress Dialog Architecture

The progress dialog needs to be accessible and manageable across multiple stages and components. Here's the proposed architecture:

```python
class ProgressManager:
    """Centralized progress management for multi-stage operations."""

    def __init__(self, parent, title="Loading Data...", total_stages=3):
        self.dialog = ProgressDialog(parent, title, cancelable=True, development_mode=True)
        self.current_stage = 0
        self.total_stages = total_stages
        self.stage_weights = [40, 35, 25]  # MainWindow, RightSidebar, Panels
        self.current_stage_progress = 0

    def update_stage_progress(self, stage_progress, status="", new_stage=False):
        """Update progress within current stage or advance to new stage."""
        if new_stage:
            self.current_stage += 1
            self.current_stage_progress = 0

        self.current_stage_progress = stage_progress

        # Calculate overall progress
        completed_stages_progress = sum(self.stage_weights[:self.current_stage])
        current_stage_progress = (stage_progress / 100.0) * self.stage_weights[self.current_stage]
        total_progress = completed_stages_progress + current_stage_progress

        return self.dialog.update_progress(int(total_progress), status)
```

---

# Detailed Answers to Implementation Questions

## 1. How does the ProgressDialog need to be updated for quick and easy access from multiple points in the UI processing chain?

### Current Limitations
The existing ProgressDialog implementation requires each component to create and manage its own dialog instance, leading to:
- Multiple dialogs potentially appearing simultaneously
- No coordination between different stages of the same operation
- Loss of timing continuity across stages
- Inconsistent user experience

### Required Updates

#### A. Singleton Progress Manager Pattern
```python
class ProgressManager:
    """Centralized progress management with singleton-like behavior per operation."""

    _active_instance = None

    @classmethod
    def get_or_create_instance(cls, parent, operation_id, **kwargs):
        """Get existing instance or create new one for operation."""
        if cls._active_instance and cls._active_instance.operation_id == operation_id:
            return cls._active_instance
        else:
            cls._active_instance = cls(parent, operation_id, **kwargs)
            return cls._active_instance

    def __init__(self, parent, operation_id, title="Processing...", **kwargs):
        self.operation_id = operation_id
        self.dialog = ProgressDialog(parent, title, **kwargs)
        self.stage_registry = {}  # Track stages and their weights
        self.current_stage = None
        self.overall_progress = 0
```

#### B. MainWindow Integration Points
Add progress manager access methods to MainWindow:
```python
class MainWindow:
    def __init__(self, ...):
        self.progress_manager = None

    def start_progress_operation(self, operation_id, title, cancelable=True):
        """Initialize progress tracking for multi-stage operation."""
        self.progress_manager = ProgressManager.get_or_create_instance(
            parent=self.root,
            operation_id=operation_id,
            title=title,
            cancelable=cancelable,
            development_mode=True
        )
        return self.progress_manager

    def get_progress_manager(self):
        """Get current progress manager instance."""
        return self.progress_manager
```

#### C. Component Access Pattern
Update all components to access the shared progress manager:
```python
# In RightSidebar, ROIPanel, etc.
def load_data_with_progress(self, data, **kwargs):
    """Load data with progress tracking."""
    # Get shared progress manager from main window
    progress_manager = self.app.get_progress_manager()

    if progress_manager:
        # Register this component's stage
        progress_manager.register_stage("roi_processing", weight=15)
        progress_manager.set_current_stage("roi_processing")

        # Update progress during processing
        progress_manager.update_progress(25, "Processing ROI curves...")
        # ... processing logic ...
        progress_manager.update_progress(100, "ROI processing complete")
```

## 2. How can methods farther down the chain know whether the ProgressDialog is already being displayed or if that step needs to show the ProgressDialog?

### State Management System

#### A. Global Progress State Tracking
```python
class ProgressState:
    """Global state tracking for progress operations."""

    def __init__(self):
        self.active_operations = {}  # operation_id -> ProgressManager
        self.operation_hierarchy = {}  # parent_operation -> child_operations

    def is_progress_active(self, operation_id=None):
        """Check if any progress dialog is currently active."""
        if operation_id:
            return operation_id in self.active_operations
        return len(self.active_operations) > 0

    def get_active_progress_manager(self, operation_id=None):
        """Get active progress manager for operation or any active one."""
        if operation_id and operation_id in self.active_operations:
            return self.active_operations[operation_id]
        elif self.active_operations:
            return next(iter(self.active_operations.values()))
        return None
```

#### B. Chain Detection Logic
Each method in the processing chain uses this pattern:
```python
def process_data_with_progress_awareness(self, data, **kwargs):
    """Process data with automatic progress dialog management."""

    # Check if progress dialog is already active
    progress_manager = self.app.get_progress_manager()
    is_sub_operation = progress_manager and progress_manager.is_active()

    if not is_sub_operation:
        # We're the top-level operation - start progress dialog
        progress_manager = self.app.start_progress_operation(
            operation_id="load_plan_data",
            title="Loading Plan Data..."
        )
        should_close_on_completion = True
    else:
        # We're part of a larger operation - use existing progress
        should_close_on_completion = False

    try:
        # Register our stage in the operation
        progress_manager.register_stage("plan_loading", weight=30)
        progress_manager.set_current_stage("plan_loading")

        # Perform processing with progress updates
        self._process_with_progress(data, progress_manager)

    finally:
        # Only close if we started the operation
        if should_close_on_completion:
            progress_manager.complete_operation()
```

#### C. Context-Aware Progress Updates
```python
class ProgressManager:
    def update_progress(self, progress, status="", context=None):
        """Update progress with automatic context detection."""

        # Determine if this is a sub-operation or main operation
        if self.current_stage:
            # We're in a registered stage - update stage progress
            return self._update_stage_progress(progress, status)
        else:
            # Direct progress update
            return self.dialog.update_progress(progress, status)

    def _update_stage_progress(self, stage_progress, status):
        """Update progress within the current stage."""
        # Calculate overall progress based on stage weights
        total_progress = self._calculate_total_progress(stage_progress)

        # Update timing logs with stage context
        stage_status = f"[{self.current_stage}] {status}"

        return self.dialog.update_progress(total_progress, stage_status)
```

## 3. How can the ProgressDialog remain open in dev mode to review the contents of the scrolling text area?

### Development Mode Persistence

#### A. Manual Close Control
```python
class ProgressDialog:
    def __init__(self, parent, title, cancelable=False, development_mode=None, auto_close=None):
        # ... existing initialization ...

        # Auto-close behavior based on mode
        if auto_close is None:
            self.auto_close = not self.development_mode  # Stay open in dev mode
        else:
            self.auto_close = auto_close

        self.operation_completed = False

    def complete_operation(self, success=True, final_message="Operation completed"):
        """Mark operation as completed but don't auto-close in dev mode."""
        self.operation_completed = True
        self.update_status(final_message)

        if self.auto_close:
            # Auto-close in production mode
            self.root.after(1500, self.hide)  # Brief delay to show final status
        else:
            # Stay open in development mode
            self._enable_manual_close()

    def _enable_manual_close(self):
        """Enable manual close button after operation completion."""
        if hasattr(self, 'close_button'):
            self.close_button.config(state='normal')

        # Add timing report button in development mode
        if self.development_mode and hasattr(self, 'timing_text'):
            self._add_timing_report_controls()
```

#### B. Enhanced Development Mode UI
```python
def _setup_dialog_ui(self):
    """Set up dialog UI with development mode enhancements."""
    # ... existing setup ...

    if self.development_mode:
        # ... existing timing log setup ...

        # Add development mode controls frame
        dev_controls_frame = ttk.Frame(main_frame)
        dev_controls_frame.pack(fill=tk.X, pady=(10, 0))

        # Copy timing report button
        copy_report_btn = ttk.Button(
            dev_controls_frame,
            text="Copy Timing Report",
            command=self._copy_timing_report,
            state='disabled'  # Enabled after completion
        )
        copy_report_btn.pack(side=tk.LEFT, padx=(0, 5))

        # Save timing report button
        save_report_btn = ttk.Button(
            dev_controls_frame,
            text="Save Report",
            command=self._save_timing_report,
            state='disabled'  # Enabled after completion
        )
        save_report_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.dev_control_buttons = [copy_report_btn, save_report_btn]

    # Close button (always present but controlled)
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(10, 0))

    if self.cancelable:
        self.cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=self._on_cancel_clicked,
            style="danger.TButton"
        )
        self.cancel_button.pack(side=tk.LEFT)

    self.close_button = ttk.Button(
        button_frame,
        text="Close",
        command=self.hide,
        state='disabled'  # Enabled after completion
    )
    self.close_button.pack(side=tk.RIGHT)
```

#### C. Timing Report Enhancements
```python
def _add_timing_report_controls(self):
    """Add controls for timing report management in development mode."""

    # Enable development mode buttons
    for button in self.dev_control_buttons:
        button.config(state='normal')

    # Auto-scroll to show full timing log
    if self.timing_text:
        self.timing_text.see(tk.END)

        # Highlight summary information
        self._highlight_timing_summary()

def _copy_timing_report(self):
    """Copy timing report to clipboard."""
    report = self.get_timing_report()
    self.root.clipboard_clear()
    self.root.clipboard_append(report)

    # Show confirmation
    self.update_status("Timing report copied to clipboard")

def _save_timing_report(self):
    """Save timing report to file."""
    from tkinter import filedialog

    filename = filedialog.asksaveasfilename(
        title="Save Timing Report",
        defaultextension=".txt",
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
    )

    if filename:
        try:
            with open(filename, 'w') as f:
                f.write(self.get_timing_report())
            self.update_status(f"Timing report saved to {filename}")
        except Exception as e:
            self.update_status(f"Error saving report: {e}")
```

## 4. Can a "Close" button be implemented that's inactive until the end of the processing chain is reached?

### Progressive Button State Management

#### A. Button State Control System
```python
class ProgressDialog:
    def __init__(self, parent, title, cancelable=False, development_mode=None):
        # ... existing initialization ...

        self.button_states = {
            'cancel': True,      # Initially enabled if cancelable
            'close': False,      # Initially disabled
            'dev_controls': False  # Initially disabled in dev mode
        }

        self.operation_phase = "running"  # running, completed, cancelled

    def _update_button_states(self):
        """Update button states based on operation phase."""
        if self.operation_phase == "running":
            if hasattr(self, 'cancel_button') and self.cancelable:
                self.cancel_button.config(state='normal')
            if hasattr(self, 'close_button'):
                self.close_button.config(state='disabled')

        elif self.operation_phase in ["completed", "cancelled"]:
            if hasattr(self, 'cancel_button'):
                self.cancel_button.config(state='disabled')
            if hasattr(self, 'close_button'):
                self.close_button.config(state='normal')

            # Enable development mode controls
            if self.development_mode and hasattr(self, 'dev_control_buttons'):
                for button in self.dev_control_buttons:
                    button.config(state='normal')
```

#### B. Operation Lifecycle Management
```python
def complete_operation(self, success=True, final_message="Operation completed"):
    """Mark operation as completed and update UI accordingly."""
    self.operation_phase = "completed" if success else "failed"
    self.operation_completed = True

    # Update status with final message
    self.update_status(final_message)

    # Log completion with timing
    self._log_timing("Operation completed", final_message)

    # Update button states
    self._update_button_states()

    # Auto-close behavior (only in production mode)
    if self.auto_close and self.operation_phase == "completed":
        self.root.after(2000, self.hide)  # 2 second delay to show final status
    else:
        # In development mode or on failure, stay open for review
        self._prepare_for_manual_close()

def _prepare_for_manual_close(self):
    """Prepare dialog for manual close after operation completion."""

    # Focus on close button for keyboard accessibility
    if hasattr(self, 'close_button'):
        self.close_button.focus_set()

    # In development mode, auto-scroll to show timing summary
    if self.development_mode and hasattr(self, 'timing_text'):
        self.timing_text.see(tk.END)

        # Add separator and summary to timing log
        summary = f"\n{'='*50}\n{self.get_timing_summary()}\n{'='*50}"
        self.timing_text.config(state=tk.NORMAL)
        self.timing_text.insert(tk.END, summary)
        self.timing_text.config(state=tk.DISABLED)
        self.timing_text.see(tk.END)

def get_timing_summary(self) -> str:
    """Get condensed timing summary for quick review."""
    if not self.timing_log:
        return "No timing data available"

    total_time = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
    total_steps = len(self.timing_log)

    # Find slowest steps
    slowest_steps = sorted(self.timing_log, key=lambda x: x['elapsed_step'], reverse=True)[:3]

    summary_lines = [
        f"TIMING SUMMARY:",
        f"Total Time: {total_time:.3f}s",
        f"Total Steps: {total_steps}",
        f"Average Step Time: {total_time/total_steps:.3f}s" if total_steps > 0 else "Average Step Time: N/A",
        "",
        "Slowest Steps:"
    ]

    for i, step in enumerate(slowest_steps, 1):
        summary_lines.append(f"  {i}. {step['step_name']}: {step['elapsed_step']:.3f}s")

    return "\n".join(summary_lines)
```

#### C. Enhanced Close Button Behavior
```python
def _setup_close_button(self, button_frame):
    """Set up close button with enhanced behavior."""

    self.close_button = ttk.Button(
        button_frame,
        text="Close",
        command=self._on_close_clicked,
        state='disabled'
    )
    self.close_button.pack(side=tk.RIGHT)

    # Add keyboard shortcuts
    self.dialog.bind('<Return>', lambda e: self._on_close_if_enabled())
    self.dialog.bind('<Escape>', lambda e: self._on_close_if_enabled())

def _on_close_clicked(self):
    """Handle close button click with confirmation if operation is still running."""

    if self.operation_phase == "running":
        # This shouldn't happen since button is disabled, but safety check
        response = messagebox.askyesno(
            "Close Progress Dialog",
            "Operation is still running. Are you sure you want to close?",
            parent=self.dialog
        )
        if not response:
            return

    # Close dialog
    self.hide()

def _on_close_if_enabled(self):
    """Handle keyboard shortcuts for closing."""
    if hasattr(self, 'close_button') and self.close_button['state'] == 'normal':
        self._on_close_clicked()
```

### Integration Summary

This enhanced progress dialog system provides:

1. **Centralized Management**: Single progress dialog instance shared across all components
2. **Intelligent State Detection**: Automatic detection of whether progress is already being tracked
3. **Development Mode Persistence**: Dialog remains open in dev mode for timing review
4. **Progressive Button Control**: Close button enabled only after completion
5. **Rich Timing Analysis**: Detailed timing logs with summary information
6. **Keyboard Accessibility**: Proper focus management and keyboard shortcuts
7. **Graceful Error Handling**: Proper state management even when operations fail

The system maintains the existing simple API for basic use cases while providing advanced features for complex multi-stage operations like the Pinnacle IO data loading workflow.