"""
Reader for Pinnacle Patient files.
"""

from typing import Any
from pinnacle_io.models import Patient
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class PatientReader(BasePinnacleReader):
    """
    Reader for Pinnacle Patient files.
    """

    @staticmethod
    def read_from_ids(institution_id: int, patient_id: int, mount_id: int = 0, file_service: Any = None) -> Patient:
        """
        Read a Pinnacle Patient file using institution, patient, and mount IDs.

        Args:
            institution_id: Institution ID number
            patient_id: Patient ID number
            mount_id: Mount ID number (default: 0)
            file_service: File service object with open_file method

        Returns:
            Patient model populated with data from the file

        Usage:
            patient = PatientReader.read_from_ids(1, 5)
            patient = PatientReader.read_from_ids(1, 5, mount_id=2)
            patient = PatientReader.read_from_ids(1, 5, file_service=file_service)
        """
        # Construct standard Pinnacle path format
        patient_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}"

        # Delegate to path-based method
        return PatientReader.read_from_path(patient_path, file_service)

    @staticmethod
    def read_from_path(patient_path: str, file_service: Any = None) -> Patient:
        """
        Read a Pinnacle Patient file from a specified path.

        Args:
            patient_path: Path to the Patient file or directory
            file_service: File service object with open_file method

        Returns:
            Patient model populated with data from the file

        Usage:
            patient = PatientReader.read_from_path("/path/to/Patient_0/Patient")
            patient = PatientReader.read_from_path("/path/to/Patient_0")
            patient = PatientReader.read_from_path("/path/to/Patient_0", file_service=file_service)
        """
        # Resolve file path and name
        file_path, file_name = PatientReader._resolve_file_path(patient_path, "Patient")

        # Read file content using base class utility
        content_lines = PatientReader._read_file_lines(file_path, file_name, file_service)

        return PatientReader.parse(content_lines)

    @staticmethod
    def parse(content_lines: list[str]) -> Patient:
        """
        Parse a Pinnacle Patient content string and create a Patient model.

        Args:
            content_lines: Pinnacle Patient content lines

        Returns:
            Patient model populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        patient = Patient(**data)
        return patient
