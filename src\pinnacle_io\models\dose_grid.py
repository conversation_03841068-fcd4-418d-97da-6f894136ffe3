"""
SQLAlchemy model for Pinnacle DoseGrid data.

This module provides the DoseGrid model for representing dose grid details in the Pinnacle
treatment planning system. The dose grid defines the 3D volume where dose is calculated.
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, TypeVar, override, cast, Dict

from sqlalchemy import Column, <PERSON><PERSON>ey, Integer, Float
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase
from pinnacle_io.models.types import VoxelSize, Coordinate, Dimension


# TypeVar for generic type handling in _extract_xyz
T = TypeVar("T", int, float)

if TYPE_CHECKING:
    from pinnacle_io.models.dose import Dose
    from pinnacle_io.models.trial import Trial


class DoseGrid(PinnacleBase):
    """
    Model representing a dose grid in the Pinnacle treatment planning system.

    This class stores information about the 3D grid where dose is calculated, including:
    - Voxel dimensions and grid spacing
    - Grid dimensions in number of voxels
    - Origin position in DICOM coordinates
    - Volume rotation deltas for non-axial orientations
    - Display and summation settings

    The dose grid is associated with a parent Trial and may have multiple Dose objects
    associated with it, representing different dose calculations on the same grid.

    Attributes:
        id (int): Primary key (inherited from PinnacleBase)
        voxel_size (VoxelSize | None): Voxel dimensions in mm
        dimension (Dimension | None): Number of voxels in each direction
        origin (Coordinate | None): Grid origin in DICOM coordinates (mm)
        vol_rot_delta (Coordinate | None): Volume rotation deltas for non-axial orientations (radians)
        display_2d (int | None]): Flag indicating if 2D display is enabled
        dose_summation_type (int | None]): Type of dose summation

    Relationships:
        trial (Trial): The parent Trial to which this DoseGrid belongs (many-to-one).
                      Back-populates to Trial.dose_grid.
        dose_list (list[Dose]): List of Dose objects associated with this grid (one-to-many).
                              Back-populates to Dose.dose_grid.
    """

    __tablename__: str = "DoseGrid"

    # Primary key is inherited from PinnacleBase

    # Composite columns for spatial data
    voxel_size_x: Mapped[float | None] = Column("VoxelSizeX", type_=Float, nullable=True)
    voxel_size_y: Mapped[float | None] = Column("VoxelSizeY", type_=Float, nullable=True)
    voxel_size_z: Mapped[float | None] = Column("VoxelSizeZ", type_=Float, nullable=True)
    dimension_x: Mapped[int | None] = Column("DimensionX", type_=Integer, nullable=True)
    dimension_y: Mapped[int | None] = Column("DimensionY", type_=Integer, nullable=True)
    dimension_z: Mapped[int | None] = Column("DimensionZ", type_=Integer, nullable=True)
    origin_x: Mapped[float | None] = Column("OriginX", type_=Float, nullable=True)
    origin_y: Mapped[float | None] = Column("OriginY", type_=Float, nullable=True)
    origin_z: Mapped[float | None] = Column("OriginZ", type_=Float, nullable=True)
    vol_rot_delta_x: Mapped[float | None] = Column("VolRotDeltaX", type_=Float, nullable=True)
    vol_rot_delta_y: Mapped[float | None] = Column("VolRotDeltaY", type_=Float, nullable=True)
    vol_rot_delta_z: Mapped[float | None] = Column("VolRotDeltaZ", type_=Float, nullable=True)

    display_2d: Mapped[int | None] = Column(
        "Display2D",
        Integer,
        nullable=True,
        doc="Flag indicating if 2D display is enabled (1) or disabled (0)",
    )
    dose_summation_type: Mapped[int | None] = Column(
        "DoseSummationType",
        Integer,
        nullable=True,
        doc="Type of dose summation (e.g., 0=plan, 1=fraction, 2=beam, etc.)",
    )

    # Parent relationship with back-population to Trial.dose_grid
    trial_id: Mapped[int] = Column(
        "TrialID",
        Integer,
        ForeignKey("Trial.ID"),
        nullable=False,
        doc="Foreign key to the parent Trial",
    )
    trial: Mapped["Trial"] = relationship(
        "Trial",
        back_populates="dose_grid",
        lazy="joined",  # Optimize loading as trial info is frequently needed
        doc="The parent Trial to which this DoseGrid belongs",
    )

    # Child relationship with Dose objects
    dose_list: Mapped[list["Dose"]] = relationship(
        "Dose",
        back_populates="dose_grid",
        cascade="all, delete-orphan",
        lazy="selectin",
        doc="List of Dose objects associated with this grid",
    )

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize a DoseGrid instance with the given parameters.

        This constructor handles initialization of the dose grid with support for both
        individual component values (e.g., voxel_size_x, voxel_size_y, voxel_size_z)
        and composite objects (e.g., voxel_size=VoxelSize(1,1,1) or
        voxel_size={'x': 1, 'y': 1, 'z': 1}).

        Raises:
            ValueError: If conflicting arguments are provided (e.g., both
                        `voxel_size` and `VoxelSize`, or `voxel_size_x` and
                        `VoxelSizeX`).

        Args:
            **kwargs: Keyword arguments corresponding to model attributes.
                Supported keyword arguments include all column names as attributes and:
                - voxel_size: VoxelSize object or dict with x,y,z values
                - dimension: Dimension object or dict with x,y,z values
                - origin: Coordinate object or dict with x,y,z values
                - vol_rot_delta: Coordinate object or dict with x,y,z values
                - trial: The parent Trial object
                - dose_list: List of Dose objects
        """
        # Define the spatial properties to process
        property_map = {
            "voxel_size": ("VoxelSize", VoxelSize),
            "dimension": ("Dimension", Dimension),
            "origin": ("Origin", Coordinate),
            "vol_rot_delta": ("VolRotDelta", Coordinate),
        }

        # Temporarily store kwargs for spatial properties to set them via setters
        spatial_kwargs: Dict[str, Any] = {}

        for snake_name, (pascal_name, _) in property_map.items():
            # Handle composite objects (e.g., voxel_size={...} or VoxelSize(...))
            if snake_name in kwargs or pascal_name in kwargs:
                if snake_name in kwargs and pascal_name in kwargs:
                    raise ValueError(f"Cannot provide both '{snake_name}' and '{pascal_name}'.")
                value = kwargs.pop(snake_name, kwargs.pop(pascal_name, None))
                spatial_kwargs[snake_name] = value

            # Handle individual components (e.g., voxel_size_x=1.0)
            components = {}
            for i in ["x", "y", "z"]:
                snake_comp = f"{snake_name}_{i}"
                pascal_comp = f"{pascal_name}{i.upper()}"
                if snake_comp in kwargs or pascal_comp in kwargs:
                    if snake_comp in kwargs and pascal_comp in kwargs:
                        raise ValueError(f"Cannot provide both '{snake_comp}' and '{pascal_comp}'.")
                    components[i] = kwargs.pop(snake_comp, kwargs.pop(pascal_comp, None))
            if components:
                d = spatial_kwargs.setdefault(snake_name, {})
                if isinstance(d, dict):
                    d_typed: Dict[str, Any] = d  # type: ignore
                    d_typed.update(cast(Dict[str, Any], components))

        # Initialize the base object with non-spatial properties
        super().__init__(**kwargs)

        # Now, use setters to properly initialize spatial properties
        for name, value in spatial_kwargs.items():
            setattr(self, str(name), value)

    @property
    def voxel_size(self) -> VoxelSize | None:
        """Get the voxel size as a VoxelSize object."""
        components = (self.voxel_size_x, self.voxel_size_y, self.voxel_size_z)
        if any(c is None for c in components):
            return None
        return VoxelSize(*components)

    @voxel_size.setter
    def voxel_size(self, value: Any) -> None:
        """Set the voxel size from a VoxelSize object, dict, or None."""
        if value is None or (isinstance(value, dict) and not value):
            self.voxel_size_x = self.voxel_size_y = self.voxel_size_z = None
            return

        try:
            components = self._extract_xyz(value)
            # Allow None components, just set them as is
            if components["x"] is None and components["y"] is None and components["z"] is None:
                self.voxel_size_x = self.voxel_size_y = self.voxel_size_z = None
                return
            self.voxel_size_x = float(components["x"]) if components["x"] is not None else None
            self.voxel_size_y = float(components["y"]) if components["y"] is not None else None
            self.voxel_size_z = float(components["z"]) if components["z"] is not None else None
        except (TypeError, ValueError) as e:
            raise ValueError(f"Invalid voxel size value: {value}") from e

    @property
    def dimension(self) -> Dimension | None:
        """Get the grid dimension as a Dimension object."""
        components = (self.dimension_x, self.dimension_y, self.dimension_z)
        if any(c is None for c in components):
            return None
        return Dimension(*components)

    @dimension.setter
    def dimension(self, value: Any) -> None:
        """Set the grid dimension from a Dimension object, dict, or None."""
        if value is None or (isinstance(value, dict) and not value):
            self.dimension_x = self.dimension_y = self.dimension_z = None
            return

        try:
            components = self._extract_xyz(value)
            if components["x"] is None and components["y"] is None and components["z"] is None:
                self.dimension_x = self.dimension_y = self.dimension_z = None
                return
            self.dimension_x = int(components["x"]) if components["x"] is not None else None
            self.dimension_y = int(components["y"]) if components["y"] is not None else None
            self.dimension_z = int(components["z"]) if components["z"] is not None else None
        except (TypeError, ValueError) as e:
            raise ValueError(f"Invalid dimension value: {value}") from e

    @property
    def origin(self) -> Coordinate | None:
        """Get the grid origin as a Coordinate object."""
        components = (self.origin_x, self.origin_y, self.origin_z)
        if any(c is None for c in components):
            return None
        return Coordinate(*components)

    @origin.setter
    def origin(self, value: Any) -> None:
        """Set the grid origin from a Coordinate object, dict, or None."""
        if value is None or (isinstance(value, dict) and not value):
            self.origin_x = self.origin_y = self.origin_z = None
            return

        try:
            components = self._extract_xyz(value)
            if components["x"] is None and components["y"] is None and components["z"] is None:
                self.origin_x = self.origin_y = self.origin_z = None
                return
            self.origin_x = float(components["x"]) if components["x"] is not None else None
            self.origin_y = float(components["y"]) if components["y"] is not None else None
            self.origin_z = float(components["z"]) if components["z"] is not None else None
        except (TypeError, ValueError) as e:
            raise ValueError(f"Invalid origin value: {value}") from e

    @property
    def vol_rot_delta(self) -> Coordinate | None:
        """Get the volume rotation delta as a Coordinate object."""
        components = (
            self.vol_rot_delta_x,
            self.vol_rot_delta_y,
            self.vol_rot_delta_z,
        )
        if any(c is None for c in components):
            return None
        return Coordinate(*components)

    @vol_rot_delta.setter
    def vol_rot_delta(self, value: Any) -> None:
        """Set the volume rotation delta from a Coordinate object, dict, or None."""
        if value is None or (isinstance(value, dict) and not value):
            self.vol_rot_delta_x = self.vol_rot_delta_y = self.vol_rot_delta_z = None
            return

        try:
            components = self._extract_xyz(value)
            if components["x"] is None and components["y"] is None and components["z"] is None:
                self.vol_rot_delta_x = self.vol_rot_delta_y = self.vol_rot_delta_z = None
                return
            self.vol_rot_delta_x = float(components["x"]) if components["x"] is not None else None
            self.vol_rot_delta_y = float(components["y"]) if components["y"] is not None else None
            self.vol_rot_delta_z = float(components["z"]) if components["z"] is not None else None
        except (TypeError, ValueError) as e:
            raise ValueError(f"Invalid rotation delta value: {value}") from e

    @override
    def __repr__(self) -> str:
        """
        Return a string representation of this dose grid.

        Returns:
            str: A string representation in the format:
                <DoseGrid(id=X, trial='trial_name', dimension=(XxYxZ))>
        """
        trial_name = getattr(getattr(self, "trial", ""), "name", "")
        dim = self.dimension
        dim_str = f"({dim.x}, {dim.y}, {dim.z})" if dim is not None else "(0, 0, 0)"
        return f"<DoseGrid(id={self.id}, trial='{trial_name}', dimension={dim_str})>"

    def _extract_xyz(
        self,
        entity: Any,
        default_x: float | int | None = None,
        default_y: float | int | None = None,
        default_z: float | int | None = None,
    ) -> dict[str, float | int | None]:
        """
        Extract X, Y, Z components from a spatial entity.

        This helper method handles multiple input formats for spatial data:
        - Objects with x, y, z attributes (e.g., VoxelSize, Dimension, Coordinate)
        - Dictionaries with 'x', 'y', 'z' keys (case insensitive)
        - None values (returns defaults)

        Args:
            entity: The entity to extract components from. Can be:
                   - A VoxelSize, Dimension, or Coordinate object
                   - A dict with 'x', 'y', 'z' keys (case insensitive)
                   - None (returns default values)
            default_x: Default value for X if not found in entity
            default_y: Default value for Y if not found in entity
            default_z: Default value for Z if not found in entity

        Returns:
            Dict[str, float | int | None]:
                Dictionary with 'x', 'y', 'z' keys and their values
                (types match the input types where possible)
        """
        if entity is None:
            return {"x": default_x, "y": default_y, "z": default_z}

        if hasattr(entity, "x") and hasattr(entity, "y") and hasattr(entity, "z"):
            # Handle spatial type objects (VoxelSize, Dimension, Coordinate)
            return {
                "x": cast(float | int | None, entity.x),
                "y": cast(float | int | None, entity.y),
                "z": cast(float | int | None, entity.z),
            }

        if isinstance(entity, dict):
            # Handle dict with x, y, z keys (case insensitive)
            entity_lower: Dict[str, Any] = {str(k).lower(): v for k, v in entity.items()}  # type: ignore
            x = entity_lower.get("x", default_x)
            y = entity_lower.get("y", default_y)
            z = entity_lower.get("z", default_z)
            # If all are missing, treat as all None (do not raise)
            if "x" not in entity_lower and "y" not in entity_lower and "z" not in entity_lower:
                return {"x": None, "y": None, "z": None}
            return {"x": x, "y": y, "z": z}
        
        try:
            return {"x": entity[0], "y": entity[1], "z": entity[2]}
        except (IndexError, TypeError):
            pass

        # Raise an error for unsupported types
        raise ValueError(f"Unsupported type for spatial data: {type(entity)}")

    def get_voxel_volume(self) -> float | None:
        """
        Calculate the volume of a single voxel in cubic millimeters.

        Returns:
            float | None: Voxel volume in mm³, or None if any voxel dimension is not set.
        """
        if self.voxel_size is None:
            return None

        # Check for None in components
        if self.voxel_size.x is None or self.voxel_size.y is None or self.voxel_size.z is None:
            return None

        return self.voxel_size.x * self.voxel_size.y * self.voxel_size.z

    def get_grid_extent(self) -> tuple[float, float, float] | None:
        """
        Calculate the physical extent of the grid in millimeters.

        Returns:
            Tuple[float, float, float] | None: A tuple of (width, height, depth) in mm,
                                               or None if any dimension is not set.
        """
        if self.dimension is None or self.voxel_size is None:
            return None

        # Check for None in components
        if (
            self.dimension.x is None
            or self.dimension.y is None
            or self.dimension.z is None
            or self.voxel_size.x is None
            or self.voxel_size.y is None
            or self.voxel_size.z is None
        ):
            return None

        return (
            self.dimension.x * self.voxel_size.x,
            self.dimension.y * self.voxel_size.y,
            self.dimension.z * self.voxel_size.z,
        )

    def get_total_volume(self) -> float | None:
        """
        Calculate the total volume of the grid in cubic millimeters.

        Returns:
            float | None: Total volume in mm³, or None if any dimension is not set.
        """
        extent = self.get_grid_extent()
        if extent is None:
            return None
        return extent[0] * extent[1] * extent[2]
