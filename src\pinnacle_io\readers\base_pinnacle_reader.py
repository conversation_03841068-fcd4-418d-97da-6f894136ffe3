"""
Base reader class for Pinnacle readers to reduce code duplication.
"""

import os
from typing import Any, List


class BasePinnacleReader:
    """
    Base class for Pinnacle readers that provides common file handling functionality.
    
    This class eliminates duplicate code patterns across reader classes by providing:
    - Unified file service handling (defaults to FileReader when file_service is None)
    - Common path resolution logic
    - Standardized error handling
    - Consistent file reading patterns
    """
    
    @staticmethod
    def _get_file_service(file_service: Any, file_path: str) -> Any:
        """
        Get file service, defaulting to FileReader if none provided.
        
        Args:
            file_service: Optional file service object
            file_path: Path to use as root for FileReader if needed
            
        Returns:
            File service object (provided or new FileReader)
        """
        if file_service is not None:
            return file_service
        
        # Lazy import to avoid circular dependency
        from pinnacle_io.services.file_reader import FileReader
        return FileReader(file_path)
    
    @staticmethod
    def _resolve_file_path(
        input_path: str, 
        expected_filename: str, 
        path_endings: List[str] | None = None
    ) -> tuple[str, str]:
        """
        Resolve input path into directory and filename components.
        
        Args:
            input_path: Path provided by user
            expected_filename: Default filename if not in path
            path_endings: Optional list of valid path endings to check
            
        Returns:
            Tuple of (directory_path, filename)
        """
        path_endings = path_endings or [expected_filename]
        
        # Check if path ends with any of the expected patterns
        for ending in path_endings:
            if input_path.endswith(ending):
                return os.path.split(input_path)
        
        # Path doesn't end with expected pattern, treat as directory
        return input_path, expected_filename
    
    @staticmethod
    def _read_file_lines(
        file_path: str, 
        file_name: str, 
        file_service: Any,
        encoding: str = "latin1"
    ) -> List[str]:
        """
        Read file lines using file service or FileReader.
        
        Args:
            file_path: Directory path
            file_name: File name
            file_service: Optional file service (will create FileReader if None)
            encoding: Text encoding to use
            
        Returns:
            List of file lines
            
        Raises:
            FileNotFoundError: If file doesn't exist
        """
        service = BasePinnacleReader._get_file_service(file_service, file_path)
        
        if not service.exists(file_path, file_name):
            display_path = f"{file_path}/{file_name}" if file_path else file_name
            raise FileNotFoundError(f"File not found: {display_path}")
        
        # FileReader supports encoding parameter, archive services don't
        if hasattr(service, 'root_path'):  # FileReader has root_path attribute
            with service.open_file(file_path, file_name, "r", encoding=encoding, errors="ignore") as f:
                return f.readlines()
        else:  # Archive services (TarFileReader, ZipFileReader)
            with service.open_file(file_path, file_name, "r") as f:
                # Archive services return text streams that handle encoding automatically
                return f.readlines()
    
    @staticmethod
    def _read_binary_file(
        file_path: str, 
        file_name: str, 
        file_service: Any
    ) -> bytes:
        """
        Read binary file using file service or FileReader.
        
        Args:
            file_path: Directory path
            file_name: File name
            file_service: Optional file service (will create FileReader if None)
            
        Returns:
            Binary file content
            
        Raises:
            FileNotFoundError: If file doesn't exist
        """
        service = BasePinnacleReader._get_file_service(file_service, file_path)
        
        if not service.exists(file_path, file_name):
            display_path = f"{file_path}/{file_name}" if file_path else file_name
            raise FileNotFoundError(f"File not found: {display_path}")
        
        with service.open_file(file_path, file_name, "rb") as f:
            return f.read()