"""
Reader for Pinnacle plan.PatientSetup files.
"""

from typing import Any
from pinnacle_io.models import PatientSetup
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class PatientSetupReader(BasePinnacleReader):
    """
    Reader for Pinnacle plan.PatientSetup files.
    """

    @staticmethod
    def read_from_ids(institution_id: int, patient_id: int, plan_id: int, mount_id: int = 0, file_service: Any = None) -> PatientSetup:
        """
        Read a Pinnacle plan.PatientSetup file using ID-based loading.

        Args:
            institution_id: Institution ID number
            patient_id: Patient ID number
            plan_id: Plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)
            file_service: File service object with open_file method

        Returns:
            PatientSetup model populated with data from the file
        
        Usage:
            patient_setup = PatientSetupReader.read_from_ids(1, 1, 0, file_service=file_service)
            patient_setup = PatientSetupReader.read_from_ids(1, 1, 0, 0, file_service=file_service)
        """
        # Construct standard Pinnacle path format
        plan_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"
        # Delegate to path-based method
        return PatientSetupReader.read_from_path(plan_path, file_service)

    @staticmethod
    def read_from_path(plan_path: str, file_service: Any = None) -> PatientSetup:
        """
        Read a Pinnacle plan.PatientSetup file using path-based loading.

        Args:
            plan_path: Path to the Pinnacle plan.PatientSetup file or directory
            file_service: File service object with open_file method

        Returns:
            PatientSetup model populated with data from the file
        
        Usage:
            patient_setup = PatientSetupReader.read_from_path("/path/to/Patient_0/Plan_0/plan.PatientSetup")
            patient_setup = PatientSetupReader.read_from_path("/path/to/Patient_0/Plan_0")
            patient_setup = PatientSetupReader.read_from_path("/path/to/Patient_0/Plan_0", file_service=file_service)
        """
        # Resolve file path and name with case-insensitive handling
        file_path, file_name = PatientSetupReader._resolve_file_path(
            plan_path, "plan.PatientSetup", ["plan.patientsetup", "plan.PatientSetup"]
        )
        
        # Read file content using base class utility
        content_lines = PatientSetupReader._read_file_lines(file_path, file_name, file_service)
        
        return PatientSetupReader.parse(content_lines)

    @staticmethod
    def read(plan_path: str, file_service: Any = None) -> PatientSetup:
        """
        Legacy method for backward compatibility.
        
        Deprecated: Use read_from_path() or read_from_ids() instead.
        """
        return PatientSetupReader.read_from_path(plan_path, file_service)

    @staticmethod
    def parse(content_lines: list[str]) -> PatientSetup:
        """
        Parse a Pinnacle plan.PatientSetup content string and create a PatientSetup model.

        Args:
            content_lines: Pinnacle plan.PatientSetup content lines

        Returns:
            PatientSetup model populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        patient_setup = PatientSetup(**data)
        return patient_setup
