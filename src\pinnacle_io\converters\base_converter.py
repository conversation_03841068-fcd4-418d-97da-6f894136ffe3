"""
Base converter class for SimpleDicomConverter.

This module provides the base class for all DICOM converters in the application.
"""

import logging
import os
from abc import ABC
from typing import Any, Dict, Optional

from pinnacle_io.models import ImageSet, Patient, Plan, Trial
from pinnacle_io.readers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TrialReader
from pydicom.dataset import Dataset, FileDataset, FileMetaDataset
from pydicom.uid import UID

from pinnacle_io.exceptions import (
    DataValidationError,
    DicomConversionError,
    FileReadError,
    # ConfigurationError,
    UIDGenerationError,
)
from pinnacle_io.converters.constants import (
    DICOM_EXTENSION,
    IMPLEMENTATION_CLASS_UID,
    MANUFACTURER,
    MODALITY_TO_FILE_PREFIX,
    SPECIFIC_CHARACTER_SET,
    STATION_NAME,
    TRANSFER_SYNTAX_UID,
)
from pinnacle_io.converters.uid_manager import DicomUidManager

# Setup logging
logger = logging.getLogger(__name__)


class DicomConverter(ABC):
    """
    Base class for all DICOM converters.

    This abstract class defines the common interface and functionality for
    all DICOM converters in the application. Specific converter classes for
    different DICOM modalities should inherit from this class.
    """

    MODALITY: Optional[str] = None

    def __init__(self, patient: Patient, uid_manager: Optional[DicomUidManager] = None):
        """
        Initialize the DICOM converter with a Patient model and optional UID manager.

        Args:
            patient: Patient model object.
            uid_manager: DicomUidManager instance for UID coordination across DICOM files.
                        If None, a new instance will be created.

        Raises:
            DataValidationError: If patient data is None or invalid
            UIDGenerationError: If UID manager initialization fails
        """
        try:
            # Initialize logger first so it's available for validation
            self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
            
            # Validate patient data
            self._validate_patient_data(patient)

            self.patient = patient
            self.patient_path = patient.patient_path if patient else ""
            self.patient_folder = os.path.basename(self.patient_path) if self.patient_path else ""
            self.institution_path = os.path.dirname(os.path.dirname(self.patient_path)) if self.patient_path else ""

            # Initialize UID manager for proper DICOM relationships
            try:
                self.uid_manager = uid_manager or DicomUidManager()
            except Exception as e:
                raise UIDGenerationError(
                    "Failed to initialize UID manager",
                    context={"patient_id": getattr(patient, "medical_record_number", "unknown")},
                    cause=e,
                )

            # Initialize common attributes from patient with validation
            self.first_name = patient.first_name if patient else ""
            self.middle_name = patient.middle_name if patient else ""
            self.last_name = patient.last_name if patient else ""
            self.patient_id = patient.medical_record_number if patient else ""
            self.patient_birth_date = patient.date_of_birth if patient else ""
            self.patient_sex = self._normalize_patient_sex(patient.gender) if patient and patient.gender else "O"
            self.study_date = ""
            self.study_time = ""

            # Shared UIDs for DICOM relationships - use UID manager
            self.study_instance_uid = self.uid_manager.get_study_instance_uid()
            self.series_instance_uid = ""  # Set by specific converters using uid_manager
            self.frame_of_reference_uid = self.uid_manager.get_frame_of_reference_uid()

            self.logger.debug(f"Initialized {self.__class__.__name__} for patient ID: {self.patient_id}")

        except (DataValidationError, UIDGenerationError):
            raise
        except Exception as e:
            raise DicomConversionError(
                f"Failed to initialize {self.__class__.__name__}",
                context={"patient_id": getattr(patient, "medical_record_number", "unknown") if patient else "None"},
                cause=e,
            )

    def set_shared_uids(
        self, study_uid: Optional[str] = None, series_uid: Optional[str] = None, frame_ref_uid: Optional[str] = None
    ):
        """
        Set shared UIDs for proper DICOM relationships.

        This method allows overriding the default UIDs from the UID manager if needed.
        Typically used when coordinating UIDs across multiple converters.

        Args:
            study_uid: Study Instance UID to use (if None, keeps current value)
            series_uid: Series Instance UID to use (if None, keeps current value)
            frame_ref_uid: Frame of Reference UID to use (if None, keeps current value)
        """
        if study_uid:
            self.study_instance_uid = study_uid
        if series_uid:
            self.series_instance_uid = series_uid
        if frame_ref_uid:
            self.frame_of_reference_uid = frame_ref_uid

    @classmethod
    def from_archive(cls, patient_path: str):
        """
        Create a DICOM converter instance from a Pinnacle archive path.

        Args:
            patient_path: Full path to the patient folder.

        Returns:
            DicomConverter instance initialized with patient data.
        """
        # Load patient data from the archive
        patient = PatientReader.read_from_path(patient_path)

        # Set the full patient path on the patient model
        patient.patient_path = patient_path

        # Return a new instance with the loaded patient
        return cls(patient)

    def create_file_meta(self, sop_class_uid: str, sop_instance_uid: str) -> FileMetaDataset:
        """
        Create a DICOM file meta dataset containing standard metadata required for DICOM files.

        Args:
            sop_class_uid: SOP Class UID for the DICOM file.
            sop_instance_uid: SOP Instance UID for the DICOM file.

        Returns:
            DICOM file meta dataset.
        """
        file_meta = FileMetaDataset()
        file_meta.MediaStorageSOPClassUID = UID(sop_class_uid)
        file_meta.MediaStorageSOPInstanceUID = UID(sop_instance_uid)
        file_meta.TransferSyntaxUID = UID(TRANSFER_SYNTAX_UID)
        file_meta.ImplementationClassUID = UID(IMPLEMENTATION_CLASS_UID)

        return file_meta

    def create_dataset(self, file_meta: FileMetaDataset) -> FileDataset:
        """
        Create a DICOM dataset with the given file meta information and standard attributes.

        Args:
            file_meta: DICOM file meta dataset.

        Returns:
            DICOM file dataset.
        """
        ds = FileDataset("", {}, file_meta=file_meta, preamble=b"\x00" * 128)
        ds.SpecificCharacterSet = SPECIFIC_CHARACTER_SET
        ds.Manufacturer = MANUFACTURER
        ds.StationName = STATION_NAME
        ds.SOPInstanceUID = file_meta.MediaStorageSOPInstanceUID

        return ds

    def set_common_elements(self, ds: FileDataset) -> None:
        """
        Set common DICOM elements in the dataset, such as patient and study information.

        Args:
            ds: DICOM dataset to update.
        """
        import time

        ds.PatientName = "{}^{}^{}".format(self.last_name, self.first_name, self.middle_name)
        ds.PatientID = self.patient_id
        ds.PatientBirthDate = self.patient_birth_date
        ds.PatientSex = self.patient_sex
        ds.StudyDate = self.study_date or time.strftime("%Y%m%d")
        ds.StudyTime = self.study_time or time.strftime("%H%M%S")
        ds.StudyInstanceUID = self.study_instance_uid
        ds.SeriesInstanceUID = self.series_instance_uid
        ds.FrameOfReferenceUID = self.frame_of_reference_uid
        ds.InstanceCreationDate = time.strftime("%Y%m%d")
        ds.InstanceCreationTime = time.strftime("%H%M%S")

    def _validate_patient_data(self, patient: Patient | None) -> None:
        """
        Validate patient data for DICOM conversion requirements.

        Args:
            patient: Patient model to validate

        Raises:
            DataValidationError: If required patient data is missing or invalid
        """
        if patient is None:
            raise DataValidationError("Patient data is required but was None")

        # Log validation start
        self.logger.debug("Validating patient data for DICOM conversion")

        # Check for required patient identification
        if not patient.medical_record_number:
            self.logger.warning("Patient medical record number is missing - using empty string")

        # Check for patient name components
        missing_name_parts = []
        if not patient.first_name:
            missing_name_parts.append("first_name")
        if not patient.last_name:
            missing_name_parts.append("last_name")

        if missing_name_parts:
            self.logger.warning(f"Patient name components missing: {missing_name_parts}")

        # Validate patient path if provided - use full path if available
        if hasattr(self, 'full_patient_path') and self.full_patient_path:
            if not os.path.exists(self.full_patient_path):
                self.logger.warning(f"Patient path does not exist: {self.full_patient_path}")
        elif hasattr(patient, "patient_path") and patient.patient_path:
            self.logger.debug(f"Patient path (relative): {patient.patient_path}")

        self.logger.debug("Patient data validation completed")

    def set_root_path(self, root_path: str) -> None:
        """
        Set the root path for the Pinnacle archive.
        
        This method allows setting the root path after initialization,
        which is used to construct full paths for validation.
        
        Args:
            root_path: Root path to the Pinnacle archive
        """
        self.root_path = root_path
        
        # Update patient_path to include the root path if patient_path exists
        if hasattr(self.patient, "patient_path") and self.patient.patient_path:
            import os
            self.full_patient_path = os.path.join(root_path, self.patient.patient_path)
        else:
            self.full_patient_path = None

    def _validate_required_data(self, data: Any, field_name: str, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Validate that required data is present and valid.

        Args:
            data: Data to validate
            field_name: Name of the field being validated
            context: Additional context for error reporting

        Raises:
            DataValidationError: If required data is missing or invalid
        """
        if data is None:
            raise DataValidationError(f"Required field '{field_name}' is missing", context=context or {})

    def _validate_optional_data(self, data: Any, field_name: str, default_value: Any = None) -> Any:
        """
        Validate optional data and provide graceful degradation with defaults.

        Args:
            data: Data to validate
            field_name: Name of the field being validated
            default_value: Default value to return if data is missing

        Returns:
            Original data if valid, default value if missing, or None if no default
        """
        if data is None:
            if default_value is not None:
                self.logger.debug(f"Optional field '{field_name}' missing, using default: {default_value}")
                return default_value
            else:
                self.logger.debug(f"Optional field '{field_name}' missing, no default provided")
                return None
        return data

    def _log_conversion_context(self, operation: str, **kwargs: Any) -> None:
        """
        Log conversion operation context for debugging.

        Args:
            operation: Name of the operation being performed
            **kwargs: Additional context information to log
        """
        context_info = {"patient_id": self.patient_id, "modality": self.MODALITY, "operation": operation, **kwargs}

        # Filter out None values for cleaner logging
        context_info = {k: v for k, v in context_info.items() if v is not None}

        self.logger.debug(f"Conversion context: {context_info}")

    @staticmethod
    def _normalize_patient_sex(gender: str) -> str:
        """
        Normalize patient gender to valid DICOM PatientSex values.

        DICOM PatientSex (0010,0040) should be one of: M, F, O (Other)

        Args:
            gender: Input gender string

        Returns:
            Valid DICOM PatientSex value
        """
        if not gender:
            return "O"  # Other for empty/None values

        gender_upper = gender.upper()

        # Map common gender values to DICOM codes
        if gender_upper in ["M", "MALE", "MAN"]:
            return "M"
        elif gender_upper in ["F", "FEMALE", "WOMAN"]:
            return "F"
        else:
            # For "Unknown", "Other", or any unrecognized value
            return "O"

    @staticmethod
    def get_dataset_file_name(ds: Dataset) -> str:
        """
        Generate the file name for the DICOM file. Requires the
        DICOM dataset instance Modality and SOP Instance UID attributes to be
        defined.

        Returns:
            The file name in the format: '[prefix].[sop_instance_uid].dcm'.
        """
        if not ds.SOPInstanceUID:
            raise AttributeError(
                "SOP Instance UID not defined. A dataset filename cannot be generated without the SOP Instance UID"
            )

        prefix = MODALITY_TO_FILE_PREFIX.get(ds.Modality, "")
        return f"{prefix}{ds.SOPInstanceUID}{DICOM_EXTENSION}"

    def save_dataset(self, ds: Dataset, output_path: str) -> str:
        """
        Save a DICOM dataset to a file at the specified output path.

        Args:
            ds: DICOM dataset to save. The dataset should have the SOP Instance UID and Modality defined.
            output_path: Path where the dataset will be saved.

        Returns:
            Full path of the output file

        Raises:
            DataValidationError: If dataset is missing required attributes
            FileReadError: If unable to create output directory or save file
        """
        try:
            # Validate dataset before saving
            if not hasattr(ds, "SOPInstanceUID") or not ds.SOPInstanceUID:
                raise DataValidationError(
                    "Dataset missing required SOPInstanceUID", context={"modality": getattr(ds, "Modality", "unknown")}
                )

            if not hasattr(ds, "Modality") or not ds.Modality:
                raise DataValidationError(
                    "Dataset missing required Modality",
                    context={
                        "sop_instance_uid": str(ds.SOPInstanceUID) if hasattr(ds, "SOPInstanceUID") else "unknown"
                    },
                )

            output_file = os.path.join(output_path, self.get_dataset_file_name(ds))

            self._log_conversion_context(
                "save_dataset", output_file=output_file, sop_instance_uid=str(ds.SOPInstanceUID), modality=ds.Modality
            )

            # Ensure output directory exists
            try:
                os.makedirs(output_path, exist_ok=True)
            except OSError as e:
                raise FileReadError(
                    f"Failed to create output directory: {output_path}",
                    context={"output_path": output_path, "error": str(e)},
                    cause=e,
                )

            # Save the dataset
            try:
                ds.save_as(output_file, enforce_file_format=True)
                self.logger.info(f"Successfully saved DICOM file: {output_file}")
                return output_file
            except Exception as e:
                raise FileReadError(
                    "Failed to save DICOM dataset to file",
                    context={"output_file": output_file, "modality": ds.Modality},
                    cause=e,
                )

        except (DataValidationError, FileReadError):
            raise
        except Exception as e:
            raise DicomConversionError(
                "Unexpected error during dataset save operation",
                context={"output_path": output_path, "modality": getattr(ds, "Modality", "unknown")},
                cause=e,
            )


class PlanBaseConverter(DicomConverter):
    """
    Base class for DICOM converters which depend on a plan (e.g., StructureConverter, DoseConverter).
    """

    def __init__(self, patient: Patient, uid_manager: Optional[DicomUidManager] = None):
        """
        Initialize the Plan base converter.

        Args:
            patient: Patient model object.
            uid_manager: DicomUidManager instance for UID coordination.
        """
        super().__init__(patient, uid_manager)

    @classmethod
    def from_archive(cls, patient_path: str):
        """
        Create a PlanBaseConverter instance from a Pinnacle archive path.

        Args:
            patient_path: Full path to the patient folder.

        Returns:
            PlanBaseConverter instance initialized with patient data.
        """
        return super().from_archive(patient_path)

    def get_plan(self, plan_path: str) -> Optional[Plan]:
        """
        Retrieve a plan from the patient list by path.

        This method takes the path to a plan folder and returns the corresponding
        Plan model object from the patient's plan list.

        Args:
            plan_path: Full path to the plan folder.

        Returns:
            Plan model object or None if the plan is not found.
        """
        plan_folder = os.path.basename(plan_path)
        plan = next(
            (plan for plan in self.patient.plan_list if f"Plan_{plan.plan_id}" == plan_folder),
            None,
        )
        return plan

    def load_image_set(self, image_set_id: int) -> Optional[ImageSet]:
        """
        Load an image set from the specified image set ID.

        This method takes the ID of an image set and returns the corresponding
        ImageSet model object by reading the image header file from the
        patient's directory.

        Args:
            image_set_id: ID of the image set to load.

        Returns:
            ImageSet model object or None if the image set is not found.
        """
        patient_path = self.patient_path or ""
        image_set_path = os.path.join(patient_path, f"ImageSet_{image_set_id}.header")
        return ImageSetReader.read_from_path(image_set_path)

    def load_planning_ct(self, plan_path: str) -> Optional[ImageSet]:
        """
        Load the planning CT image set for the specified plan.

        This method takes the path to a plan folder and returns the corresponding
        ImageSet model object from the patient's image set list.

        Args:
            plan_path: Full path to the plan folder.

        Returns:
            ImageSet model object or None if the image set is not found.
        """
        plan = self.get_plan(plan_path)
        if plan and plan.primary_ct_image_set_id is not None:
            return self.load_image_set(plan.primary_ct_image_set_id)
        return None


class TrialBaseConverter(PlanBaseConverter):
    """
    Base class for DICOM converters which depend on a trial (e.g., PlanConverter, DoseConverter).
    """

    def __init__(self, patient: Patient, uid_manager: Optional[DicomUidManager] = None):
        """
        Initialize the Trial base converter.

        Args:
            patient: Patient model object.
            uid_manager: DicomUidManager instance for UID coordination.
        """
        super().__init__(patient, uid_manager)

    @classmethod
    def from_archive(cls, patient_path: str):
        """
        Create a TrialBaseConverter instance from a Pinnacle archive path.

        Args:
            patient_path: Full path to the patient folder.

        Returns:
            TrialBaseConverter instance initialized with patient data.
        """
        return super().from_archive(patient_path)

    def load_trials(self, plan_path: str) -> list[Trial]:
        """
        Load trials from the specified plan path.

        Args:
            plan_path: Full path to the plan folder containing the trials.

        Returns:
            List of Trial objects loaded from the plan.

        Raises:
            FileReadError: If plan path doesn't exist or trial loading fails
        """
        try:
            if not plan_path:
                raise FileReadError("Plan path is required but was empty or None")

            if not os.path.exists(plan_path):
                raise FileReadError(f"Plan path does not exist: {plan_path}", context={"plan_path": plan_path})

            self._log_conversion_context("load_trials", plan_path=plan_path)

            trials = TrialReader.read_from_path(plan_path)

            if not trials:
                self.logger.warning(f"No trials found in plan path: {plan_path}")
                return []

            self.logger.debug(f"Successfully loaded {len(trials)} trials from {plan_path}")
            return trials

        except FileReadError:
            raise
        except Exception as e:
            raise FileReadError("Failed to load trials from plan path", context={"plan_path": plan_path}, cause=e)
