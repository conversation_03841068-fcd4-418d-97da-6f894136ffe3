"""
Custom SQLAlchemy types and spatial coordinate classes for Pinnacle data models.

This module provides:
1. Custom SQLAlchemy types for database storage
2. A hierarchy of spatial coordinate types for 3D medical imaging data
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, TypeVar, final, Optional, cast
import math

from sqlalchemy import Text
from sqlalchemy.types import TypeDecorator


class JsonList(TypeDecorator[str]):
    """
    SQLAlchemy type for storing Python lists as JSON strings in the database.

    This type handles automatic conversion between Python lists and JSON strings
    when reading from and writing to the database.

    Example:
        class MyModel(Base):
            __tablename__: str = 'my_model'

            id = Column(Integer, primary_key=True)
            tags = Column(JsonList)  # Will be stored as JSON string
    """

    impl = Text

    def load_dialect_impl(self, dialect: Any) -> Any:
        return dialect.type_descriptor(Text())

    def process_bind_param(self, value: Any, dialect: Any) -> Optional[str]:
        """
        Convert Python list to JSON string for database storage.

        Args:
            value: The Python list to convert, or None.
            dialect: The DBAPI in use.

        Returns:
            JSON string representation of the list, or None if value is None.
        """
        if value is None:
            return None
        return str(value)

    def process_result_value(self, value: Any, dialect: Any) -> Any:
        """
        Convert JSON string from database back to Python list.
        """
        if not value:
            return None
        try:
            result = eval(value) if value else None
            return result if isinstance(result, list) else None
        except (ValueError, SyntaxError):
            return None


# Type variables for generic base class
T = TypeVar("T", int, float, covariant=True)


class SpatialBase(ABC, Generic[T]):
    """
    Abstract base class for 3D spatial coordinates.

    This class provides common functionality for all spatial coordinate types
    while allowing type-specific implementations for different use cases.
    """

    __slots__ = ("_x", "_y", "_z")

    def __init__(
        self,
        x: T | None = None,
        y: T | None = None,
        z: T | None = None,
    ) -> None:
        """Initialize with x, y, z components."""
        self._x = self._validate_component("x", x)
        self._y = self._validate_component("y", y)
        self._z = self._validate_component("z", z)

    @abstractmethod
    def _validate_component(self, name: str, value: T | None) -> T | None:
        """Validate a component value. Must be implemented by subclasses."""
        pass

    @property
    def x(self) -> T | None:
        """X component of the coordinate."""
        return self._x

    # @x.setter
    # def x(self, value: T | None) -> None:
    #     self._x = self._validate_component("x", value)

    @property
    def y(self) -> T | None:
        """Y component of the coordinate."""
        return self._y

    # @y.setter
    # def y(self, value: T | None) -> None:
    #     self._y = self._validate_component("y", value)

    @property
    def z(self) -> T | None:
        """Z component of the coordinate."""
        return self._z

    # @z.setter
    # def z(self, value: T | None) -> None:
    #     self._z = self._validate_component("z", value)

    def to_dict(self) -> Dict[str, T | None]:
        """Convert to a dictionary with x, y, z keys."""
        return {"x": self.x, "y": self.y, "z": self.z}

    def to_list(self) -> list[T | None]:
        """Convert to a list [x, y, z]."""
        return [self.x, self.y, self.z]

    @classmethod
    def from_dict(cls, data: Dict[str, T | None]) -> "SpatialBase[T]":
        """Create from a dictionary with x, y, z keys."""
        return cls(
            x=data.get("x"),
            y=data.get("y"),
            z=data.get("z"),
        )

    def __eq__(self, other: object) -> bool:
        """Test equality with another coordinate."""
        if not isinstance(other, SpatialBase):
            return False
        return cast(Any, self).x == cast(Any, other).x and cast(Any, self).y == cast(Any, other).y and cast(Any, self).z == cast(Any, other).z

    def __repr__(self) -> str:
        """String representation of the coordinate."""
        return f"{self.__class__.__name__}(x={self.x}, y={self.y}, z={self.z})"


@final
class VoxelSize(SpatialBase[float]):
    """
    Represents the physical size of a voxel in millimeters.

    All components must be positive floating-point numbers.
    """

    def _validate_component(self, name: str, value: float | None) -> float | None:
        """Validate that the component is a positive number."""
        if value is None:
            return None
        val = float(value)
        if val < 0:
            raise ValueError(f"{name} must be non-negative, got {val}")
        return val

    def volume(self) -> float | None:
        """Calculate the volume of a voxel with these dimensions."""
        if self.x is None or self.y is None or self.z is None:
            return None
        return self.x * self.y * self.z


@final
class VolumeSize(SpatialBase[float]):
    """
    Represents the physical dimensions of a volume in millimeters.

    All components must be non-negative floating-point numbers.
    """

    def _validate_component(self, name: str, value: float | None) -> float | None:
        """Validate that the component is a non-negative number."""
        if value is None:
            return None
        val = float(value)
        if val < 0:
            raise ValueError(f"{name} must be non-negative, got {val}")
        return val

    def volume(self) -> float | None:
        """Calculate the total volume."""
        if self.x is None or self.y is None or self.z is None:
            return None
        return self.x * self.y * self.z


@final
class Coordinate(SpatialBase[float]):
    """
    Represents a physical point in 3D space in millimeters.

    No constraints on component values.
    """

    def _validate_component(self, name: str, value: float | None) -> float | None:
        """Convert to float with no additional validation."""
        if value is None:
            return None
        return float(value)

    def distance_to(self, other: "Coordinate") -> float | None:
        """Calculate Euclidean distance to another coordinate."""
        if self.x is None or self.y is None or self.z is None or other.x is None or other.y is None or other.z is None:
            return None
        dx = self.x - other.x
        dy = self.y - other.y
        dz = self.z - other.z
        return math.sqrt(dx * dx + dy * dy + dz * dz)

    def __add__(self, other: "Coordinate") -> "Coordinate":
        """Add two coordinates component-wise."""
        # The isinstance check is required for correct operator overloading (see Python docs),
        # so that NotImplemented is returned and TypeError is raised for unsupported types.
        if not isinstance(other, Coordinate):  #  type: ignore
            return NotImplemented
        return Coordinate(
            x=self.x + other.x if self.x is not None and other.x is not None else None,
            y=self.y + other.y if self.y is not None and other.y is not None else None,
            z=self.z + other.z if self.z is not None and other.z is not None else None,
        )

    def __mul__(self, scalar: float) -> "Coordinate":
        """Multiply coordinate by a scalar."""
        return Coordinate(
            x=self.x * scalar if self.x is not None else None,
            y=self.y * scalar if self.y is not None else None,
            z=self.z * scalar if self.z is not None else None,
        )

    __rmul__ = __mul__  # Allow scalar * coordinate


@final
class Index(SpatialBase[int]):
    """
    Represents discrete array indices for voxel access.

    All components must be non-negative integers.
    """

    def _validate_component(self, name: str, value: int | None) -> int | None:
        """Validate that the component is a non-negative integer."""
        if value is None:
            return None
        val = int(value)
        if val < 0:
            raise ValueError(f"{name} must be non-negative, got {val}")
        return val

    def to_continuous(self) -> "ContinuousIndex":
        """Convert to a ContinuousIndex."""
        return ContinuousIndex(
            float(self.x) if self.x is not None else None,
            float(self.y) if self.y is not None else None,
            float(self.z) if self.z is not None else None,
        )


@final
class ContinuousIndex(SpatialBase[float]):
    """
    Represents precise sub-voxel positions in array space.

    No constraints on component values.
    """

    def _validate_component(self, name: str, value: float | None) -> float | None:
        """Convert to float with no additional validation."""
        if value is None:
            return None
        return float(value)

    def to_index(self) -> Index:
        """Convert to the nearest Index (rounding down)."""
        return Index(
            int(self.x) if self.x is not None else None,
            int(self.y) if self.y is not None else None,
            int(self.z) if self.z is not None else None,
        )

    def rounded(self) -> "ContinuousIndex":
        """Return a new ContinuousIndex with rounded components."""
        return ContinuousIndex(
            round(self.x) if self.x is not None else None,
            round(self.y) if self.y is not None else None,
            round(self.z) if self.z is not None else None,
        )


@final
class Dimension(SpatialBase[int]):
    """
    Represents the number of voxels in each dimension of a 3D array.

    All components must be positive integers.
    """

    def _validate_component(self, name: str, value: int | None) -> int | None:
        """Validate that the component is a positive integer."""
        if value is None:
            return None
        val = int(value)
        if val <= 0:
            raise ValueError(f"{name} must be positive, got {val}")
        return val

    def num_voxels(self) -> int | None:
        """Calculate the total number of voxels."""
        if self.x is None or self.y is None or self.z is None:
            return None
        return self.x * self.y * self.z

    def to_volume_size(self, voxel_size: VoxelSize) -> VolumeSize:
        """Convert to physical size using the given voxel dimensions."""
        if self.x is None or self.y is None or self.z is None or voxel_size.x is None or voxel_size.y is None or voxel_size.z is None:
            return VolumeSize(None, None, None)
        return VolumeSize(
            x=self.x * voxel_size.x,
            y=self.y * voxel_size.y,
            z=self.z * voxel_size.z,
        )

    def contains(self, index: Index) -> bool:
        """Check if the given index is within these dimensions."""
        if self.x is None or self.y is None or self.z is None or index.x is None or index.y is None or index.z is None:
            return False
        return 0 <= index.x < self.x and 0 <= index.y < self.y and 0 <= index.z < self.z
