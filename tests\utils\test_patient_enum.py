"""
Tests for pinnacle_io.utils.patient_enum module.
"""

import pytest
from pinnacle_io.utils.patient_enum import (
    PatientPositionEnum,
    PatientOrientationEnum,
    TableMotionEnum,
    PatientSetupEnum,
)


class TestPatientPositionEnum:
    """Test cases for PatientPositionEnum."""

    def test_enum_values(self):
        """Test that enum values are correctly defined."""
        assert PatientPositionEnum.Supine == "On back (supine)"
        assert PatientPositionEnum.Prone == "On front (prone)"
        assert PatientPositionEnum.DecubitusRight == "Right side down"
        assert PatientPositionEnum.DecubitusLeft == "Left side down"
        assert PatientPositionEnum.Unknown == "Unknown"

    def test_enum_inheritance(self):
        """Test that enum inherits from str."""
        assert isinstance(PatientPositionEnum.Supine, str)
        assert isinstance(PatientPositionEnum.Prone, str)

    def test_enum_membership(self):
        """Test enum membership."""
        assert PatientPositionEnum.Supine in PatientPositionEnum
        assert PatientPositionEnum.Prone in PatientPositionEnum
        assert PatientPositionEnum.DecubitusRight in PatientPositionEnum
        assert PatientPositionEnum.DecubitusLeft in PatientPositionEnum
        assert PatientPositionEnum.Unknown in PatientPositionEnum

    def test_enum_iteration(self):
        """Test that all enum values can be iterated."""
        values = list(PatientPositionEnum)
        assert len(values) == 5
        assert PatientPositionEnum.Supine in values
        assert PatientPositionEnum.Prone in values
        assert PatientPositionEnum.DecubitusRight in values
        assert PatientPositionEnum.DecubitusLeft in values
        assert PatientPositionEnum.Unknown in values


class TestPatientOrientationEnum:
    """Test cases for PatientOrientationEnum."""

    def test_enum_values(self):
        """Test that enum values are correctly defined."""
        assert PatientOrientationEnum.HeadFirst == "Head First Into Scanner"
        assert PatientOrientationEnum.FeetFirst == "Feet First Into Scanner"
        assert PatientOrientationEnum.Unknown == "Unknown"

    def test_enum_inheritance(self):
        """Test that enum inherits from str."""
        assert isinstance(PatientOrientationEnum.HeadFirst, str)
        assert isinstance(PatientOrientationEnum.FeetFirst, str)

    def test_enum_membership(self):
        """Test enum membership."""
        assert PatientOrientationEnum.HeadFirst in PatientOrientationEnum
        assert PatientOrientationEnum.FeetFirst in PatientOrientationEnum
        assert PatientOrientationEnum.Unknown in PatientOrientationEnum

    def test_enum_iteration(self):
        """Test that all enum values can be iterated."""
        values = list(PatientOrientationEnum)
        assert len(values) == 3
        assert PatientOrientationEnum.HeadFirst in values
        assert PatientOrientationEnum.FeetFirst in values
        assert PatientOrientationEnum.Unknown in values


class TestTableMotionEnum:
    """Test cases for TableMotionEnum."""

    def test_enum_values(self):
        """Test that enum values are correctly defined."""
        assert TableMotionEnum.IntoScanner == "Table Moves Into Scanner"
        assert TableMotionEnum.OutOfScanner == "Table Moves Out Of Scanner"
        assert TableMotionEnum.Unknown == "Unknown"

    def test_enum_inheritance(self):
        """Test that enum inherits from str."""
        assert isinstance(TableMotionEnum.IntoScanner, str)
        assert isinstance(TableMotionEnum.OutOfScanner, str)

    def test_enum_membership(self):
        """Test enum membership."""
        assert TableMotionEnum.IntoScanner in TableMotionEnum
        assert TableMotionEnum.OutOfScanner in TableMotionEnum
        assert TableMotionEnum.Unknown in TableMotionEnum

    def test_enum_iteration(self):
        """Test that all enum values can be iterated."""
        values = list(TableMotionEnum)
        assert len(values) == 3
        assert TableMotionEnum.IntoScanner in values
        assert TableMotionEnum.OutOfScanner in values
        assert TableMotionEnum.Unknown in values


class TestPatientSetupEnum:
    """Test cases for PatientSetupEnum."""

    def test_enum_values(self):
        """Test that enum values are correctly defined."""
        assert PatientSetupEnum.HFS == "HFS"
        assert PatientSetupEnum.HFP == "HFP"
        assert PatientSetupEnum.FFS == "FFS"
        assert PatientSetupEnum.FFP == "FFP"
        assert PatientSetupEnum.HFDR == "HFDR"
        assert PatientSetupEnum.HFDL == "HFDL"
        assert PatientSetupEnum.FFDR == "FFDR"
        assert PatientSetupEnum.FFDL == "FFDL"
        assert PatientSetupEnum.Unknown == "Unknown"

    def test_enum_inheritance(self):
        """Test that enum inherits from str."""
        assert isinstance(PatientSetupEnum.HFS, str)
        assert isinstance(PatientSetupEnum.HFP, str)

    def test_enum_membership(self):
        """Test enum membership."""
        assert PatientSetupEnum.HFS in PatientSetupEnum
        assert PatientSetupEnum.HFP in PatientSetupEnum
        assert PatientSetupEnum.FFS in PatientSetupEnum
        assert PatientSetupEnum.FFP in PatientSetupEnum
        assert PatientSetupEnum.HFDR in PatientSetupEnum
        assert PatientSetupEnum.HFDL in PatientSetupEnum
        assert PatientSetupEnum.FFDR in PatientSetupEnum
        assert PatientSetupEnum.FFDL in PatientSetupEnum
        assert PatientSetupEnum.Unknown in PatientSetupEnum

    def test_enum_iteration(self):
        """Test that all enum values can be iterated."""
        values = list(PatientSetupEnum)
        assert len(values) == 9
        expected_values = [
            PatientSetupEnum.HFS, PatientSetupEnum.HFP, PatientSetupEnum.FFS,
            PatientSetupEnum.FFP, PatientSetupEnum.HFDR, PatientSetupEnum.HFDL,
            PatientSetupEnum.FFDR, PatientSetupEnum.FFDL, PatientSetupEnum.Unknown
        ]
        for value in expected_values:
            assert value in values


class TestPatientSetupEnumClassMethods:
    """Test cases for PatientSetupEnum class methods."""

    def test_from_orientation_and_position_valid_combinations(self):
        """Test valid orientation and position combinations."""
        test_cases = [
            (PatientOrientationEnum.HeadFirst, PatientPositionEnum.Supine, PatientSetupEnum.HFS),
            (PatientOrientationEnum.HeadFirst, PatientPositionEnum.Prone, PatientSetupEnum.HFP),
            (PatientOrientationEnum.FeetFirst, PatientPositionEnum.Supine, PatientSetupEnum.FFS),
            (PatientOrientationEnum.FeetFirst, PatientPositionEnum.Prone, PatientSetupEnum.FFP),
            (PatientOrientationEnum.HeadFirst, PatientPositionEnum.DecubitusRight, PatientSetupEnum.HFDR),
            (PatientOrientationEnum.HeadFirst, PatientPositionEnum.DecubitusLeft, PatientSetupEnum.HFDL),
            (PatientOrientationEnum.FeetFirst, PatientPositionEnum.DecubitusRight, PatientSetupEnum.FFDR),
            (PatientOrientationEnum.FeetFirst, PatientPositionEnum.DecubitusLeft, PatientSetupEnum.FFDL),
        ]

        for orientation, position, expected in test_cases:
            result = PatientSetupEnum.from_orientation_and_position(orientation, position)
            assert result == expected, f"Failed for {orientation} + {position}"

    def test_from_orientation_and_position_unknown_orientation(self):
        """Test with unknown orientation."""
        result = PatientSetupEnum.from_orientation_and_position(
            PatientOrientationEnum.Unknown, PatientPositionEnum.Supine
        )
        assert result == PatientSetupEnum.Unknown

    def test_from_orientation_and_position_unknown_position(self):
        """Test with unknown position."""
        result = PatientSetupEnum.from_orientation_and_position(
            PatientOrientationEnum.HeadFirst, PatientPositionEnum.Unknown
        )
        assert result == PatientSetupEnum.Unknown

    def test_from_orientation_and_position_both_unknown(self):
        """Test with both orientation and position unknown."""
        result = PatientSetupEnum.from_orientation_and_position(
            PatientOrientationEnum.Unknown, PatientPositionEnum.Unknown
        )
        assert result == PatientSetupEnum.Unknown

    def test_from_orientation_and_position_return_type(self):
        """Test that the method returns the correct type."""
        result = PatientSetupEnum.from_orientation_and_position(
            PatientOrientationEnum.HeadFirst, PatientPositionEnum.Supine
        )
        assert isinstance(result, PatientSetupEnum)
        assert result == PatientSetupEnum.HFS

    def test_from_orientation_and_position_internal_mapping(self):
        """Test that internal mappings are correct."""
        # Test the orientation mapping
        orientation_cases = [
            (PatientOrientationEnum.HeadFirst, "HF"),
            (PatientOrientationEnum.FeetFirst, "FF"),
        ]

        # Test the position mapping
        position_cases = [
            (PatientPositionEnum.Supine, "S"),
            (PatientPositionEnum.Prone, "P"),
            (PatientPositionEnum.DecubitusRight, "DR"),
            (PatientPositionEnum.DecubitusLeft, "DL"),
        ]

        # Verify all combinations work as expected
        for orientation, orientation_tag in orientation_cases:
            for position, position_tag in position_cases:
                expected_value = orientation_tag + position_tag
                result = PatientSetupEnum.from_orientation_and_position(orientation, position)
                assert result.value == expected_value


class TestEnumStringComparison:
    """Test string comparison capabilities of enums."""

    def test_patient_position_string_comparison(self):
        """Test that enum values can be compared with strings."""
        assert PatientPositionEnum.Supine == "On back (supine)"
        assert PatientPositionEnum.Prone == "On front (prone)"
        assert PatientPositionEnum.Supine.value == "On back (supine)"

    def test_patient_orientation_string_comparison(self):
        """Test that enum values can be compared with strings."""
        assert PatientOrientationEnum.HeadFirst == "Head First Into Scanner"
        assert PatientOrientationEnum.FeetFirst == "Feet First Into Scanner"
        assert PatientOrientationEnum.HeadFirst.value == "Head First Into Scanner"

    def test_table_motion_string_comparison(self):
        """Test that enum values can be compared with strings."""
        assert TableMotionEnum.IntoScanner == "Table Moves Into Scanner"
        assert TableMotionEnum.OutOfScanner == "Table Moves Out Of Scanner"
        assert TableMotionEnum.IntoScanner.value == "Table Moves Into Scanner"

    def test_patient_setup_string_comparison(self):
        """Test that enum values can be compared with strings."""
        assert PatientSetupEnum.HFS == "HFS"
        assert PatientSetupEnum.HFP == "HFP"
        assert PatientSetupEnum.HFS.value == "HFS"