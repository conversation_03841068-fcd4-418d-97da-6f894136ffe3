from typing import Dict, Optional

from pydicom.uid import generate_uid


class DicomUidManager:
    """
    Centralized UID manager for DICOM file generation.
    Ensures consistent sharing of StudyInstanceUID, FrameOfReferenceUID, SeriesInstanceUID, and SOPInstanceUIDs
    across all DICOM files (CT, RTSTRUCT, RTPLAN, RTDOSE) for a given export.
    
    Can preserve original UIDs from Pinnacle ImageSet data to maintain proper DICOM relationships
    and ensure compatibility with PACS systems.
    """

    def __init__(self, study_instance_uid: Optional[str] = None, frame_of_reference_uid: Optional[str] = None):
        """
        Initialize the UID manager.
        
        Args:
            study_instance_uid: Existing Study Instance UID to preserve (e.g., from Pinnacle ImageSet).
                               If None, a new UID will be generated.
            frame_of_reference_uid: Existing Frame of Reference UID to preserve (e.g., from Pinnacle ImageSet).
                                   If None, a new UID will be generated.
        """
        self.study_instance_uid: str = study_instance_uid or generate_uid()
        self.frame_of_reference_uid: str = frame_of_reference_uid or generate_uid()
        self._series_uids: Dict[str, str] = {}  # modality -> series UID
        self._sop_instance_uids: Dict[str, Dict[int, str]] = {}  # modality -> {index: sop UID}

    def get_study_instance_uid(self) -> str:
        """Return the StudyInstanceUID for this export."""
        return self.study_instance_uid

    def get_frame_of_reference_uid(self) -> str:
        """Return the FrameOfReferenceUID for this export."""
        return self.frame_of_reference_uid

    def get_series_instance_uid(self, modality: str, index: int = 0, preserve_uid: Optional[str] = None) -> str:
        """
        Get or generate a SeriesInstanceUID for a given modality (and optional index for multiple series).
        
        Args:
            modality: DICOM modality (e.g., "CT", "RTSTRUCT", "RTPLAN", "RTDOSE")
            index: Index for multiple series of the same modality (default: 0)
            preserve_uid: Existing Series Instance UID to preserve (e.g., from Pinnacle ImageSet)
        
        Returns:
            Series Instance UID for the specified modality and index
        """
        key = f"{modality}_{index}"
        if key not in self._series_uids:
            if preserve_uid:
                self._series_uids[key] = preserve_uid
            else:
                self._series_uids[key] = generate_uid()
        return self._series_uids[key]

    def get_sop_instance_uid(self, modality: str, index: int = 0) -> str:
        """
        Get or generate a SOPInstanceUID for a given modality and index (e.g., CT slice, plan, etc).
        """
        if modality not in self._sop_instance_uids:
            self._sop_instance_uids[modality] = {}
        if index not in self._sop_instance_uids[modality]:
            self._sop_instance_uids[modality][index] = generate_uid()
        return self._sop_instance_uids[modality][index]

    def reset(self):
        """
        Reset all UIDs except the StudyInstanceUID and FrameOfReferenceUID.
        Useful for starting a new export within the same session.
        """
        self._series_uids.clear()
        self._sop_instance_uids.clear()
