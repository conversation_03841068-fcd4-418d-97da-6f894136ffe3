"""
Tests for the PointReader (extracted from test_point.py).
"""

from pathlib import Path
import pytest

from pinnacle_io.readers.point_reader import PointReader
from pinnacle_io.models import Point
from pinnacle_io.services.file_reader import FileReader


def test_read_point_file():
    """Tests reading a valid Points file using direct path (backward compatibility)."""
    points = PointReader.read_from_path(plan_path=str(Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"))

    assert isinstance(points, list)
    assert all(isinstance(point, Point) for point in points)

    # Check point data
    assert len(points) == 1
    point = points[0]

    assert point.name == "iso"
    assert point.x_coord == -1.20199
    assert point.y_coord == 1.89459
    assert point.z_coord == 2.0
    assert point.radius == 1.0
    assert point.color == "red"
    assert point.coord_sys == "CT"
    assert point.coordinate_format == "%6.2f"
    assert point.display_2d == "Off"
    assert point.display_3d == "Off"
    assert point.volume_name == "LAST^FIRST^M"
    assert point.poi_interpreted_type == "MARKER"
    assert point.poi_display_on_other_volumes == 1
    assert point.is_locked == 0

    # Check version information
    assert point.write_version == "Pinnacle v16.0"
    assert point.create_version == "Pinnacle v16.0"
    assert point.version_login_name == "candor01"
    assert point.create_time_stamp.strftime("%Y-%m-%d %H:%M:%S") == "2020-01-01 10:00:00"
    assert point.write_time_stamp.strftime("%Y-%m-%d %H:%M:%S") == "2020-01-01 10:00:00"


def test_read_point_file_with_service():
    """Tests reading a valid Points file using file service."""
    plan_path = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"
    file_service = FileReader(str(plan_path))
    points = PointReader.read_from_path(plan_path="", file_service=file_service)

    assert isinstance(points, list)
    assert all(isinstance(point, Point) for point in points)

    # Check point data
    assert len(points) == 1
    point = points[0]
    assert point.name == "iso"
