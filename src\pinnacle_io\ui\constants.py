"""
This module contains constants used in the UI.
"""

DEFAULT_WINDOW_WIDTH = 1400
DEFAULT_WINDOW_LEVEL = 1000

pinnacle_rgb_colors = {
    'blue': (0, 0, 255),
    'brick': (142, 35, 35),
    'brown': (139, 69, 19),
    'dark green': (0, 100, 0),
    'gold': (255, 193, 37),
    'green': (0, 255, 0),
    'forest': (34, 139, 34),
    'light blue': (191, 239, 255),
    'light pink': (255, 192, 203),
    'light purple': (221, 160, 221),
    'maroon': (139, 28, 98),
    'olive': (107, 142, 35),
    'orange': (255, 140, 0),
    'lightorange': (255, 165, 0),
    'peach': (245, 204, 176),
    'skin': (245, 204, 176),
    'pink': (255, 20, 147),
    'purple': (125, 38, 205),
    'red': (255, 0, 0),
    'sky blue': (0, 191, 255),
    'tomato': (255, 99, 71),
    'turquoise': (72, 209, 204),
    'yellow': (255, 255, 0),
    'spectrum': (255, 255, 255),
    'Fusion_Red': (255, 0, 0),
    'SUV2': (255, 255, 0),
    'SUV3': (255, 255, 0),
    'rainbow2': (255, 0, 0),
    'GEM': (255, 0, 0),
    'Smart': (255, 255, 255),
    'greyscale': (155, 155, 155),
    'inverse_grey': (155, 155, 155),
    'Thermal': (255, 140, 0),
}

# Create a copy of pinnacle_rgb_colors with RGB values converted to hex
pinnacle_hex_colors = {k: '#{:02x}{:02x}{:02x}'.format(*v) for k, v in pinnacle_rgb_colors.items()}

def get_pinnacle_hex_color(color_name: str) -> str:
    """
    Get the hex color value for a given color name.
    
    Args:
        color_name: Name of the color
    
    Returns:
        Hex color value
    """
    # If it's already a hex color, return it as is
    if color_name.startswith('#'):
        return color_name
    elif color_name in pinnacle_hex_colors:
        return pinnacle_hex_colors[color_name]
    
    # If it's not hex, try to convert from RGB values
    return "#ff0000"  # Default to red if parsing fails
