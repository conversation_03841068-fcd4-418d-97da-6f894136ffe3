"""
DICOM Export Service for SimpleDicomConverter.

This module provides a service layer that orchestrates the use of existing converter classes
to export DICOM files. It eliminates the pymedphys dependency and uses the proven converter
infrastructure already present in the codebase.

Key Classes:
    - DicomExportConfig: Configuration for DICOM export operations
    - DicomExportResult: Result of DICOM export operations
    - DicomExportService: Main service for exporting DICOM files

Typical Usage:
    export_service = DicomExportService(file_service)
    export_config = DicomExportConfig(
        output_directory="/path/to/output",
        modalities=['CT', 'RTSTRUCT', 'RTPLAN', 'RTDOSE'],
        validate_output=True
    )

    result = export_service.export_dicom_files(
        institution, patient, plan, trial, export_config
    )

    if result.success:
        print(f"Exported {len(result.files)} DICOM files")
    else:
        print(f"Export failed: {result.error}")
"""

import logging
import os
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional

from pinnacle_io.models import Institution, Patient, Plan, Trial

from pinnacle_io.converters.dose_converter import DoseConverter
from pinnacle_io.converters.image_converter import ImageConverter
from pinnacle_io.converters.plan_converter import PlanConverter
from pinnacle_io.converters.structure_converter import StructureConverter
from pinnacle_io.converters.uid_manager import DicomUidManager

logger = logging.getLogger(__name__)


@dataclass
class DicomExportConfig:
    """Configuration for DICOM export operations."""

    output_directory: str
    modalities: List[str] = field(default_factory=lambda: ["CT", "RTSTRUCT", "RTPLAN", "RTDOSE"])
    validate_output: bool = True
    overwrite_existing: bool = False
    create_directories: bool = True


@dataclass
class DicomExportResult:
    """Result of DICOM export operation."""

    success: bool
    files: Dict[str, str] = field(default_factory=dict)  # modality -> file_path
    warnings: List[str] = field(default_factory=list)
    error: Optional[str] = None
    exported_modalities: List[str] = field(default_factory=list)
    failed_modalities: List[str] = field(default_factory=list)


class DicomExportService:
    """
    Service for exporting DICOM files using existing converter classes.

    This service orchestrates the use of ImageConverter, StructureConverter,
    PlanConverter, and DoseConverter to export DICOM files from Pinnacle data
    without relying on pymedphys.
    """

    # Valid DICOM modalities supported by this service
    SUPPORTED_MODALITIES = {"CT", "RTSTRUCT", "RTPLAN", "RTDOSE"}

    def __init__(self, file_service: Any) -> None:
        """
        Initialize the DICOM export service.

        Args:
            file_service: File service for accessing Pinnacle data files
        """
        self.file_service = file_service
        self.uid_manager = DicomUidManager()
        self.logger = logging.getLogger(__name__)
        # Store the pinnacle_reader if file_service has one (from DicomExportManager)
        self.pinnacle_reader = getattr(file_service, 'pinnacle_reader', None)

    def export_dicom_files(
        self,
        institution: Optional[Institution],
        patient: Optional[Patient],
        plan: Optional[Plan],
        trial: Optional[Trial],
        config: Optional[DicomExportConfig],
    ) -> DicomExportResult:
        """
        Export DICOM files using existing converter infrastructure.

        This method orchestrates the conversion of Pinnacle data to DICOM format
        using the proven converter classes. It handles UID management to ensure
        proper DICOM relationships between files.

        Args:
            institution: Institution model object
            patient: Patient model object
            plan: Plan model object
            trial: Trial model object
            config: Export configuration

        Returns:
            DicomExportResult containing success status, file paths, and any warnings/errors
        """
        result = DicomExportResult(success=False)

        try:
            # Validate configuration and requirements
            validation_result = self._validate_export_requirements(institution, patient, plan, trial, config)
            if not validation_result.success:
                result.error = validation_result.error
                result.warnings.extend(validation_result.warnings)
                return result

            # Prepare output directory
            if not (config and config.output_directory):
                result.success = False
                result.error = "Output directory is required for DICOM export"
                return result

            output_path = Path(config.output_directory)
            if config.create_directories:
                output_path.mkdir(parents=True, exist_ok=True)

            # Extract original UIDs from Pinnacle ImageSet if available (for planning CT)
            original_study_uid = None
            original_frame_ref_uid = None
            original_series_uid = None

            try:
                planning_ct = self.file_service.get_image_set(institution, patient, plan)
                if planning_ct:
                    original_study_uid = planning_ct.study_instance_uid
                    original_frame_ref_uid = planning_ct.frame_of_reference_uid
                    original_series_uid = planning_ct.series_uid

                    self.logger.info("Preserving original UIDs from Pinnacle ImageSet:")
                    self.logger.info(f"  Study UID: {original_study_uid}")
                    self.logger.info(f"  Frame Ref UID: {original_frame_ref_uid}")
                    self.logger.info(f"  Series UID: {original_series_uid}")
            except Exception as e:
                self.logger.warning(f"Could not load ImageSet to extract original UIDs: {e}")

            # Initialize UID manager with original UIDs if available
            if original_study_uid and original_frame_ref_uid:
                self.logger.info("Using original Pinnacle UIDs for DICOM export")
                self.uid_manager = DicomUidManager(
                    study_instance_uid=original_study_uid, frame_of_reference_uid=original_frame_ref_uid
                )
            else:
                self.logger.info("No original UIDs available, using existing UID manager")

            # Generate shared UIDs for proper DICOM relationships (will use original if set above)
            study_uid = self.uid_manager.get_study_instance_uid()
            frame_ref_uid = self.uid_manager.get_frame_of_reference_uid()

            self.logger.info(f"Starting DICOM export with study UID: {study_uid}")

            # Export each requested modality
            for modality in config.modalities:
                if modality not in self.SUPPORTED_MODALITIES:
                    warning = f"Unsupported modality '{modality}' skipped"
                    result.warnings.append(warning)
                    self.logger.warning(warning)
                    continue

                try:
                    exported_files = self._export_modality(
                        modality, institution, patient, plan, trial, study_uid, frame_ref_uid, str(output_path)
                    )

                    if exported_files:
                        result.files.update(exported_files)
                        result.exported_modalities.append(modality)
                        self.logger.info(f"Successfully exported {modality}: {list(exported_files.keys())}")
                    else:
                        result.failed_modalities.append(modality)
                        warning = f"No files exported for modality {modality}"
                        result.warnings.append(warning)
                        self.logger.warning(warning)

                except Exception as e:
                    result.failed_modalities.append(modality)
                    error_msg = f"Failed to export {modality}: {str(e)}"
                    result.warnings.append(error_msg)
                    self.logger.error(error_msg, exc_info=True)

            # Determine overall success
            result.success = len(result.exported_modalities) > 0

            if result.success:
                self.logger.info(f"Export completed: {len(result.exported_modalities)} modalities exported")
            else:
                result.error = "No modalities were successfully exported"
                self.logger.error(result.error)

            # Validate output if requested
            if config.validate_output and result.success:
                self._validate_dicom_output(result.files, result)

            return result

        except Exception as e:
            result.success = False
            result.error = f"Unexpected error during DICOM export: {str(e)}"
            self.logger.error(result.error, exc_info=True)
            return result

    def _validate_export_requirements(
        self,
        institution: Optional[Institution],  # Currently unused but kept for future expansion
        patient: Optional[Patient],
        plan: Optional[Plan],
        trial: Optional[Trial],
        config: Optional[DicomExportConfig],
    ) -> DicomExportResult:
        """
        Validate that all requirements are met for DICOM export.

        Args:
            institution: Institution model object (reserved for future use)
            patient: Patient model object
            plan: Plan model object
            trial: Trial model object
            config: Export configuration

        Returns:
            DicomExportResult indicating validation success/failure
        """
        result = DicomExportResult(success=True)

        # Check required objects
        if not patient:
            result.success = False
            result.error = "Patient object is required for DICOM export"
            return result

        if not plan:
            result.success = False
            result.error = "Plan object is required for DICOM export"
            return result

        # Check output directory
        if not (config and config.output_directory):
            result.success = False
            result.error = "Output directory is required for DICOM export"
            return result

        output_path = Path(config.output_directory)
        if output_path.exists() and not output_path.is_dir():
            result.success = False
            result.error = f"Output path exists but is not a directory: {config.output_directory}"
            return result

        # Check write permissions
        try:
            if not output_path.exists():
                if config.create_directories:
                    output_path.mkdir(parents=True, exist_ok=True)
                else:
                    result.success = False
                    result.error = f"Output directory does not exist: {config.output_directory}"
                    return result

            # Test write permissions
            test_file = output_path / ".write_test"
            test_file.touch()
            test_file.unlink()

        except Exception as e:
            result.success = False
            result.error = f"Cannot write to output directory: {str(e)}"
            return result

        # Validate modality-specific requirements
        for modality in config.modalities:
            if modality not in self.SUPPORTED_MODALITIES:
                continue

            modality_check = self._check_modality_requirements(modality, plan, trial)
            if not modality_check.success:
                result.warnings.extend(modality_check.warnings)

        return result

    def _check_modality_requirements(self, modality: str, plan: Plan, trial: Trial) -> DicomExportResult:
        """
        Check requirements for a specific modality.

        Args:
            modality: DICOM modality to check
            plan: Plan model object
            trial: Trial model object

        Returns:
            DicomExportResult indicating if requirements are met
        """
        result = DicomExportResult(success=True)

        if modality == "CT":
            if not hasattr(plan, "primary_ct_image_set") or not plan.primary_ct_image_set:
                result.warnings.append("No image sets available for CT export")
        elif modality == "RTSTRUCT":
            if not hasattr(plan, "roi_list") or not plan.roi_list:
                result.warnings.append("No ROI data available for RTSTRUCT export")
        elif modality == "RTPLAN":
            if not trial:
                result.warnings.append("No trial data available for RTPLAN export")
        elif modality == "RTDOSE":
            if not trial:
                result.warnings.append("No trial data available for RTDOSE export")
            # Additional dose-specific checks could be added here

        return result

    def _export_modality(
        self,
        modality: str,
        institution: Institution,  # Currently unused but kept for future expansion
        patient: Patient,
        plan: Plan,
        trial: Trial,
        study_uid: str,
        frame_ref_uid: str,
        output_path: str,
    ) -> Dict[str, str]:
        """
        Export a specific DICOM modality.

        Args:
            modality: DICOM modality to export
            institution: Institution model object (reserved for future use)
            patient: Patient model object
            plan: Plan model object
            trial: Trial model object
            study_uid: Study instance UID
            frame_ref_uid: Frame of reference UID
            output_path: Output directory path

        Returns:
            Dictionary mapping file identifiers to file paths
        """
        exported_files = {}

        if modality == "CT":
            exported_files.update(self._export_ct_images(patient, plan, study_uid, frame_ref_uid, output_path))
        elif modality == "RTSTRUCT":
            exported_files.update(self._export_structure_set(patient, plan, study_uid, frame_ref_uid, output_path))
        elif modality == "RTPLAN":
            exported_files.update(self._export_plan(patient, plan, trial, study_uid, frame_ref_uid, output_path))
        elif modality == "RTDOSE":
            exported_files.update(self._export_dose(patient, plan, trial, study_uid, frame_ref_uid, output_path))

        return exported_files

    def _export_ct_images(
        self, patient: Patient, plan: Plan, study_uid: str, frame_ref_uid: str, output_path: str
    ) -> Dict[str, str]:
        """
        Export CT images using ImageConverter.

        Args:
            patient: Patient model object
            plan: Plan model object
            study_uid: Study instance UID
            frame_ref_uid: Frame of reference UID
            output_path: Output directory path

        Returns:
            Dictionary mapping CT slice identifiers to file paths
        """
        exported_files = {}

        if not hasattr(plan, "primary_ct_image_set") or not plan.primary_ct_image_set:
            self.logger.warning("No image sets available for CT export")
            return exported_files

        # Use the first (primary) image set
        planning_ct = plan.primary_ct_image_set

        try:
            image_converter = ImageConverter(patient, self.uid_manager)
            
            # Set root path if we have access to the PinnacleReader
            if self.pinnacle_reader and hasattr(self.pinnacle_reader, 'path'):
                image_converter.set_root_path(str(self.pinnacle_reader.path))

            # Check if we have original series UID preserved in UID manager
            original_series_uid = planning_ct.series_uid if planning_ct else None
            if original_series_uid:
                preserved_series_uid = self.uid_manager.get_series_instance_uid("CT", 0, original_series_uid)
                image_converter.set_shared_uids(study_uid, series_uid=preserved_series_uid, frame_ref_uid=frame_ref_uid)
                self.logger.info(f"Using original CT Series UID: {preserved_series_uid}")
            else:
                image_converter.set_shared_uids(study_uid, frame_ref_uid=frame_ref_uid)

            ct_datasets = image_converter.convert_from_models(planning_ct)

            for i, ct_ds in enumerate(ct_datasets):
                file_path = image_converter.save_dataset(ct_ds, output_path)
                exported_files[f"CT_slice_{i:03d}"] = file_path

        except Exception as e:
            self.logger.error(f"Error exporting CT images: {e}", exc_info=True)
            raise

        return exported_files

    def _export_structure_set(
        self, patient: Patient, plan: Plan, study_uid: str, frame_ref_uid: str, output_path: str
    ) -> Dict[str, str]:
        """
        Export RT Structure Set using StructureConverter.

        Args:
            patient: Patient model object
            plan: Plan model object
            study_uid: Study instance UID
            frame_ref_uid: Frame of reference UID
            output_path: Output directory path

        Returns:
            Dictionary mapping structure set identifier to file path
        """
        exported_files = {}

        if not hasattr(plan, "roi_list") or not plan.roi_list:
            self.logger.warning("No ROI data available for RTSTRUCT export")
            return exported_files

        try:
            structure_converter = StructureConverter(patient, self.uid_manager)
            
            # Set root path if we have access to the PinnacleReader
            if self.pinnacle_reader and hasattr(self.pinnacle_reader, 'path'):
                structure_converter.set_root_path(str(self.pinnacle_reader.path))
            structure_converter.set_shared_uids(study_uid, frame_ref_uid=frame_ref_uid)

            # Get planning CT for reference
            if not hasattr(plan, "primary_ct_image_set") or not plan.primary_ct_image_set:
                raise ValueError("No planning CT available for structure set export")
            planning_ct = plan.primary_ct_image_set

            # Get point list if available
            poi_list = getattr(plan, "point_list", [])

            struct_ds = structure_converter.convert_from_models(plan.roi_list, planning_ct, poi_list)
            file_path = structure_converter.save_dataset(struct_ds, output_path)
            exported_files["RTSTRUCT"] = file_path

        except Exception as e:
            self.logger.error(f"Error exporting RT Structure Set: {e}", exc_info=True)
            raise

        return exported_files

    def _export_plan(
        self, patient: Patient, plan: Plan, trial: Trial, study_uid: str, frame_ref_uid: str, output_path: str
    ) -> Dict[str, str]:
        """
        Export RT Plan using PlanConverter.

        Args:
            patient: Patient model object
            plan: Plan model object
            trial: Trial model object
            study_uid: Study instance UID
            frame_ref_uid: Frame of reference UID
            output_path: Output directory path

        Returns:
            Dictionary mapping plan identifier to file path
        """
        exported_files = {}

        if not trial:
            self.logger.warning("No trial data available for RTPLAN export")
            return exported_files

        try:
            plan_converter = PlanConverter(patient, self.uid_manager)
            
            # Set root path if we have access to the PinnacleReader
            if self.pinnacle_reader and hasattr(self.pinnacle_reader, 'path'):
                plan_converter.set_root_path(str(self.pinnacle_reader.path))
            plan_converter.set_shared_uids(study_uid, frame_ref_uid=frame_ref_uid)

            # Get planning CT for reference
            planning_ct = (
                plan.primary_ct_image_set
                if hasattr(plan, "primary_ct_image_set") and plan.primary_ct_image_set
                else None
            )

            plan_ds = plan_converter.convert_from_models(trial, planning_ct)
            file_path = plan_converter.save_dataset(plan_ds, output_path)
            exported_files["RTPLAN"] = file_path

        except Exception as e:
            self.logger.error(f"Error exporting RT Plan: {e}", exc_info=True)
            raise

        return exported_files

    def _export_dose(
        self, patient: Patient, plan: Plan, trial: Trial, study_uid: str, frame_ref_uid: str, output_path: str
    ) -> Dict[str, str]:
        """
        Export RT Dose using DoseConverter.

        Args:
            patient: Patient model object
            plan: Plan model object
            trial: Trial model object
            study_uid: Study instance UID
            frame_ref_uid: Frame of reference UID
            output_path: Output directory path

        Returns:
            Dictionary mapping dose identifier to file path
        """
        exported_files = {}

        if not trial:
            self.logger.warning("No trial data available for RTDOSE export")
            return exported_files

        try:
            dose_converter = DoseConverter(patient, self.uid_manager)
            
            # Set root path if we have access to the PinnacleReader
            if self.pinnacle_reader and hasattr(self.pinnacle_reader, 'path'):
                dose_converter.set_root_path(str(self.pinnacle_reader.path))
            dose_converter.set_shared_uids(study_uid, frame_ref_uid=frame_ref_uid)

            # Get planning CT for reference
            if not hasattr(plan, "primary_ct_image_set") or not plan.primary_ct_image_set:
                raise ValueError("No planning CT available for dose export")
            planning_ct = plan.primary_ct_image_set

            # For dose, we need the dose data from the trial
            # This may need adjustment based on the actual trial model structure
            dose_ds = dose_converter.convert_from_models(trial, planning_ct)
            file_path = dose_converter.save_dataset(dose_ds, output_path)
            exported_files["RTDOSE"] = file_path

        except Exception as e:
            self.logger.error(f"Error exporting RT Dose: {e}", exc_info=True)
            raise

        return exported_files

    def _validate_dicom_output(self, files: Dict[str, str], result: DicomExportResult) -> None:
        """
        Validate generated DICOM files.

        Args:
            files: Dictionary of exported files
            result: Export result to update with validation findings
        """
        try:
            for file_path in files.values():
                if not os.path.exists(file_path):
                    result.warnings.append(f"Generated file not found: {file_path}")
                elif os.path.getsize(file_path) == 0:
                    result.warnings.append(f"Generated file is empty: {file_path}")

        except Exception as e:
            result.warnings.append(f"Error validating DICOM output: {str(e)}")
            self.logger.error(f"Error validating DICOM output: {e}", exc_info=True)
