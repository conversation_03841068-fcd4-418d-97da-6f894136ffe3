"""
Constants module for pinnacle_io DICOM conversion.

This module provides a central place for accessing all the constants used in the
application. It uses the settings module to retrieve values from the configuration
file, falling back to hardcoded defaults if needed.
"""

from pinnacle_io.utils.settings import (
    get_ct_image_parameters,
    get_default_dose_rate,
    get_dicom_implementation_class_uid,
    get_dicom_sop_class_uid,
    get_dicom_transfer_syntax_uid,
    get_hex_colors,
    get_manufacturer,
    get_mlc_leaf_jaw_pairs,
    get_mlc_leaf_position_boundaries,
    get_rgb_colors,
    get_station_name,
)

# DICOM UIDs and Implementation Information
IMPLEMENTATION_CLASS_UID = get_dicom_implementation_class_uid()
IMPLEMENTATION_VERSION_NAME = "DICOM_CONVERTER_1.0"
TRANSFER_SYNTAX_UID = get_dicom_transfer_syntax_uid()

# SOP Class UIDs
RT_STRUCTURE_SET_SOP_CLASS_UID = get_dicom_sop_class_uid("rt_structure_set")
RT_PLAN_SOP_CLASS_UID = get_dicom_sop_class_uid("rt_plan")
RT_DOSE_SOP_CLASS_UID = get_dicom_sop_class_uid("rt_dose")
CT_IMAGE_SOP_CLASS_UID = get_dicom_sop_class_uid("ct_image")
STUDY_COMPONENT_SOP_CLASS_UID = get_dicom_sop_class_uid("study_component")

# Media Storage SOP Class UIDs (same as SOP Class UIDs)
MEDIA_STORAGE_SOP_CLASS_UID_CT = CT_IMAGE_SOP_CLASS_UID
MEDIA_STORAGE_SOP_CLASS_UID_RTSTRUCT = RT_STRUCTURE_SET_SOP_CLASS_UID
MEDIA_STORAGE_SOP_CLASS_UID_RTPLAN = RT_PLAN_SOP_CLASS_UID
MEDIA_STORAGE_SOP_CLASS_UID_RTDOSE = RT_DOSE_SOP_CLASS_UID

# Modality values
MODALITY_CT = "CT"
MODALITY_RTSTRUCT = "RTSTRUCT"
MODALITY_RTPLAN = "RTPLAN"
MODALITY_RTDOSE = "RTDOSE"
MODALITY_MG = "MG"
MODALITY_MR = "MR"
MODALITY_PT = "PT"
MODALITY_RF = "RF"
MODALITY_US = "US"
MODALITY_XA = "XA"

# File prefixes and extensions
CT_PREFIX = "CT."
RT_STRUCTURE_PREFIX = "RS."
RT_PLAN_PREFIX = "RP."
RT_DOSE_PREFIX = "RD."
MG_PREFIX = "MG."
MR_PREFIX = "MR."
PT_PREFIX = "PT."
RF_PREFIX = "RF."
US_PREFIX = "US."
XA_PREFIX = "XA."
DICOM_EXTENSION = ".dcm"

# Modality to File Prefix Map
MODALITY_TO_FILE_PREFIX = {
    MODALITY_CT: CT_PREFIX,
    MODALITY_RTSTRUCT: RT_STRUCTURE_PREFIX,
    MODALITY_RTPLAN: RT_PLAN_PREFIX,
    MODALITY_RTDOSE: RT_DOSE_PREFIX,
    MODALITY_MG: MG_PREFIX,
    MODALITY_MR: MR_PREFIX,
    MODALITY_PT: PT_PREFIX,
    MODALITY_RF: RF_PREFIX,
    MODALITY_US: US_PREFIX,
    MODALITY_XA: XA_PREFIX,
}

# Equipment Information
MANUFACTURER = get_manufacturer()
STATION_NAME = get_station_name()

# ROI Colors (centralized for UI and DICOM usage)
RGB_COLORS = get_rgb_colors()
HEX_COLORS = get_hex_colors()

# MLC Configuration
MLC_LEAF_JAW_PAIRS = get_mlc_leaf_jaw_pairs()
MLC_LEAF_POSITION_BOUNDARIES = get_mlc_leaf_position_boundaries()

# Dose Parameters
DEFAULT_DOSE_RATE = get_default_dose_rate()

# CT Image Parameters
CT_IMAGE_PARAMETERS = get_ct_image_parameters()

# Character Sets
SPECIFIC_CHARACTER_SET = "ISO_IR 100"

# Image Types
IMAGE_TYPE_ORIGINAL_PRIMARY_AXIAL = ["ORIGINAL", "PRIMARY", "AXIAL"]

# Photometric Interpretation
PHOTOMETRIC_INTERPRETATION_MONOCHROME2 = "MONOCHROME2"

# Rotation Directions
ROTATION_DIRECTION_CW = "CW"
ROTATION_DIRECTION_NONE = "NONE"

# RT ROI Interpreted Types
RT_ROI_TYPE_MARKER = "MARKER"

# Contour Geometric Types
CONTOUR_GEOMETRIC_TYPE_POINT = "POINT"

# Beam Limiting Device Types
BEAM_LIMITING_DEVICE_TYPE_ASYMX = "ASYMX"
BEAM_LIMITING_DEVICE_TYPE_ASYMY = "ASYMY"
BEAM_LIMITING_DEVICE_TYPE_MLCX = "MLCX"

# Approval Status
APPROVAL_STATUS_UNAPPROVED = "UNAPPROVED"

# Structure Set Names
STRUCTURE_SET_NAME_DEFAULT = "POIandROI"

# ROI Generation Algorithm
ROI_GENERATION_ALGORITHM_SEMIAUTOMATIC = "SEMIAUTOMATIC"

# Wedge Position
WEDGE_POSITION_IN = "IN"

# Default Values
DEFAULT_SERIES_NUMBER = "1"
