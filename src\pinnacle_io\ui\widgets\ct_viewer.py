"""
CT viewer widget for displaying medical images with overlays.
"""

import tkinter as tk
import ttkbootstrap as ttk
import numpy as np
from PIL import Image, ImageTk
from typing import List, Any
from pinnacle_io.ui.constants import DEFAULT_WINDOW_WIDTH, DEFAULT_WINDOW_LEVEL

class CTViewer(ttk.Frame):
    """CT image viewer with overlay coordination."""

    def __init__(self, parent, app):
        """
        Initialize the CT viewer.

        Args:
            parent: Parent widget
            app: Main application instance
        """
        super().__init__(parent)
        self.app = app

        # Core image data
        self.current_image_set = None
        self.patient_position = None

        # Display state
        self.current_slice_index = 0
        self.total_slices = 0
        self.window_level = 0  # CT window level (HU)
        self.window_width = 1000  # CT window width (HU)

        # View orientation state
        self.view_orientation = "axial"  # "axial", "sagittal", "coronal"
        self.orientation_labels = {}  # Store orientation label widgets for styling

        # Remember slice position for each orientation
        self.orientation_slice_positions = {
            "axial": 0,
            "sagittal": 0,
            "coronal": 0
        }

        # Zoom state
        self.zoom_factor = 1.0  # 1.0 = fit to canvas, >1.0 = zoomed in, <1.0 = zoomed out
        self.min_zoom = 0.1
        self.max_zoom = 10.0
        self.zoom_step = 0.1

        # Coordinate system tracking (millimeters)
        self.pixel_spacing = (1.0, 1.0, 1.0)  # (x, y, z) in mm
        self.image_origin = (0.0, 0.0, 0.0)   # (x, y, z) origin in mm

        # Mouse position tracking for coordinate updates during slice navigation
        self.last_mouse_x = None
        self.last_mouse_y = None

        # Display caching
        self.displayed_image = None  # Current PIL image being shown
        self.photo_image = None     # Current PhotoImage for tkinter

        # Overlay panel registry
        self.overlay_panels: List[Any] = []

        # Performance optimization state
        self._pending_overlay_refresh = False

        # Callbacks for external control synchronization
        self.on_slice_changed_callback = None
        self.on_window_width_changed_callback = None
        self.on_window_level_changed_callback = None
        self.on_zoom_changed_callback = None
        self.on_orientation_changed_callback = None

        self._setup_ui()

    def _setup_ui(self):
        """Set up the CT viewer UI components."""
        # Title bar
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, padx=5, pady=(5, 0))

        title_label = ttk.Label(
            title_frame,
            text="CT Viewer",
            font=("Arial", 12, "bold")
        )
        title_label.pack(side=tk.LEFT)

        # Center the image info label
        center_frame = ttk.Frame(title_frame)
        center_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.image_info_var = tk.StringVar(value="No image loaded")
        info_label = ttk.Label(
            center_frame,
            textvariable=self.image_info_var,
            font=("Arial", 9)
        )
        info_label.pack()

        # View orientation controls on the right
        orientation_frame = ttk.Frame(title_frame)
        orientation_frame.pack(side=tk.RIGHT)

        # Create clickable orientation labels
        self._create_orientation_controls(orientation_frame)

        # Main viewer frame
        viewer_frame = ttk.Frame(self, relief="solid", borderwidth=1)
        viewer_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Image canvas for CT display
        self.canvas = tk.Canvas(
            viewer_frame,
            bg="black",
            highlightthickness=0
        )
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # Force black background (multiple methods for ttkbootstrap compatibility)
        self.canvas.configure(bg="#000000")
        self.canvas.after_idle(lambda: self.canvas.configure(bg="#000000"))

        # Create a permanent black rectangle as background
        self._create_black_background()

        # Bind keyboard events for navigation
        self.canvas.bind("<Button-1>", self._on_canvas_click)  # Focus for keyboard events
        self.canvas.bind("<Enter>", self._on_mouse_enter)       # Focus when mouse enters
        self.canvas.bind("<Motion>", self._on_mouse_motion)     # Track mouse movement for coordinates

        # Cross-platform mouse wheel support
        self._bind_mousewheel_events()

        # Mouse wheel with Ctrl for zoom
        self._bind_zoom_events()

        self.canvas.bind("<Key>", self._on_key_press)

        # Ensure canvas can receive focus and events
        self.canvas.focus_set()  # Allow keyboard focus
        self.canvas.config(takefocus=True)  # Make canvas focusable

        # Show initial content
        self._show_no_image_content()

        # Control panel
        self._create_control_panel()

    def _create_black_background(self):
        """Create a permanent black rectangle background for the canvas."""
        # Create background rectangle that covers entire canvas
        self.canvas.update_idletasks()  # Ensure canvas has size
        width = self.canvas.winfo_width() or 800
        height = self.canvas.winfo_height() or 600

        # Create a black rectangle that covers the entire canvas
        # Use lowest z-order so it stays behind all other items
        self.canvas.create_rectangle(
            0, 0, width * 10, height * 10,  # Make it large for panning/zooming
            fill="#000000", outline="#000000", tags="black_background"
        )

        # Ensure background stays at bottom of display list
        self.canvas.tag_lower("black_background")

        # Bind resize event to update background size
        self.canvas.bind("<Configure>", self._on_canvas_resize)

    def _on_canvas_resize(self, event):
        """Update black background when canvas is resized."""
        # Update background rectangle to cover new canvas size
        width = event.width
        height = event.height
        coords = (0, 0, width * 10, height * 10)  # Extra large for panning

        # Find and update background rectangle
        bg_items = self.canvas.find_withtag("black_background")
        if bg_items:
            self.canvas.coords(bg_items[0], *coords)

    def _bind_mousewheel_events(self):
        """Bind mouse wheel events for cross-platform compatibility."""
        import platform
        system = platform.system()

        # Try multiple approaches for maximum compatibility
        try:
            if system == "Windows":
                # Windows uses MouseWheel with delta
                self.canvas.bind("<MouseWheel>", self._on_mousewheel)
            elif system == "Darwin":  # macOS
                # macOS uses MouseWheel with delta
                self.canvas.bind("<MouseWheel>", self._on_mousewheel)
            else:  # Linux and others
                # Linux uses Button-4 and Button-5
                self.canvas.bind("<Button-4>", self._on_mousewheel_linux)
                self.canvas.bind("<Button-5>", self._on_mousewheel_linux)

                # Some Linux distributions also support MouseWheel
                try:
                    self.canvas.bind("<MouseWheel>", self._on_mousewheel)
                except:
                    pass  # Fallback failed, but Button-4/5 should work

                # Additional WSL-specific bindings that sometimes work
                try:
                    self.canvas.bind("<Control-Button-4>", self._on_mousewheel_linux)
                    self.canvas.bind("<Control-Button-5>", self._on_mousewheel_linux)
                except:
                    pass  # WSL-specific events failed, but standard events should work

        except Exception as e:
            print(f"Warning: Error binding mouse wheel events: {e}")

        # Ensure the canvas and parent can receive these events
        self.canvas.config(takefocus=True)
        self.focus_set()  # Set focus on the widget itself too

    def _bind_zoom_events(self):
        """Bind Ctrl + mouse wheel events for zoom functionality."""
        import platform
        system = platform.system()

        try:
            if system == "Windows":
                # Windows Ctrl + MouseWheel
                self.canvas.bind("<Control-MouseWheel>", self._on_zoom_mousewheel)
            elif system == "Darwin":  # macOS
                # macOS Ctrl + MouseWheel
                self.canvas.bind("<Control-MouseWheel>", self._on_zoom_mousewheel)
            else:  # Linux and others
                # Linux Ctrl + Button-4 and Ctrl + Button-5
                self.canvas.bind("<Control-Button-4>", self._on_zoom_mousewheel_linux)
                self.canvas.bind("<Control-Button-5>", self._on_zoom_mousewheel_linux)

                # Some Linux distributions also support Ctrl + MouseWheel
                try:
                    self.canvas.bind("<Control-MouseWheel>", self._on_zoom_mousewheel)
                except:
                    pass  # Fallback failed, but Button-4/5 should work

        except Exception as e:
            print(f"Warning: Error binding zoom events: {e}")

    def _create_control_panel(self):
        """Create the control panel at the bottom."""
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # # Orientation controls and hints
        # orientation_frame = ttk.LabelFrame(control_frame, text="Orientation & Navigation", padding=5)
        # orientation_frame.pack(fill=tk.X)

        # # Orientation controls
        # orientation_control_frame = ttk.Frame(orientation_frame)
        # orientation_control_frame.pack(fill=tk.X, pady=(0, 5))

        # ttk.Label(orientation_control_frame, text="View:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)
        # self._create_orientation_controls(orientation_control_frame)

        # Navigation hints and coordinate display
        nav_coord_frame = ttk.Frame(control_frame)
        nav_coord_frame.pack(fill=tk.X)

        # Navigation hints on the left
        ttk.Label(nav_coord_frame,
                 text="Navigation: ↑↓ (1 slice), PgUp/PgDn (5 slices), Mouse wheel | Zoom: Ctrl+Wheel, +/-, Home=Reset",
                 font=("Arial", 8)).pack(side=tk.LEFT, anchor="w")

        # Coordinate display on the right
        self.coordinate_var = tk.StringVar(value="")
        self.coordinate_label = ttk.Label(nav_coord_frame,
                                        textvariable=self.coordinate_var,
                                        font=("Arial", 8))
        self.coordinate_label.pack(side=tk.RIGHT, anchor="e")

        # # Keep these variables for compatibility but don't create UI controls
        # self.slice_var = tk.IntVar(value=0)
        # self.window_width_var = tk.IntVar(value=self.window_width)
        # self.window_level_var = tk.IntVar(value=self.window_level)
        # self.slice_info_var = tk.StringVar(value="Slice: 0 / 0")

    def _create_orientation_controls(self, parent):
        """Create clickable orientation control labels."""
        orientations = ["Axial", "Sagittal", "Coronal"]

        for i, orientation in enumerate(orientations):
            if i > 0:
                # Add separator
                separator = ttk.Label(parent, text=" | ", font=("Arial", 9))
                separator.pack(side=tk.LEFT)

            # Create clickable label
            label = tk.Label(
                parent,
                text=orientation,
                font=("Arial", 9),
                fg="white"
            )
            label.pack(side=tk.LEFT)

            # Store reference for styling updates
            self.orientation_labels[orientation.lower()] = label

            # Bind click event
            orientation_key = orientation.lower()
            label.bind("<Button-1>", lambda e, o=orientation_key: self._on_orientation_change(o))

            # Bind hover events for non-active labels
            label.bind("<Enter>", lambda e, lbl=label: self._on_orientation_hover_enter(lbl))
            label.bind("<Leave>", lambda e, lbl=label: self._on_orientation_hover_leave(lbl))

        # Set initial active orientation styling
        self._update_orientation_styling()

    def _on_orientation_change(self, orientation):
        """Handle orientation change."""
        if self.view_orientation != orientation:
            # Save current slice position for the current orientation
            self.orientation_slice_positions[self.view_orientation] = self.current_slice_index

            # Change to new orientation
            self.view_orientation = orientation
            self._update_orientation_styling()

            # Update slice count for new orientation and restore slice position
            self._update_slice_for_orientation()
            self._update_slice_display()

            # Update coordinate display at current mouse position for new orientation
            self._update_coordinate_display_for_slice_change()

            # Notify external controls of orientation change
            if self.on_orientation_changed_callback:
                self.on_orientation_changed_callback(orientation, self.total_slices, self.current_slice_index)

    def _on_orientation_hover_enter(self, label):
        """Handle mouse enter on orientation label."""
        # Only apply hover effect if this is not the active orientation
        orientation = self._get_orientation_from_label(label)
        if orientation != self.view_orientation:
            label.config(fg="white", font=("Arial", 9, "underline"))

    def _on_orientation_hover_leave(self, label):
        """Handle mouse leave on orientation label."""
        # Only remove hover effect if this is not the active orientation
        orientation = self._get_orientation_from_label(label)
        if orientation != self.view_orientation:
            label.config(fg="skyblue", font=("Arial", 9))

    def _get_orientation_from_label(self, label):
        """Get orientation key from label widget."""
        for orientation, stored_label in self.orientation_labels.items():
            if stored_label == label:
                return orientation
        return None

    def _update_orientation_styling(self):
        """Update styling for orientation labels based on current active orientation."""
        for orientation, label in self.orientation_labels.items():
            if orientation == self.view_orientation:
                # Active orientation: underlined, darker color, no hover
                label.config(fg="grey", font=("Arial", 9, "underline"), cursor="")
            else:
                # Inactive orientation: normal skyblue, clickable
                label.config(fg="skyblue", font=("Arial", 9), cursor="hand2")

    def _update_slice_for_orientation(self):
        """Update slice index and total slices when orientation changes."""
        if self.current_image_set and hasattr(self.current_image_set, 'pixel_data') and self.current_image_set.pixel_data is not None:
            dimensions = self._get_orientation_dimensions()
            if dimensions:
                self.total_slices = dimensions[2]  # Z dimension for current orientation

                # Restore saved slice position for this orientation, or use middle slice if not set
                saved_slice = self.orientation_slice_positions.get(self.view_orientation, None)
                if saved_slice is not None and saved_slice < self.total_slices:
                    self.current_slice_index = saved_slice
                else:
                    # Default to middle slice and save it
                    self.current_slice_index = self.total_slices // 2
                    self.orientation_slice_positions[self.view_orientation] = self.current_slice_index

                # Notify external controls
                if self.on_slice_changed_callback:
                    self.on_slice_changed_callback(self.current_slice_index)

    def _get_orientation_dimensions(self):
        """Get dimensions for current orientation (width, height, depth)."""
        if not self.current_image_set or not hasattr(self.current_image_set, 'pixel_data') or self.current_image_set.pixel_data is None:
            return None

        # Original dimensions: x, y, z
        x_dim = self.current_image_set.x_dim
        y_dim = self.current_image_set.y_dim
        z_dim = self.current_image_set.z_dim

        if not all([x_dim, y_dim, z_dim]):
            return None

        if self.view_orientation == "axial":
            # Axial: looking down from superior, slice through Z
            return (x_dim, y_dim, z_dim)
        elif self.view_orientation == "sagittal":
            # Sagittal: looking from side (left/right), slice through X
            return (y_dim, z_dim, x_dim)
        elif self.view_orientation == "coronal":
            # Coronal: looking from front/back, slice through Y
            return (x_dim, z_dim, y_dim)

        return None

    def _get_orientation_voxel_spacing(self):
        """Get voxel spacing for current orientation (width_spacing, height_spacing)."""
        if self.view_orientation == "axial":
            # Axial: X and Y dimensions
            return (self.pixel_spacing[0], self.pixel_spacing[1])
        elif self.view_orientation == "sagittal":
            # Sagittal: Y and Z dimensions
            return (self.pixel_spacing[1], self.pixel_spacing[2])
        elif self.view_orientation == "coronal":
            # Coronal: X and Z dimensions
            return (self.pixel_spacing[0], self.pixel_spacing[2])
        else:
            # Fallback to axial
            return (self.pixel_spacing[0], self.pixel_spacing[1])

    def _get_slice_data_for_orientation(self, slice_index):
        """Get slice data for current orientation and slice index."""
        if (slice_index < 0 or
            not self.current_image_set or 
            not hasattr(self.current_image_set, 'pixel_data') or 
            self.current_image_set.pixel_data is None):
            return None

        pixel_data = self.current_image_set.pixel_data

        if self.view_orientation == "axial":
            # Axial: normal Z-slice
            if slice_index >= pixel_data.shape[0]:
                return None
            return pixel_data[slice_index, :, :]

        elif self.view_orientation == "sagittal":
            # Sagittal: slice through X dimension (viewing from side)
            if slice_index >= pixel_data.shape[2]:
                return None
            # Extract X slice and reorient for upright patient (Y, Z)
            return pixel_data[:, :, slice_index]

        elif self.view_orientation == "coronal":
            # Coronal: slice through Y dimension (viewing from front/back)
            if slice_index >= pixel_data.shape[1]:
                return None
            # Extract Y slice and rotate for proper orientation
            return pixel_data[:, slice_index, :]

        return None

    def _show_no_image_content(self):
        """Show content when no image is loaded."""
        self.canvas.delete("all")
        self.canvas.update()

        # Wait for canvas to be drawn
        self.canvas.after(10, self._draw_no_image_content)

    def _draw_no_image_content(self):
        """Draw placeholder content when no image is loaded."""
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width > 1 and canvas_height > 1:
            # Draw placeholder outline
            center_x = canvas_width // 2
            center_y = canvas_height // 2
            size = min(canvas_width, canvas_height) // 3

            # Placeholder image rectangle
            self.canvas.create_rectangle(
                center_x - size, center_y - size,
                center_x + size, center_y + size,
                outline="gray", width=2
            )

            # Content text
            self.canvas.create_text(
                center_x, center_y - 20,
                text="CT Viewer",
                fill="white",
                font=("Arial", 16, "bold")
            )

            self.canvas.create_text(
                center_x, center_y + 10,
                text="Load a patient plan to view CT images",
                fill="gray",
                font=("Arial", 12)
            )

            self.canvas.create_text(
                center_x, center_y + 30,
                text="Navigation: ↑↓ arrows, Page Up/Down, mouse wheel",
                fill="gray",
                font=("Arial", 10)
            )

            self.canvas.create_text(
                center_x, center_y + 50,
                text="Zoom: Ctrl + mouse wheel, +/- keys, Home to reset",
                fill="gray",
                font=("Arial", 10)
            )

    def register_overlay_panel(self, panel):
        """Register an overlay panel for coordinate updates."""
        if panel not in self.overlay_panels:
            self.overlay_panels.append(panel)
            panel.register_ct_viewer(self)

    def unregister_overlay_panel(self, panel):
        """Unregister an overlay panel."""
        if panel in self.overlay_panels:
            self.overlay_panels.remove(panel)

    def refresh_overlays(self, immediate: bool = False):
        """Refresh all overlay panels with current display parameters.

        Args:
            immediate: If True, bypass throttling and refresh immediately
        """
        if not self.current_image_set:
            return

        # Performance optimization: throttle refresh requests
        if hasattr(self, '_pending_overlay_refresh') and self._pending_overlay_refresh and not immediate:
            return  # Already have a refresh scheduled

        if immediate:
            self._do_overlay_refresh()
        else:
            # Schedule refresh for next idle cycle
            self._pending_overlay_refresh = True
            self.after_idle(self._do_overlay_refresh)

    def _do_overlay_refresh(self):
        """Internal method to perform actual overlay refresh."""
        if hasattr(self, '_pending_overlay_refresh'):
            self._pending_overlay_refresh = False

        if not self.current_image_set:
            return

        # Get current slice Z coordinate
        current_z = self.get_current_slice_coordinate()
        if current_z is None:
            return

        # Get display parameters for overlay panels - cache for efficiency
        display_params = self._get_display_params()
        if not display_params:
            return

        # Batch overlay rendering to minimize canvas operations
        self.canvas.delete("overlay")  # Clear all overlays at once

        # Trigger overlay rendering in all registered panels
        for panel in self.overlay_panels:
            try:
                panel.render_overlays(self.canvas, current_z, display_params)
            except Exception as e:
                print(f"Error rendering overlays for panel {panel}: {e}")

    def load_image_set(self, image_set):
        """Load an image set for display.

        Args:
            image_set: ImageSet object containing CT image data
        """
        self.current_image_set = image_set

        # Store patient position from image_set
        self.patient_position = getattr(image_set, 'patient_position', None) if image_set else None

        if image_set and hasattr(image_set, 'pixel_data') and image_set.pixel_data is not None:
            # Extract image information
            modality = getattr(image_set, 'modality', 'Unknown')
            dimensions = image_set.get_image_dimensions()
            pixel_spacing = image_set.get_pixel_spacing()

            if dimensions:
                # Initialize slice positions for all orientations to middle slice
                x_dim, y_dim, z_dim = dimensions
                self.orientation_slice_positions = {
                    "axial": z_dim // 2,
                    "sagittal": x_dim // 2,
                    "coronal": y_dim // 2
                }

                # Use orientation-aware dimensions for initial load
                orientation_dimensions = self._get_orientation_dimensions()
                if orientation_dimensions:
                    self.total_slices = orientation_dimensions[2]  # z-dimension for current orientation
                else:
                    self.total_slices = dimensions[2]  # fallback to original z-dimension

                # Start at middle slice for current orientation
                self.current_slice_index = self.orientation_slice_positions[self.view_orientation]

                # Notify external controls
                if self.on_slice_changed_callback:
                    self.on_slice_changed_callback(self.current_slice_index)

                # Store coordinate system information
                if pixel_spacing:
                    self.pixel_spacing = pixel_spacing

                # Store image origin (start positions)
                self.image_origin = (
                    getattr(image_set, 'x_start_dicom', 0.0),
                    getattr(image_set, 'y_start_dicom', 0.0),
                    getattr(image_set, 'z_start', 0.0)
                )

                # Update info display
                self.image_info_var.set(f"{modality} - {dimensions[0]}×{dimensions[1]}×{dimensions[2]} - Spacing: {pixel_spacing[0]:.2f}×{pixel_spacing[1]:.2f}×{pixel_spacing[2]:.2f}mm")

                # Set appropriate window/level for CT
                if modality == "CT":
                    self.window_width = 1400  # Soft tissue window
                    self.window_level = 1000
                    # Update labels if they exist (they may be in external controls now)
                    if hasattr(self, 'window_width_label'):
                        self.window_width_label.config(text=f"{self.window_width}")
                    if hasattr(self, 'window_level_label'):
                        self.window_level_label.config(text=f"{self.window_level}")
                    # Notify external controls
                    if self.on_window_width_changed_callback:
                        self.on_window_width_changed_callback(self.window_width)
                    if self.on_window_level_changed_callback:
                        self.on_window_level_changed_callback(self.window_level)

                print(f"Loaded {modality} image set: {dimensions[0]}×{dimensions[1]}×{dimensions[2]}")

                # Display the current slice after UI is fully rendered
                self.canvas.after(10, self._update_slice_display)
            else:
                self.image_info_var.set(f"{modality} - Invalid dimensions")
                self._show_no_image_content()
        else:
            self.image_info_var.set("No image loaded")
            self.total_slices = 0
            self.current_slice_index = 0
            # Notify external controls
            if self.on_slice_changed_callback:
                self.on_slice_changed_callback(0)
            self._show_no_image_content()

    def refresh_dose_display(self):
        """Refresh dose overlays from the dose panel data."""
        # Trigger overlay refresh for all panels (including dose panel)
        self.refresh_overlays()

    def _update_slice_display(self):
        """Update the displayed slice."""
        if not self.current_image_set or not hasattr(self.current_image_set, 'pixel_data') or self.current_image_set.pixel_data is None:
            return

        if self.current_slice_index >= self.total_slices:
            return

        try:
            # Get slice data
            slice_data = self._get_slice_data_for_orientation(self.current_slice_index)
            if slice_data is None:
                return

            # Apply window/level to the slice data
            windowed_data = self._apply_window_level(slice_data)

            # Convert to PIL Image
            pil_image = Image.fromarray(windowed_data, mode='L')

            # Scale image to fit canvas while maintaining aspect ratio
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                # Get voxel spacing for current orientation
                voxel_width, voxel_height = self._get_orientation_voxel_spacing()

                # Calculate physical dimensions (mm)
                physical_width = pil_image.width * voxel_width
                physical_height = pil_image.height * voxel_height

                # Calculate scaling to fit canvas while preserving physical aspect ratio
                scale_x = canvas_width / physical_width
                scale_y = canvas_height / physical_height
                base_scale = min(scale_x, scale_y) * 0.9  # 90% to leave some border

                # Apply zoom factor
                zoom_scale = base_scale * self.zoom_factor

                # Calculate final dimensions using voxel spacing
                new_width = int(physical_width * zoom_scale)
                new_height = int(physical_height * zoom_scale)

                # Resize image
                pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # Store for coordinate calculations
                self.displayed_image = pil_image

                # Convert to PhotoImage for tkinter
                self.photo_image = ImageTk.PhotoImage(pil_image)

                # Clear canvas and display image
                self.canvas.delete("all")
                self.canvas.create_image(
                    canvas_width // 2,
                    canvas_height // 2,
                    image=self.photo_image,
                    anchor=tk.CENTER
                )

                # Draw all overlays via registered panels
                self.refresh_overlays()

                # Slice info is now handled by external CT panel

        except Exception as e:
            print(f"Error displaying slice: {e}")

    def _apply_window_level(self, slice_data):
        """Apply window/level (brightness/contrast) to slice data."""
        try:
            # Convert to float for processing
            data = slice_data.astype(np.float32)

            # Apply window/level transformation
            min_val = self.window_level - self.window_width // 2
            max_val = self.window_level + self.window_width // 2

            # Clip values to window range
            data = np.clip(data, min_val, max_val)

            # Scale to 0-255 range
            if max_val > min_val:
                data = (data - min_val) / (max_val - min_val) * 255
            else:
                data = np.full_like(data, 127)  # Mid-gray if no window range

            # Convert to uint8
            return data.astype(np.uint8)

        except Exception as e:
            print(f"Error applying window/level: {e}")
            return np.full_like(slice_data, 127, dtype=np.uint8)

    def _on_slice_change(self, value):
        """Handle slice slider change."""
        new_slice = int(float(value))
        if new_slice != self.current_slice_index and 0 <= new_slice < self.total_slices:
            self.current_slice_index = new_slice
            self._update_slice_display()

    def _on_window_width_change(self, value):
        """Handle window width change."""
        self.window_width = int(float(value))
        if hasattr(self, 'window_width_label'):
            self.window_width_label.config(text=f"{self.window_width}")
        self._update_slice_display()
        # Notify external controls
        if self.on_window_width_changed_callback:
            self.on_window_width_changed_callback(self.window_width)

    def _on_window_level_change(self, value):
        """Handle window level change."""
        self.window_level = int(float(value))
        if hasattr(self, 'window_level_label'):
            self.window_level_label.config(text=f"{self.window_level}")
        self._update_slice_display()
        # Notify external controls
        if self.on_window_level_changed_callback:
            self.on_window_level_changed_callback(self.window_level)

    def _on_canvas_click(self, event):
        """Handle canvas click to set focus for keyboard and mouse events."""
        del event  # Unused parameter
        self.canvas.focus_set()
        self.canvas.focus_force()  # Force focus in case normal focus_set() doesn't work

    def _on_mouse_enter(self, event):
        """Handle mouse entering the canvas area."""
        del event  # Unused parameter
        self.canvas.focus_set()  # Ensure focus when mouse enters

    def _on_mouse_motion(self, event):
        """Handle mouse motion for coordinate and dose display."""
        if not self.current_image_set or not hasattr(self, 'coordinate_var'):
            return

        # Track mouse position for slice navigation coordinate updates
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y

        # Convert mouse position to world coordinates
        world_coords = self.pixel_to_world_coordinates(event.x, event.y)
        if world_coords is None:
            self.coordinate_var.set("")
            return

        world_x, world_y, world_z = world_coords

        # Try to get dose value at this location
        dose_panel = next((panel for panel in self.overlay_panels if hasattr(panel, 'current_dose')), None)
        if dose_panel:
            dose_value = dose_panel.current_dose.get_dose_value_at_point(world_x, world_y, world_z)
        else:
            dose_value = None

        # Format the coordinate display
        if dose_value is not None:
            # Case 2: Inside dose grid - show coordinates, dose, and pixel position
            coord_text = (f"({world_x:.3f}, {world_y:.3f}, {world_z:.3f}) / {dose_value:.1f} cGy")
        else:
            # Case 1: Outside dose grid - show coordinates and pixel position
            coord_text = f"({world_x:.3f}, {world_y:.3f}, {world_z:.3f})"

        self.coordinate_var.set(coord_text)

    def _update_coordinate_display_for_slice_change(self):
        """Update coordinate display when slice changes but mouse hasn't moved."""
        if (not self.current_image_set or
            not hasattr(self, 'coordinate_var') or
            self.last_mouse_x is None or
            self.last_mouse_y is None):
            return

        # Use the last known mouse position to update coordinates for new slice
        world_coords = self.pixel_to_world_coordinates(self.last_mouse_x, self.last_mouse_y)
        if world_coords is None:
            self.coordinate_var.set("")
            return

        world_x, world_y, world_z = world_coords

        # Try to get dose value at this location
        dose_panel = next((panel for panel in self.overlay_panels if hasattr(panel, 'current_dose')), None)
        if dose_panel:
            dose_value = dose_panel.current_dose.get_dose_value_at_point(world_x, world_y, world_z)
        else:
            dose_value = None

        # Format the coordinate display
        if dose_value is not None:
            # Case 2: Inside dose grid - show coordinates, dose, and pixel position
            coord_text = (f"({world_x:.3f}, {world_y:.3f}, {world_z:.3f}) / {dose_value:.1f} cGy")
        else:
            # Case 1: Outside dose grid - show coordinates and pixel position
            coord_text = f"({world_x:.3f}, {world_y:.3f}, {world_z:.3f})"

        self.coordinate_var.set(coord_text)

    def _on_mousewheel(self, event):
        """Handle mouse wheel for slice navigation (cross-platform)."""
        # Check if Ctrl is pressed - if so, ignore for slice navigation (zoom will handle it)
        if getattr(event, 'state', 0) & 0x4:  # Ctrl key pressed
            return

        if self.total_slices > 0:
            # Handle different platforms
            delta = getattr(event, 'delta', 0)
            num = getattr(event, 'num', 0)

            if num == 4 or delta > 0:  # Linux Button-4 or Windows wheel up
                self._navigate_slice(-1)  # Previous slice
            elif num == 5 or delta < 0:  # Linux Button-5 or Windows wheel down
                self._navigate_slice(1)   # Next slice

    def _on_mousewheel_linux(self, event):
        """Handle Linux-specific mouse wheel events (Button-4 and Button-5)."""
        # Check if Ctrl is pressed - if so, ignore for slice navigation (zoom will handle it)
        if getattr(event, 'state', 0) & 0x4:  # Ctrl key pressed
            return

        if self.total_slices > 0:
            if event.num == 4:  # Scroll up
                self._navigate_slice(-1)
            elif event.num == 5:  # Scroll down
                self._navigate_slice(1)

    def _on_zoom_mousewheel(self, event):
        """Handle Ctrl + mouse wheel for zoom functionality."""
        delta = getattr(event, 'delta', 0)
        num = getattr(event, 'num', 0)

        if num == 4 or delta > 0:  # Zoom in
            self._zoom_in()
        elif num == 5 or delta < 0:  # Zoom out
            self._zoom_out()

    def _on_zoom_mousewheel_linux(self, event):
        """Handle Linux-specific Ctrl + mouse wheel events for zoom."""
        if event.num == 4:  # Ctrl + scroll up = zoom in
            self._zoom_in()
        elif event.num == 5:  # Ctrl + scroll down = zoom out
            self._zoom_out()

    def _zoom_in(self):
        """Increase zoom factor."""
        new_zoom = min(self.zoom_factor + self.zoom_step, self.max_zoom)
        if new_zoom != self.zoom_factor:
            self.zoom_factor = new_zoom
            self._update_slice_display()
            # Notify external controls
            if self.on_zoom_changed_callback:
                self.on_zoom_changed_callback(self.zoom_factor)

    def _zoom_out(self):
        """Decrease zoom factor."""
        new_zoom = max(self.zoom_factor - self.zoom_step, self.min_zoom)
        if new_zoom != self.zoom_factor:
            self.zoom_factor = new_zoom
            self._update_slice_display()
            # Notify external controls
            if self.on_zoom_changed_callback:
                self.on_zoom_changed_callback(self.zoom_factor)

    def reset_zoom(self):
        """Reset zoom to fit the image to canvas."""
        if self.zoom_factor != 1.0:
            self.zoom_factor = 1.0
            self._update_slice_display()
            # Notify external controls
            if self.on_zoom_changed_callback:
                self.on_zoom_changed_callback(self.zoom_factor)

    def _on_key_press(self, event):
        """Handle keyboard navigation."""
        if self.total_slices == 0:
            return

        if event.keysym == 'Up':
            self._navigate_slice(-1)
        elif event.keysym == 'Down':
            self._navigate_slice(1)
        elif event.keysym == 'Prior':  # Page Up
            self._navigate_slice(-5)
        elif event.keysym == 'Next':   # Page Down
            self._navigate_slice(5)
        elif event.keysym == 'Home':   # Home key - reset zoom
            self.reset_zoom()
        elif event.keysym == 'plus' or event.keysym == 'equal':  # + or = key - zoom in
            self._zoom_in()
        elif event.keysym == 'minus':  # - key - zoom out
            self._zoom_out()

    def _navigate_slice(self, delta):
        """Navigate to a different slice by delta."""
        new_slice = self.current_slice_index + delta
        new_slice = max(0, min(new_slice, self.total_slices - 1))

        if new_slice != self.current_slice_index:
            self.current_slice_index = new_slice
            # Save the slice position for current orientation
            self.orientation_slice_positions[self.view_orientation] = new_slice
            self._update_slice_display()
            # Update coordinate display at current mouse position
            self._update_coordinate_display_for_slice_change()
            # Notify external controls
            if self.on_slice_changed_callback:
                self.on_slice_changed_callback(self.current_slice_index)

    def pixel_to_world_coordinates(self, pixel_x, pixel_y):
        """Convert pixel coordinates to world coordinates (mm)."""
        if not self.current_image_set or not self.displayed_image:
            return None

        # Calculate the scale factor used for display
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            return None

        # Get orientation-aware image dimensions
        orientation_dims = self._get_orientation_dimensions()
        if not orientation_dims:
            return None

        original_width = orientation_dims[0]
        original_height = orientation_dims[1]

        # Get voxel spacing for current orientation
        voxel_width, voxel_height = self._get_orientation_voxel_spacing()

        # Calculate physical dimensions (mm)
        physical_width = original_width * voxel_width
        physical_height = original_height * voxel_height

        # Calculate display scale preserving physical aspect ratio (must match _update_slice_display)
        scale_x = canvas_width / physical_width
        scale_y = canvas_height / physical_height
        base_scale = min(scale_x, scale_y) * 0.9  # 90% to leave some border

        # Apply zoom factor (must match _update_slice_display)
        zoom_scale = base_scale * self.zoom_factor

        # Calculate actual displayed image dimensions
        displayed_width = physical_width * zoom_scale
        displayed_height = physical_height * zoom_scale

        # Convert from canvas coordinates to displayed image coordinates
        center_x = canvas_width // 2
        center_y = canvas_height // 2

        # Calculate offset from center in canvas coordinates
        offset_x = pixel_x - center_x
        offset_y = pixel_y - center_y

        # Convert to displayed image pixel coordinates (centered on canvas)
        img_pixel_x = offset_x + (displayed_width / 2)
        img_pixel_y = offset_y + (displayed_height / 2)

        # Convert from displayed image pixels to original image pixels
        # displayed_width = original_width * voxel_width * zoom_scale
        # So: original_pixel = displayed_pixel / (voxel_spacing * zoom_scale)
        orig_pixel_x = img_pixel_x / (voxel_width * zoom_scale)
        orig_pixel_y = img_pixel_y / (voxel_height * zoom_scale)

        # Convert to world coordinates (mm) based on orientation
        if self.view_orientation == "axial":
            # Axial: orig_pixel_x maps to X, orig_pixel_y maps to Y
            world_x = self.image_origin[0] + orig_pixel_x * self.pixel_spacing[0]
            world_y = self.image_origin[1] + orig_pixel_y * self.pixel_spacing[1]
            world_z = self.image_origin[2] + self.current_slice_index * self.pixel_spacing[2]
        elif self.view_orientation == "sagittal":
            # Sagittal: orig_pixel_x maps to Y, orig_pixel_y maps to Z
            world_x = self.image_origin[0] + self.current_slice_index * self.pixel_spacing[0]
            world_y = self.image_origin[1] + orig_pixel_x * self.pixel_spacing[1]
            world_z = self.image_origin[2] + orig_pixel_y * self.pixel_spacing[2]
        elif self.view_orientation == "coronal":
            # Coronal: orig_pixel_x maps to X, orig_pixel_y maps to Z
            world_x = self.image_origin[0] + orig_pixel_x * self.pixel_spacing[0]
            world_y = self.image_origin[1] + self.current_slice_index * self.pixel_spacing[1]
            world_z = self.image_origin[2] + orig_pixel_y * self.pixel_spacing[2]
        else:
            # Fallback to axial
            world_x = self.image_origin[0] + orig_pixel_x * self.pixel_spacing[0]
            world_y = self.image_origin[1] + orig_pixel_y * self.pixel_spacing[1]
            world_z = self.image_origin[2] + self.current_slice_index * self.pixel_spacing[2]

        return (world_x, world_y, world_z)

    def _apply_coordinate_transformations(self):
        """Apply coordinate transformations handled by individual panels."""
        # Coordinate transformations are now handled by individual overlay panels
        # This method is kept for compatibility but delegates to panels
        pass

    def refresh_coordinate_transformations(self):
        """Refresh coordinate transformations and update display.

        Call this method if patient position or data changes after initial load.
        """
        self._apply_coordinate_transformations()
        self._update_slice_display()

    def get_current_slice_coordinate(self):
        """Get the coordinate of the currently displayed slice."""
        if not self.current_image_set:
            return None

        # Calculate coordinate based on current orientation and slice index
        if self.view_orientation == "axial":
            # Axial: slice through Z dimension
            coord = self.image_origin[2] + self.current_slice_index * self.pixel_spacing[2]
        elif self.view_orientation == "sagittal":
            # Sagittal: slice through X dimension
            coord = self.image_origin[0] + self.current_slice_index * self.pixel_spacing[0]
        elif self.view_orientation == "coronal":
            # Coronal: slice through Y dimension
            coord = self.image_origin[1] + self.current_slice_index * self.pixel_spacing[1]
        else:
            coord = self.image_origin[2] + self.current_slice_index * self.pixel_spacing[2]  # fallback

        return coord

    def _get_display_params(self):
        """Get display parameters for overlay panels."""
        if not self.current_image_set or not self.displayed_image:
            return None

        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            return None

        # Get orientation-aware image dimensions
        orientation_dims = self._get_orientation_dimensions()
        if not orientation_dims:
            return None

        original_width = orientation_dims[0]
        original_height = orientation_dims[1]

        # Get voxel spacing for current orientation
        voxel_width, voxel_height = self._get_orientation_voxel_spacing()

        # Calculate physical dimensions (mm)
        physical_width = original_width * voxel_width
        physical_height = original_height * voxel_height

        # Calculate display scale preserving physical aspect ratio
        scale_x = canvas_width / physical_width
        scale_y = canvas_height / physical_height
        scale = min(scale_x, scale_y) * 0.9

        # Apply zoom factor
        scale *= self.zoom_factor

        return {
            'scale': scale,
            'canvas_width': canvas_width,
            'canvas_height': canvas_height,
            'center_x': canvas_width // 2,
            'center_y': canvas_height // 2,
            'original_width': original_width,
            'original_height': original_height,
            'zoom_factor': self.zoom_factor,
            'pixel_spacing': self.pixel_spacing,
            'image_origin': self.image_origin
        }

    # Public methods for external control (called from CT panel)

    def set_slice(self, slice_index):
        """Set the current slice index from external control."""
        if 0 <= slice_index < self.total_slices and slice_index != self.current_slice_index:
            self.current_slice_index = slice_index
            # Save the slice position for current orientation
            self.orientation_slice_positions[self.view_orientation] = slice_index
            self._update_slice_display()
            # Update coordinate display at current mouse position
            self._update_coordinate_display_for_slice_change()

    def set_window_width(self, width):
        """Set the window width from external control."""
        if width != self.window_width:
            self.window_width = width
            if hasattr(self, 'window_width_label'):
                self.window_width_label.config(text=f"{width}")
            self._update_slice_display()

    def set_window_level(self, level):
        """Set the window level from external control."""
        if level != self.window_level:
            self.window_level = level
            if hasattr(self, 'window_level_label'):
                self.window_level_label.config(text=f"{level}")
            self._update_slice_display()

    def set_zoom(self, zoom_factor):
        """Set the zoom factor from external control."""
        if self.min_zoom <= zoom_factor <= self.max_zoom and zoom_factor != self.zoom_factor:
            self.zoom_factor = zoom_factor
            self._update_slice_display()

    def clear_data(self):
        """Clear all data from the CT viewer."""
        # Reset core image data
        self.current_image_set = None
        self.patient_position = None

        # Reset display state
        self.current_slice_index = 0
        self.coordinate_var.set("")
        self.total_slices = 0
        self.window_level = DEFAULT_WINDOW_LEVEL
        self.window_width = DEFAULT_WINDOW_WIDTH

        # Reset orientation state and slice positions
        self.view_orientation = "axial"
        self.orientation_slice_positions = {
            "axial": 0,
            "sagittal": 0,
            "coronal": 0
        }

        # Reset zoom state
        self.zoom_factor = 1.0

        # Reset coordinate system tracking
        self.pixel_spacing = (1.0, 1.0, 1.0)
        self.image_origin = (0.0, 0.0, 0.0)

        # Clear mouse position tracking
        self.last_mouse_x = None
        self.last_mouse_y = None

        # Clear display caching
        self.displayed_image = None
        self.photo_image = None

        # Clear all overlay panels
        for panel in self.overlay_panels:
            panel.clear_data() if hasattr(panel, 'clear_data') else None

        # Update image info display
        self.image_info_var.set("No image loaded")

        # Clear canvas and show no image content
        self.canvas.delete("all")
        self._show_no_image_content()

        # Reset callbacks
        self.on_slice_changed_callback = None
        self.on_window_width_changed_callback = None
        self.on_window_level_changed_callback = None
        self.on_zoom_changed_callback = None
        self.on_orientation_changed_callback = None
