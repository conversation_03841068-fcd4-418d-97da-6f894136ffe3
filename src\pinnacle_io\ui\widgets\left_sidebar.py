"""
Left sidebar widget containing patient, plan, and trial selection.
"""

import tkinter as tk
import ttkbootstrap as ttk


class LeftSidebar(ttk.Frame):
    """Left sidebar with patient, plan, and trial selection panels."""

    def __init__(self, parent, app):
        """
        Initialize the left sidebar.

        Args:
            parent: Parent widget
            app: Main application instance
        """
        super().__init__(parent, width=350)
        self.app = app
        self.pack_propagate(False)  # Maintain fixed width

        # Current selections
        self.current_patient_id = None
        self.current_plan_id = None
        self.current_plans = {}  # Dict with plan_id as keys
        self.current_trial_id = None
        self.current_trials = {}  # Dict with trial_id as keys

        # Flag to prevent double-triggering during programmatic selection
        self._programmatic_selection = False

        self._setup_ui()

    def _setup_ui(self):
        """Set up the sidebar UI components."""
        # Title
        title_label = ttk.Label(
            self,
            text="Navigation",
            font=("Arial", 12, "bold")
        )
        title_label.pack(fill=tk.X, padx=5, pady=(5, 10))

        # Patients section
        self._create_patients_section()

        # Plans section
        self._create_plans_section()

        # Trials section
        self._create_trials_section()

    def _create_patients_section(self):
        """Create the patients selection section."""
        # Patients label
        patients_label = ttk.Label(
            self,
            text="Patients",
            font=("Arial", 10, "bold")
        )
        patients_label.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Patients listbox with scrollbar
        patients_frame = ttk.Frame(self)
        patients_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 10))

        # Create scrollbar and listbox
        patients_scrollbar = ttk.Scrollbar(patients_frame)
        patients_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.patients_listbox = ttk.Treeview(
            patients_frame,
            columns=("mrn", "name"),
            show="tree headings",
            yscrollcommand=patients_scrollbar.set
        )
        self.patients_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        patients_scrollbar.config(command=self.patients_listbox.yview)

        # Configure columns
        self.patients_listbox.heading("#0", text="ID")
        self.patients_listbox.heading("mrn", text="MRN")
        self.patients_listbox.heading("name", text="Name")
        self.patients_listbox.column("#0", width=40, minwidth=40)
        self.patients_listbox.column("mrn", width=60, minwidth=40)
        self.patients_listbox.column("name", width=160, minwidth=100)

        # Bind selection event
        self.patients_listbox.bind("<<TreeviewSelect>>", self._on_patient_select)

    def _create_plans_section(self):
        """Create the plans selection section."""
        # Plans label
        plans_label = ttk.Label(
            self,
            text="Plans",
            font=("Arial", 10, "bold")
        )
        plans_label.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Plans listbox with scrollbar
        plans_frame = ttk.Frame(self)
        plans_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 10))

        # Create scrollbar and listbox
        plans_scrollbar = ttk.Scrollbar(plans_frame)
        plans_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.plans_listbox = ttk.Treeview(
            plans_frame,
            columns=("name",),
            show="tree headings",
            yscrollcommand=plans_scrollbar.set
        )
        self.plans_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        plans_scrollbar.config(command=self.plans_listbox.yview)

        # Configure columns
        self.plans_listbox.heading("#0", text="ID")
        self.plans_listbox.heading("name", text="Name")
        self.plans_listbox.column("#0", width=40, minwidth=40)
        self.plans_listbox.column("name", width=180, minwidth=100)

        # Bind selection event
        self.plans_listbox.bind("<<TreeviewSelect>>", self._on_plan_select)

    def _create_trials_section(self):
        """Create the trials selection section."""
        # Trials label
        trials_label = ttk.Label(
            self,
            text="Trials",
            font=("Arial", 10, "bold")
        )
        trials_label.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Trials listbox with scrollbar
        trials_frame = ttk.Frame(self)
        trials_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # Create scrollbar and listbox
        trials_scrollbar = ttk.Scrollbar(trials_frame)
        trials_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.trials_listbox = ttk.Treeview(
            trials_frame,
            columns=("name", "beams"),
            show="tree headings",
            yscrollcommand=trials_scrollbar.set
        )
        self.trials_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        trials_scrollbar.config(command=self.trials_listbox.yview)

        # Configure columns
        self.trials_listbox.heading("#0", text="ID")
        self.trials_listbox.heading("name", text="Name")
        self.trials_listbox.heading("beams", text="Beams")
        self.trials_listbox.column("#0", width=30, minwidth=30)
        self.trials_listbox.column("name", width=120, minwidth=80)
        self.trials_listbox.column("beams", width=60, minwidth=40)

        # Bind selection event
        self.trials_listbox.bind("<<TreeviewSelect>>", self._on_trial_select)

    def populate_patients(self, patients):
        """
        Populate the patients listbox with data from patient list.

        Args:
            patients: List of patient lite objects
        """
        # Clear existing items
        self.patients_listbox.delete(*self.patients_listbox.get_children())

        # Add patients
        for patient in patients:
            # Extract patient ID and name
            patient_id = patient.patient_id
            mrn = patient.medical_record_number
            patient_name = f"{patient.last_name}, {patient.first_name}"

            # If patient_name is None or empty, use a fallback
            if not patient_name:
                patient_name = f"Patient {patient_id}"

            self.patients_listbox.insert(
                "",
                "end",
                iid=patient_id,
                text=str(patient_id),
                values=(mrn, patient_name,)
            )

    def populate_plans(self, patient):
        """
        Populate the plans listbox with data from patient.

        Args:
            patient: Patient object containing plan list
        """
        # Clear existing items
        self.plans_listbox.delete(*self.plans_listbox.get_children())
        self.current_plans = {}

        # Add plans
        if hasattr(patient, 'plan_list') and patient.plan_list:
            # Convert plan list to dict with plan_id as keys
            self.current_plans = {plan.plan_id: plan for plan in patient.plan_list}
            for plan in patient.plan_list:
                # Extract plan ID and name
                plan_id = plan.plan_id
                plan_name = plan.name

                # If plan_name is None or empty, use a fallback
                if not plan_name:
                    plan_name = f"Plan {plan_id}"

                self.plans_listbox.insert(
                    "",
                    "end",
                    iid=plan_id,
                    text=str(plan_id),
                    values=(plan_name,)
                )

    def populate_trials(self, trials):
        """
        Populate the trials listbox with trial data.

        Args:
            trials: List of Trial objects
        """
        # Clear existing items
        self.trials_listbox.delete(*self.trials_listbox.get_children())
        # Convert trials list to dict with trial_id as keys
        self.current_trials = {trial.trial_id: trial for trial in trials}

        # Add trials
        for idx, trial in enumerate(trials):
            trial_name = getattr(trial, 'name', f'Trial {idx + 1}')
            beam_count = len(getattr(trial, 'beam_list', []))

            # If trial_name is None or empty, use a fallback
            if not trial_name:
                trial_name = f"Trial {idx + 1}"

            self.trials_listbox.insert(
                "",
                "end",
                iid=trial.trial_id,
                text=str(trial.trial_id),
                values=(trial_name, beam_count)
            )

    def select_patient(self, patient_id) -> bool:
        """
        Programmatically select a patient.

        Args:
            patient_id: ID of patient to select

        Returns:
            bool: True if selection was successful, False otherwise
        """
        try:
            self._programmatic_selection = True
            self.patients_listbox.selection_set(str(patient_id))
            self.patients_listbox.focus(str(patient_id))
            self._trigger_patient_selection(patient_id)
            # Schedule flag reset for next event loop cycle to avoid race condition
            self.after_idle(lambda: setattr(self, '_programmatic_selection', False))
            return True
        except tk.TclError:
            print(f"Could not select patient {patient_id}")
            self._programmatic_selection = False
            return False

    def select_plan(self, plan_id) -> bool:
        """
        Programmatically select a plan.

        Args:
            plan_id: ID of plan to select

        Returns:
            bool: True if selection was successful, False otherwise
        """
        try:
            self._programmatic_selection = True
            self.plans_listbox.selection_set(str(plan_id))
            self.plans_listbox.focus(str(plan_id))
            self._trigger_plan_selection(plan_id)
            # Schedule flag reset for next event loop cycle to avoid race condition
            self.after_idle(lambda: setattr(self, '_programmatic_selection', False))
            return True
        except tk.TclError:
            print(f"Could not select plan {plan_id}")
            self._programmatic_selection = False
            return False

    def select_trial(self, trial_id) -> bool:
        """
        Programmatically select a trial.

        Args:
            trial_id: ID of trial to select

        Returns:
            bool: True if selection was successful, False otherwise
        """
        try:
            self._programmatic_selection = True
            self.trials_listbox.selection_set(str(trial_id))
            self.trials_listbox.focus(str(trial_id))
            self._trigger_trial_selection(trial_id)
            # Schedule flag reset for next event loop cycle to avoid race condition
            self.after_idle(lambda: setattr(self, '_programmatic_selection', False))
            return True
        except tk.TclError:
            print(f"Could not select trial {trial_id}")
            self._programmatic_selection = False
            return False

    def _on_patient_select(self, event):
        """Handle patient selection event."""
        if self._programmatic_selection:
            return
        selection = self.patients_listbox.selection()
        if selection:
            patient_id = int(selection[0])
            self._trigger_patient_selection(patient_id)

    def _on_plan_select(self, event):
        """Handle plan selection event."""
        if self._programmatic_selection:
            return
        selection = self.plans_listbox.selection()
        if selection:
            plan_id = int(selection[0])
            self._trigger_plan_selection(plan_id)

    def _on_trial_select(self, event):
        """Handle trial selection event."""
        if self._programmatic_selection:
            return
        selection = self.trials_listbox.selection()
        if selection:
            trial_id = int(selection[0])
            self._trigger_trial_selection(trial_id)

    def _trigger_patient_selection(self, patient_id):
        """Trigger patient selection callback."""
        # Check if this is a duplicate selection
        if self.current_patient_id == patient_id:
            return  # No change, don't trigger UI updates

        # New patient selected - reset all dependent selections
        self.current_patient_id = patient_id
        self.current_plan_id = None
        self.current_trial_id = None

        # Clear dependent listboxes
        self.plans_listbox.delete(*self.plans_listbox.get_children())
        self.trials_listbox.delete(*self.trials_listbox.get_children())
        self.current_plans = {}
        self.current_trials = {}

        # Notify app
        self.app.on_patient_selected(patient_id)

    def _trigger_plan_selection(self, plan_id):
        """Trigger plan selection callback."""
        if self.current_patient_id is None:
            return

        # Check if this is a duplicate selection
        if self.current_plan_id == plan_id:
            return  # No change, don't trigger UI updates

        # New plan selected - reset trial selection
        self.current_plan_id = plan_id
        self.current_trial_id = None

        # Clear trials
        self.trials_listbox.delete(*self.trials_listbox.get_children())
        self.current_trials = {}

        # Notify app
        self.app.on_plan_selected(self.current_patient_id, self.current_plans.get(plan_id))

    def _trigger_trial_selection(self, trial_id):
        """Trigger trial selection callback."""
        if self.current_patient_id is None or self.current_plan_id is None:
            return

        if trial_id not in self.current_trials:
            return

        # Check if this is a duplicate selection
        if self.current_trial_id == trial_id:
            return  # No change, don't trigger UI updates

        self.current_trial_id = trial_id

        # Notify app
        self.app.on_trial_selected(
            self.current_patient_id,
            self.current_plans.get(self.current_plan_id),
            self.current_trials.get(trial_id)
        )

    def clear_data(self):
        """Clear all data from the left sidebar."""
        # Reset current selections
        self.current_patient_id = None
        self.current_plan_id = None
        self.current_plans = {}
        self.current_trial_id = None
        self.current_trials = {}

        # Reset programmatic selection flag
        self._programmatic_selection = False

        # Clear all listboxes
        self.patients_listbox.delete(*self.patients_listbox.get_children())
        self.plans_listbox.delete(*self.plans_listbox.get_children())
        self.trials_listbox.delete(*self.trials_listbox.get_children())