"""
ROI panel widget for displaying and controlling ROI visibility and rendering.
"""

import tkinter as tk
import ttkbootstrap as ttk
from typing import List, Optional, Callable, Dict, Any

from pinnacle_io.models.roi import ROI
from pinnacle_io.ui.constants import get_pinnacle_hex_color
from pinnacle_io.ui.widgets.base_overlay_panel import BaseOverlayPanel
from pinnacle_io.utils.coordinate_transforms import (
    transform_coordinates_for_rois,
    needs_coordinate_transform
)


class ROIPanel(BaseOverlayPanel):
    """Panel for displaying ROIs with checkboxes to control visibility in CT viewer."""

    def __init__(self, parent, ct_viewer=None, on_roi_visibility_changed: Optional[Callable[[ROI, bool], None]] = None):
        """
        Initialize the ROI panel.

        Args:
            parent: Parent widget
            ct_viewer: Reference to CT viewer for coordinate system access
            on_roi_visibility_changed: Callback function called when ROI visibility changes.
                                     Takes ROI object and visibility boolean as parameters.
        """
        super().__init__(parent, ct_viewer)
        self.on_roi_visibility_changed = on_roi_visibility_changed

        # ROI-specific data
        self.current_rois: List[ROI] = []
        self.patient_position: Optional[str] = None
        self.roi_checkboxes: dict[int, tk.BooleanVar] = {}  # roi_number -> checkbox var
        self._transformation_applied: Optional[str] = None  # Track transformation state

    def _setup_ui(self):
        """Set up the ROI panel UI components."""
        # Title
        title_label = ttk.Label(
            self,
            text="ROIs",
            font=("Arial", 10, "bold")
        )
        title_label.pack(fill=tk.X, padx=5, pady=(5, 10))

        # Create scrollable frame for ROI list
        self._create_scrollable_roi_list()

        # Control buttons frame
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=5, pady=(5, 0))

        # Show All button
        show_all_btn = ttk.Button(
            control_frame,
            text="Show All",
            command=self._show_all_rois
        )
        show_all_btn.pack(side=tk.LEFT, padx=(0, 5))

        # Hide All button
        hide_all_btn = ttk.Button(
            control_frame,
            text="Hide All",
            command=self._hide_all_rois
        )
        hide_all_btn.pack(side=tk.LEFT)

    def _create_scrollable_roi_list(self):
        """Create a scrollable frame for the ROI list."""
        # Create frame with scrollbar
        list_frame = ttk.Frame(self)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # Create canvas and scrollbar
        self.canvas = tk.Canvas(list_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.canvas.yview)

        # Create scrollable frame inside canvas
        self.scrollable_frame = ttk.Frame(self.canvas)

        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.canvas.bind("<Button-4>", self._on_mousewheel)
        self.canvas.bind("<Button-5>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        delta = 0
        if hasattr(event, 'delta'):
            delta = event.delta
        elif hasattr(event, 'num'):
            delta = -1 if event.num == 5 else 1

        if delta:
            self.canvas.yview_scroll(int(-1 * (delta / 120)), "units")

    def load_data(self, data: List[ROI], patient_position: Optional[str] = None, **kwargs):
        """
        Load ROI data into the panel.

        Args:
            data: List of ROI objects to display
            patient_position: Patient position for coordinate transformation
            **kwargs: Additional parameters
        """
        self.current_rois = data
        self.patient_position = patient_position
        self.visibility_state.clear()
        self.roi_checkboxes.clear()

        # Reset transformation state when new data is loaded
        self._transformation_applied = None

        # Apply coordinate transformations to current_rois
        self.apply_coordinate_transforms(patient_position)

        # Clear existing widgets
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # Create checkbox for each ROI
        for roi in self.current_rois:
            self._create_roi_checkbox(roi)

        # Update canvas scroll region
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def _create_roi_checkbox(self, roi: ROI):
        """Create a checkbox widget for an ROI."""
        roi_number = roi.roi_number if roi.roi_number is not None else 0
        roi_name = roi.name if roi.name else f"ROI {roi_number}"

        # Create frame for this ROI
        roi_frame = ttk.Frame(self.scrollable_frame)
        roi_frame.pack(fill=tk.X, pady=1)

        # Create checkbox variable
        checkbox_var = tk.BooleanVar(value=True)  # Default to visible
        self.roi_checkboxes[roi_number] = checkbox_var
        self.visibility_state[roi_number] = True

        # Create checkbox
        checkbox = ttk.Checkbutton(
            roi_frame,
            text=roi_name,
            variable=checkbox_var,
            command=lambda r=roi: self._on_roi_checkbox_changed(r)
        )
        checkbox.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Add color indicator if ROI has color information
        if hasattr(roi, 'color') and roi.color:
            self._add_color_indicator(roi_frame, roi.color)

    def _add_color_indicator(self, parent_frame: ttk.Frame, color_str: str):
        """Add a color indicator for the ROI."""
        try:
            color = get_pinnacle_hex_color(color_str)

            # Create small colored frame
            color_frame = tk.Frame(
                parent_frame,
                width=12,
                height=12,
                bg=color,
                relief="solid",
                borderwidth=1
            )
            color_frame.pack(side=tk.RIGHT, padx=(5, 0))
            color_frame.pack_propagate(False)

        except Exception:
            # If color parsing fails, just skip the color indicator
            pass

    def _on_roi_checkbox_changed(self, roi: ROI):
        """Handle ROI checkbox state change."""
        roi_number = roi.roi_number if roi.roi_number is not None else 0

        if roi_number in self.roi_checkboxes:
            is_visible = self.roi_checkboxes[roi_number].get()
            self.visibility_state[roi_number] = is_visible

            # Call callback if provided
            if self.on_roi_visibility_changed:
                self.on_roi_visibility_changed(roi, is_visible)

            # Request CT viewer refresh
            self.request_refresh()

    def _show_all_rois(self):
        """Show all ROIs."""
        for roi_number, checkbox_var in self.roi_checkboxes.items():
            checkbox_var.set(True)
            self.visibility_state[roi_number] = True

        # Trigger callbacks for all ROIs
        if self.on_roi_visibility_changed:
            for roi in self.current_rois:
                self.on_roi_visibility_changed(roi, True)

        # Request refresh
        self.request_refresh()

    def _hide_all_rois(self):
        """Hide all ROIs."""
        for roi_number, checkbox_var in self.roi_checkboxes.items():
            checkbox_var.set(False)
            self.visibility_state[roi_number] = False

        # Trigger callbacks for all ROIs
        if self.on_roi_visibility_changed:
            for roi in self.current_rois:
                self.on_roi_visibility_changed(roi, False)

        # Request refresh
        self.request_refresh()

    def is_roi_visible(self, roi: ROI) -> bool:
        """
        Check if an ROI is currently visible.

        Args:
            roi: ROI object to check

        Returns:
            bool: True if ROI is visible, False otherwise
        """
        roi_number = roi.roi_number if roi.roi_number is not None else 0
        return self.roi_visibility.get(roi_number, False)

    def get_visible_rois(self) -> List[ROI]:
        """
        Get list of currently visible ROIs.

        Returns:
            List[ROI]: List of visible ROI objects
        """
        visible_rois = []
        for roi in self.current_rois:
            if self.is_roi_visible(roi):
                visible_rois.append(roi)
        return visible_rois

    def get_roi_curves_for_slice(self, roi: ROI, z_coordinate: float, tolerance: float = 0.5) -> List:
        """
        Get ROI curves that intersect with a specific Z-coordinate slice.

        Args:
            roi: ROI object containing curves
            z_coordinate: Z-coordinate of the slice in millimeters
            tolerance: Tolerance for Z-coordinate matching in millimeters

        Returns:
            List: List of curves that intersect the specified slice
        """
        if not hasattr(roi, 'curve_list') or not roi.curve_list:
            return []

        matching_curves = []
        for curve in roi.curve_list:
            if hasattr(curve, 'z_position') and curve.z_position is not None:
                # Check if curve's Z position is within tolerance of target slice
                if abs(curve.z_position - z_coordinate) <= tolerance:
                    matching_curves.append(curve)

        return matching_curves

    # Methods required by BaseOverlayPanel interface

    def render_overlays(self, canvas: tk.Canvas, current_slice_z: float, display_params: Dict[str, Any]):
        """
        Render ROI overlays on the CT viewer canvas.

        Args:
            canvas: Canvas to draw on
            current_slice_z: Current slice Z-coordinate
            display_params: Display parameters (scale, center, etc.)
        """
        # Performance optimization: check if display parameters changed
        params_changed = self._cache_display_params(display_params)

        # Use current_rois which now contains transformed data
        if not self.current_rois:
            return

        # Check if we have valid display parameters
        if not display_params or display_params['scale'] <= 0:
            return

        # Clear existing ROI overlays only when needed
        self.clear_overlays(canvas, "roi_overlay")

        # Batch visible ROI processing for better performance
        visible_rois = [(roi, roi.roi_number if roi.roi_number is not None else 0)
                       for roi in self.current_rois
                       if self.visibility_state.get(roi.roi_number if roi.roi_number is not None else 0, False)]

        # Draw ROI curves for visible ROIs
        for roi, roi_number in visible_rois:
            # Get curves for current slice
            curves = self._get_roi_curves_for_current_slice(roi, current_slice_z)
            roi_color = roi.color

            for curve in curves:
                self._draw_curve_on_canvas(curve, display_params, roi_color, canvas)

    def update_visibility(self, item_id: Any, is_visible: bool):
        """
        Update visibility of specific ROI.

        Args:
            item_id: ROI number or ROI object
            is_visible: Whether ROI should be visible
        """
        if isinstance(item_id, ROI):
            roi_number = item_id.roi_number if item_id.roi_number is not None else 0
        else:
            roi_number = item_id

        self.visibility_state[roi_number] = is_visible

        # Update checkbox if it exists
        if roi_number in self.roi_checkboxes:
            self.roi_checkboxes[roi_number].set(is_visible)

        # Request refresh
        self.request_refresh()

    def get_visible_items(self) -> List[ROI]:
        """
        Get list of currently visible ROIs.

        Returns:
            List of visible ROI objects
        """
        visible_rois = []

        for roi in self.current_rois:
            roi_number = roi.roi_number if roi.roi_number is not None else 0
            if self.visibility_state.get(roi_number, False):
                visible_rois.append(roi)

        return visible_rois

    def apply_coordinate_transforms(self, patient_position: Optional[str]):
        """
        Apply coordinate transformations for patient position.

        Args:
            patient_position: Patient position for coordinate transformation
        """
        # Skip if already transformed to this position (avoid redundant operations)
        if self._transformation_applied == patient_position:
            return

        self.patient_position = patient_position

        # Handle ROI transformations
        if not self.current_rois or not patient_position:
            # No ROIs or patient position available, keep current data unchanged
            return
        else:
            try:
                # Check if coordinate transformation is needed for this patient position
                if needs_coordinate_transform(patient_position):
                    print(f"Applying ROI coordinate transformation for patient position: {patient_position}")
                    # NEW: Use in-place transformation instead of creating copies
                    transform_coordinates_for_rois(self.current_rois, patient_position, inplace=True)
                    self._transformation_applied = patient_position
                else:
                    print(f"No ROI coordinate transformation needed for patient position: {patient_position}")
                    # No transformation needed, but still track state
                    self._transformation_applied = patient_position

            except Exception as e:
                print(f"Error applying ROI coordinate transformation: {e}")
                # Keep original data if transformation fails

    def _get_roi_curves_for_current_slice(self, roi: ROI, current_z: float, tolerance: float = 0.05) -> List:
        """Get ROI curves that intersect with the current slice."""
        if not hasattr(roi, 'curve_list') or not roi.curve_list:
            return []

        matching_curves = []
        for curve in roi.curve_list:
            # Get Z coordinate from curve
            curve_z = self._get_curve_z_position(curve)

            if curve_z is not None:
                # Check if curve's Z position is within tolerance of current slice
                if abs(curve_z - current_z) <= tolerance:
                    matching_curves.append(curve)

        return matching_curves

    def _get_curve_z_position(self, curve):
        """Extract Z position from a curve."""
        # First try the z_position attribute
        if hasattr(curve, 'z_position') and curve.z_position is not None:
            return curve.z_position

        # If no z_position, extract from points (all points should have same Z)
        if hasattr(curve, 'points') and len(curve.points) > 0:
            try:
                # Points should be a numpy array of shape (N, 3)
                points = curve.points
                if len(points.shape) == 2 and points.shape[1] >= 3:
                    # Get Z coordinate from first point (all should be the same)
                    return float(points[0, 2])
            except Exception:
                pass

        return None

    def _draw_curve_on_canvas(self, curve, display_params: Dict[str, Any], color: str, canvas: tk.Canvas):
        """Draw a single ROI curve on the canvas."""
        try:
            # Get curve points
            if not hasattr(curve, 'points') or len(curve.points) == 0:
                return

            points = curve.points  # This should be a numpy array of shape (N, 3)

            if len(points) < 2:
                return

            # Convert world coordinates to display coordinates
            display_points = []
            for point in points:
                display_coords = self.world_to_display_coordinates(
                    point[0], point[1], point[2], display_params
                )
                if display_coords:
                    display_points.extend(display_coords)

            # Draw the curve as a polygon outline
            if len(display_points) >= 6:  # At least 3 points (6 coordinates)
                canvas.create_polygon(
                    display_points,
                    outline=get_pinnacle_hex_color(color),
                    fill="",
                    width=2,
                    tags="roi_overlay"
                )

        except Exception as e:
            print(f"Error drawing ROI curve: {e}")

    def clear_data(self):
        """Clear all data from the ROI panel."""
        # Clear ROI data
        self.current_rois = []
        self.patient_position = None
        self.roi_checkboxes.clear()
        self.visibility_state.clear()
        self._transformation_applied = None  # Reset transformation state

        # Clear existing widgets
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # Update canvas scroll region
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))