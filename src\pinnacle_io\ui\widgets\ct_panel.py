"""
CT panel widget for displaying CT image information and controls.
"""

import tkinter as tk
import ttkbootstrap as ttk
from typing import Optional, Callable, Dict, Any, List
import numpy as np

from pinnacle_io.models.image_set import ImageSet
from pinnacle_io.ui.widgets.base_overlay_panel import BaseOverlayPanel
from pinnacle_io.ui.constants import DEFAULT_WINDOW_WIDTH, DEFAULT_WINDOW_LEVEL

class CTPanel(BaseOverlayPanel):
    """Panel for displaying CT image information and controls."""

    def __init__(self, parent, ct_viewer=None, on_slice_changed: Optional[Callable[[int], None]] = None,
                 on_window_width_changed: Optional[Callable[[int], None]] = None,
                 on_window_level_changed: Optional[Callable[[int], None]] = None,
                 on_zoom_changed: Optional[Callable[[float], None]] = None):
        """
        Initialize the CT panel.

        Args:
            parent: Parent widget
            ct_viewer: Reference to CT viewer for coordinate system access
            on_slice_changed: Callback function called when slice changes
            on_window_width_changed: Callback function called when window width changes
            on_window_level_changed: Callback function called when window level changes
            on_zoom_changed: Callback function called when zoom changes
        """
        self.on_slice_changed = on_slice_changed
        self.on_window_width_changed = on_window_width_changed
        self.on_window_level_changed = on_window_level_changed
        self.on_zoom_changed = on_zoom_changed

        # CT-specific data
        self.current_image_set: Optional[ImageSet] = None
        self.current_total_slices = 0  # Track total slices for current orientation

        # Control variables
        self.slice_var = tk.IntVar(value=0)
        self.window_width_var = tk.IntVar(value=DEFAULT_WINDOW_WIDTH)
        self.window_level_var = tk.IntVar(value=DEFAULT_WINDOW_LEVEL)
        self.zoom_var = tk.DoubleVar(value=1.0)

        # UI components
        self.slice_info_var = tk.StringVar(value="Slice: 0 / 0")
        self.window_width_label = None
        self.window_level_label = None
        self.zoom_label = None

        # Image information display variables
        self.db_name_var = tk.StringVar(value="")
        self.series_description_var = tk.StringVar(value="")
        self.series_date_time_var = tk.StringVar(value="")
        self.patient_position_var = tk.StringVar(value="")

        super().__init__(parent, ct_viewer)

        # Register for orientation change notifications
        if ct_viewer:
            ct_viewer.on_orientation_changed_callback = self._on_orientation_changed

    def _setup_ui(self):
        """Set up the CT panel UI components."""
        # Title
        title_label = ttk.Label(
            self,
            text="CT Images",
            font=("Arial", 10, "bold")
        )
        title_label.pack(fill=tk.X, padx=5, pady=(5, 10))

        # Image Information frame
        self._create_image_info_frame()

        # Image Control frame
        self._create_image_control_frame()

    def _create_image_info_frame(self):
        """Create the Image Information frame."""
        info_frame = ttk.LabelFrame(self, text="Image Information", padding=10)
        info_frame.pack(fill=tk.X, padx=5, pady=(0, 10))

        # Basic information
        basic_info_frame = ttk.Frame(info_frame)
        basic_info_frame.pack(fill=tk.X, pady=(0, 10))

        # DB Name
        ttk.Label(basic_info_frame, text="Name:", font=("Arial", 9, "bold")).grid(row=0, column=0, sticky="w", padx=(0, 5))
        ttk.Label(basic_info_frame, textvariable=self.db_name_var, font=("Arial", 9)).grid(row=0, column=1, sticky="w")

        # Series Description
        ttk.Label(basic_info_frame, text="Series:", font=("Arial", 9, "bold")).grid(row=1, column=0, sticky="w", padx=(0, 5))
        ttk.Label(basic_info_frame, textvariable=self.series_description_var, font=("Arial", 9)).grid(row=1, column=1, sticky="w")

        # Date/Time
        ttk.Label(basic_info_frame, text="Date:", font=("Arial", 9, "bold")).grid(row=2, column=0, sticky="w", padx=(0, 5))
        ttk.Label(basic_info_frame, textvariable=self.series_date_time_var, font=("Arial", 9)).grid(row=2, column=1, sticky="w")

        # Patient Position
        ttk.Label(basic_info_frame, text="Position:", font=("Arial", 9, "bold")).grid(row=3, column=0, sticky="w", padx=(0, 5))
        ttk.Label(basic_info_frame, textvariable=self.patient_position_var, font=("Arial", 9)).grid(row=3, column=1, sticky="w")

        # Configure grid weights
        basic_info_frame.columnconfigure(1, weight=1)

        # CT Geometry table
        self._create_geometry_table(info_frame)

    def _create_geometry_table(self, parent):
        """Create the CT geometry information table."""
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.X, pady=(5, 0))

        # Headers
        headers = ["", "X (L-R)", "Y (A-P)", "Z (S-I)"]
        for col, header in enumerate(headers):
            ttk.Label(table_frame, text=header, font=("Arial", 9, "bold")).grid(
                row=0, column=col, padx=5, pady=2, sticky="w"
            )

        # Data rows
        rows = [
            ("Origin (cm):", "origin_x", "origin_y", "origin_z"),
            ("End (cm):", "end_x", "end_y", "end_z"),
            ("Voxel Size (cm):", "voxel_x", "voxel_y", "voxel_z"),
            ("Voxels:", "dim_x", "dim_y", "dim_z")
        ]

        # Store variables for updating
        self.geometry_vars = {}

        for row_idx, (label, x_key, y_key, z_key) in enumerate(rows, 1):
            ttk.Label(table_frame, text=label, font=("Arial", 9, "bold")).grid(
                row=row_idx, column=0, padx=5, pady=2, sticky="w"
            )

            # Create variables and labels for each column
            for col_idx, key in enumerate([x_key, y_key, z_key], 1):
                var = tk.StringVar(value="")
                self.geometry_vars[key] = var
                ttk.Label(table_frame, textvariable=var, font=("Arial", 9)).grid(
                    row=row_idx, column=col_idx, padx=5, pady=2, sticky="w"
                )

        # Configure grid weights
        for col in range(4):
            table_frame.columnconfigure(col, weight=1)

    def _create_image_control_frame(self):
        """Create the Image Control frame with slice, window/level, and zoom controls."""
        control_frame = ttk.LabelFrame(self, text="Image Control", padding=10)
        control_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Slice controls
        slice_section = ttk.Frame(control_frame)
        slice_section.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(slice_section, text="Slice:", font=("Arial", 9, "bold")).pack(anchor="w")

        # Slice slider
        self.slice_slider = ttk.Scale(
            slice_section,
            from_=0,
            to=100,
            orient=tk.HORIZONTAL,
            variable=self.slice_var,
            command=self._on_slice_change
        )
        self.slice_slider.pack(fill=tk.X, pady=(2, 5))

        # Slice info
        slice_info_frame = ttk.Frame(slice_section)
        slice_info_frame.pack(fill=tk.X)

        ttk.Label(slice_info_frame, textvariable=self.slice_info_var, font=("Arial", 9)).pack(side=tk.LEFT)
        ttk.Label(slice_info_frame, text="↑↓, PgUp/PgDn, Mouse wheel",
                 font=("Arial", 8), foreground="gray").pack(side=tk.RIGHT)

        # Window/Level controls
        wl_section = ttk.Frame(control_frame)
        wl_section.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(wl_section, text="Window/Level:", font=("Arial", 9, "bold")).pack(anchor="w")

        # Window width
        window_frame = ttk.Frame(wl_section)
        window_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Label(window_frame, text="Window:", font=("Arial", 9)).pack(side=tk.LEFT)
        window_width_scale = ttk.Scale(
            window_frame,
            from_=1,
            to=4000,
            orient=tk.HORIZONTAL,
            variable=self.window_width_var,
            command=self._on_window_width_change
        )
        window_width_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 10))

        self.window_width_label = ttk.Label(window_frame, text=str(DEFAULT_WINDOW_WIDTH), font=("Arial", 9))
        self.window_width_label.pack(side=tk.LEFT)

        # Window level
        level_frame = ttk.Frame(wl_section)
        level_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(level_frame, text="Level:", font=("Arial", 9)).pack(side=tk.LEFT)
        window_level_scale = ttk.Scale(
            level_frame,
            from_=-1000,
            to=3000,
            orient=tk.HORIZONTAL,
            variable=self.window_level_var,
            command=self._on_window_level_change
        )
        window_level_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 10))

        self.window_level_label = ttk.Label(level_frame, text=str(DEFAULT_WINDOW_LEVEL), font=("Arial", 9))
        self.window_level_label.pack(side=tk.LEFT)

        # Zoom controls
        zoom_section = ttk.Frame(control_frame)
        zoom_section.pack(fill=tk.X)

        ttk.Label(zoom_section, text="Zoom:", font=("Arial", 9, "bold")).pack(anchor="w")

        zoom_frame = ttk.Frame(zoom_section)
        zoom_frame.pack(fill=tk.X, pady=(2, 0))

        ttk.Label(zoom_frame, text="Zoom:", font=("Arial", 9)).pack(side=tk.LEFT)
        zoom_scale = ttk.Scale(
            zoom_frame,
            from_=0.1,
            to=10.0,
            orient=tk.HORIZONTAL,
            variable=self.zoom_var,
            command=self._on_zoom_change
        )
        zoom_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 10))

        self.zoom_label = ttk.Label(zoom_frame, text="1.0x", font=("Arial", 9))
        self.zoom_label.pack(side=tk.LEFT)

        # Zoom hints
        ttk.Label(zoom_section, text="Ctrl+Wheel, +/-, Home=Reset",
                 font=("Arial", 8), foreground="gray").pack(anchor="e", pady=(2, 0))

    def load_data(self, data: Optional[ImageSet], **kwargs):
        """
        Load CT image set data into the panel.

        Args:
            data: ImageSet object to display, or None to clear
            **kwargs: Additional parameters
        """
        self.current_image_set = data

        if data is None:
            self._clear_image_info()
            self._reset_controls()
            return

        # Update image information
        self._update_image_info(data)

        # Update control ranges based on image data
        self._update_control_ranges(data)

    def _clear_image_info(self):
        """Clear all image information displays."""
        self.db_name_var.set("")
        self.series_description_var.set("")
        self.series_date_time_var.set("")
        self.patient_position_var.set("")

        # Clear geometry table
        for var in self.geometry_vars.values():
            var.set("")

    def _update_image_info(self, image_set: ImageSet):
        """Update image information display."""
        # Basic information
        self.db_name_var.set(getattr(image_set, 'db_name', '') or '')
        self.series_description_var.set(getattr(image_set, 'series_description', '') or '')
        self.series_date_time_var.set(getattr(image_set, 'series_date_time', '') or '')
        self.patient_position_var.set(getattr(image_set, 'patient_position', '') or '')

        # Geometry information
        try:
            # Get dimensions and spacing
            x_dim = getattr(image_set, 'x_dim', None) or 0
            y_dim = getattr(image_set, 'y_dim', None) or 0
            z_dim = getattr(image_set, 'z_dim', None) or 0

            x_pixdim = getattr(image_set, 'x_pixdim', None) or 0.1
            y_pixdim = getattr(image_set, 'y_pixdim', None) or 0.1
            z_pixdim = getattr(image_set, 'z_pixdim', None) or 0.25

            x_start = getattr(image_set, 'x_start_dicom', None) or 0.0
            y_start = getattr(image_set, 'y_start_dicom', None) or 0.0
            z_start = getattr(image_set, 'z_start', None) or 0.0

            # Calculate end positions (in cm)
            x_end = x_start + (x_dim * x_pixdim)
            y_end = y_start + (y_dim * y_pixdim)
            z_end = z_start + (z_dim * z_pixdim)

            # Update geometry variables
            self.geometry_vars['origin_x'].set(f"{x_start:.2f}")
            self.geometry_vars['origin_y'].set(f"{y_start:.2f}")
            self.geometry_vars['origin_z'].set(f"{z_start:.2f}")

            self.geometry_vars['end_x'].set(f"{x_end:.2f}")
            self.geometry_vars['end_y'].set(f"{y_end:.2f}")
            self.geometry_vars['end_z'].set(f"{z_end:.2f}")

            self.geometry_vars['voxel_x'].set(f"{x_pixdim:.3f}")
            self.geometry_vars['voxel_y'].set(f"{y_pixdim:.3f}")
            self.geometry_vars['voxel_z'].set(f"{z_pixdim:.3f}")

            self.geometry_vars['dim_x'].set(str(x_dim))
            self.geometry_vars['dim_y'].set(str(y_dim))
            self.geometry_vars['dim_z'].set(str(z_dim))

        except Exception as e:
            print(f"Error updating geometry info: {e}")
            # Set all to empty strings on error
            for var in self.geometry_vars.values():
                var.set("")

    def _update_control_ranges(self, image_set: ImageSet):
        """Update control ranges based on image data."""
        try:
            z_dim = getattr(image_set, 'z_dim', None) or 1

            # Initialize total slices for axial orientation (default)
            self.current_total_slices = z_dim

            # Update slice slider range
            self.slice_slider.configure(to=max(1, z_dim - 1))
            self.slice_var.set(z_dim // 2)  # Start at middle slice

            # Update slice info
            self._update_slice_info()

        except Exception as e:
            print(f"Error updating control ranges: {e}")

    def _reset_controls(self):
        """Reset all controls to default values."""
        self.slice_var.set(0)
        self.window_width_var.set(DEFAULT_WINDOW_WIDTH)
        self.window_level_var.set(DEFAULT_WINDOW_LEVEL)
        self.zoom_var.set(1.0)
        self.current_total_slices = 0

        self.slice_info_var.set("Slice: 0 / 0")

        if self.window_width_label:
            self.window_width_label.config(text=str(DEFAULT_WINDOW_WIDTH))
        if self.window_level_label:
            self.window_level_label.config(text=str(DEFAULT_WINDOW_LEVEL))
        if self.zoom_label:
            self.zoom_label.config(text="1.0x")

    def _on_slice_change(self, value):
        """Handle slice slider change."""
        try:
            slice_index = int(float(value))
            self._update_slice_info()

            if self.on_slice_changed:
                self.on_slice_changed(slice_index)

        except ValueError:
            pass

    def _on_window_width_change(self, value):
        """Handle window width slider change."""
        try:
            width = int(float(value))
            if self.window_width_label:
                self.window_width_label.config(text=str(width))

            if self.on_window_width_changed:
                self.on_window_width_changed(width)

        except ValueError:
            pass

    def _on_window_level_change(self, value):
        """Handle window level slider change."""
        try:
            level = int(float(value))
            if self.window_level_label:
                self.window_level_label.config(text=str(level))

            if self.on_window_level_changed:
                self.on_window_level_changed(level)

        except ValueError:
            pass

    def _on_zoom_change(self, value):
        """Handle zoom slider change."""
        try:
            zoom = float(value)
            if self.zoom_label:
                self.zoom_label.config(text=f"{zoom:.1f}x")

            if self.on_zoom_changed:
                self.on_zoom_changed(zoom)

        except ValueError:
            pass

    def _update_slice_info(self):
        """Update slice information display."""
        if self.current_image_set and self.current_total_slices > 0:
            current_slice = self.slice_var.get()
            self.slice_info_var.set(f"Slice: {current_slice + 1} / {self.current_total_slices}")
        else:
            self.slice_info_var.set("Slice: 0 / 0")

    # Methods required by BaseOverlayPanel interface

    def render_overlays(self, canvas: tk.Canvas, current_slice_z: float, display_params: Dict[str, Any]):
        """
        Render CT overlays on the canvas (currently none needed).

        Args:
            canvas: Canvas to draw on
            current_slice_z: Current slice Z-coordinate
            display_params: Display parameters (scale, center, etc.)
        """
        # CT panel doesn't render overlays, just provides information and controls
        pass

    def update_visibility(self, item_id: Any, is_visible: bool):
        """
        Update visibility of specific item (not applicable for CT panel).

        Args:
            item_id: Item identifier
            is_visible: Whether item should be visible
        """
        # Not applicable for CT panel
        pass

    def get_visible_items(self) -> List[Any]:
        """
        Get list of currently visible items (not applicable for CT panel).

        Returns:
            Empty list as CT panel doesn't manage visible items
        """
        return []

    def apply_coordinate_transforms(self, patient_position: Optional[str]):
        """
        Apply coordinate transformations (not applicable for CT panel).

        Args:
            patient_position: Patient position for coordinate transformation
        """
        # Not applicable for CT panel - CT is the reference coordinate system
        pass

    # Public methods for external control updates

    def update_slice(self, slice_index: int):
        """Update slice slider from external control."""
        self.slice_var.set(slice_index)
        self._update_slice_info()

    def update_window_width(self, width: int):
        """Update window width slider from external control."""
        self.window_width_var.set(width)
        if self.window_width_label:
            self.window_width_label.config(text=str(width))

    def update_window_level(self, level: int):
        """Update window level slider from external control."""
        self.window_level_var.set(level)
        if self.window_level_label:
            self.window_level_label.config(text=str(level))

    def update_zoom(self, zoom: float):
        """Update zoom slider from external control."""
        self.zoom_var.set(zoom)
        if self.zoom_label:
            self.zoom_label.config(text=f"{zoom:.1f}x")

    def _on_orientation_changed(self, orientation: str, total_slices: int, current_slice: int):
        """Handle orientation change from CT viewer."""
        del orientation  # Unused parameter

        # Store total slices for current orientation
        self.current_total_slices = total_slices

        # Update slice slider range
        self.slice_slider.configure(to=max(1, total_slices - 1))

        # Update current slice
        self.slice_var.set(current_slice)

        # Update slice info display
        self._update_slice_info()

    def register_ct_viewer(self, ct_viewer):
        """Override to register for orientation change notifications."""
        super().register_ct_viewer(ct_viewer)
        if ct_viewer:
            ct_viewer.on_orientation_changed_callback = self._on_orientation_changed

    def clear_data(self):
        """Clear all data from the CT panel."""
        # Clear image set data
        self.current_image_set = None
        self.current_total_slices = 0

        # Clear image information display
        self._clear_image_info()

        # Reset controls
        self._reset_controls()