"""
Right sidebar widget containing patient/plan/trial info and tabbed panels.
"""

import tkinter as tk
import ttkbootstrap as ttk
from .ct_panel import CTPanel
from .roi_panel import ROIPanel
from .poi_panel import POIPanel
from .beams_panel import BeamsPanel
from .dose_panel import DosePanel


class RightSidebar(ttk.Frame):
    """Right sidebar with info panel and tabbed interface."""

    def __init__(self, parent, app):
        """
        Initialize the right sidebar.

        Args:
            parent: Parent widget
            app: Main application instance
        """
        super().__init__(parent, width=400)
        self.app = app
        self.pack_propagate(False)  # Maintain fixed width

        # Current data
        self.current_patient = None
        self.current_plan_id = None
        self.current_trial = None
        self.current_rois = []
        self.current_points = []
        self.current_beams = []
        self.current_dose = None
        self.current_image_set = None

        self._setup_ui()

    def _setup_ui(self):
        """Set up the sidebar UI components."""
        # Title
        title_label = ttk.Label(
            self,
            text="Information",
            font=("Arial", 12, "bold"),
        )
        title_label.pack(fill=tk.X, padx=5, pady=(5, 10))

        # Info panel
        info_frame = ttk.LabelFrame(self, text="Current Selection", padding=10)
        info_frame.pack(fill=tk.X, padx=5, pady=(0, 10))

        # Patient info
        self.patient_var = tk.StringVar(value="Patient: None")
        self.patient_label = ttk.Label(
            info_frame,
            textvariable=self.patient_var,
            font=("Arial", 9)
        )
        self.patient_label.pack(fill=tk.X, pady=(0, 2))

        # Plan info
        self.plan_var = tk.StringVar(value="Plan: None")
        self.plan_label = ttk.Label(
            info_frame,
            textvariable=self.plan_var,
            font=("Arial", 9)
        )
        self.plan_label.pack(fill=tk.X, pady=2)

        # Trial info
        self.trial_var = tk.StringVar(value="Trial: None")
        self.trial_label = ttk.Label(
            info_frame,
            textvariable=self.trial_var,
            font=("Arial", 9)
        )
        self.trial_label.pack(fill=tk.X, pady=(2, 0))

        # Tabbed interface
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # Create CT panel
        self.ct_panel = CTPanel(
            self.notebook,
            ct_viewer=getattr(self.app, 'ct_viewer', None),
            on_slice_changed=self._on_slice_changed,
            on_window_width_changed=self._on_window_width_changed,
            on_window_level_changed=self._on_window_level_changed,
            on_zoom_changed=self._on_zoom_changed
        )
        self.notebook.add(self.ct_panel, text="CT")

        # Create ROI panel
        self.roi_panel = ROIPanel(
            self.notebook,
            ct_viewer=getattr(self.app, 'ct_viewer', None),
            on_roi_visibility_changed=self._on_roi_visibility_changed
        )
        self.notebook.add(self.roi_panel, text="ROIs")

        # Create POI panel
        self.poi_panel = POIPanel(
            self.notebook,
            ct_viewer=getattr(self.app, 'ct_viewer', None),
            on_poi_visibility_changed=self._on_poi_visibility_changed
        )
        self.notebook.add(self.poi_panel, text="POIs")

        # Create Beams panel
        self.beams_panel = BeamsPanel(
            self.notebook,
            ct_viewer=getattr(self.app, 'ct_viewer', None),
            on_beam_visibility_changed=self._on_beam_visibility_changed
        )
        self.notebook.add(self.beams_panel, text="Beams")

        # Create Dose panel
        self.dose_panel = DosePanel(
            self.notebook,
            ct_viewer=getattr(self.app, 'ct_viewer', None),
            on_isodose_visibility_changed=self._on_isodose_visibility_changed,
            app=self.app
        )
        self.notebook.add(self.dose_panel, text="Dose")

        # Register panels with CT viewer when it becomes available
        self._register_overlay_panels()

    def _register_overlay_panels(self):
        """Register overlay panels with the CT viewer."""
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            self.app.ct_viewer.register_overlay_panel(self.ct_panel)
            self.app.ct_viewer.register_overlay_panel(self.roi_panel)
            self.app.ct_viewer.register_overlay_panel(self.poi_panel)
            self.app.ct_viewer.register_overlay_panel(self.beams_panel)
            self.app.ct_viewer.register_overlay_panel(self.dose_panel)

    def update_patient_info(self, patient):
        """Update the patient information display."""
        self.current_patient = patient
        patient_name = getattr(patient, 'last_and_first_name', 'Unknown')
        mrn = getattr(patient, 'medical_record_number', 'Unknown')
        self.patient_var.set(f"Patient: {patient_name} (MRN: {mrn})")

    def update_image_set_info(self, image_set):
        """Update the image set information display."""
        self.current_image_set = image_set

        # Load image set data into the CT panel
        self.ct_panel.load_data(image_set)

        if image_set:
            image_name = getattr(image_set, 'image_name', 'Unknown')
            series_desc = getattr(image_set, 'series_description', 'Unknown')
            print(f"Updated image set info: {image_name} ({series_desc})")
        else:
            print("No image set data available")

    def update_plan_info(self, plan, rois, points):
        """Update the plan information display."""
        self.current_plan_id = plan.plan_id if plan else None
        self.current_rois = rois
        self.current_points = points

        if plan:
            plan_name = getattr(plan, 'name', f'Plan_{plan.plan_id}')
            self.plan_var.set(f"Plan: {plan_name}")
        else:
            self.plan_var.set("Plan: None")

        # Get patient position from CT viewer if available
        patient_position = None
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            patient_position = getattr(self.app.ct_viewer, 'patient_position', None)

        # Load ROIs into the ROI panel with patient position
        self.roi_panel.load_data(rois, patient_position=patient_position)

        # Load POIs into the POI panel with patient position
        self.poi_panel.load_data(points, patient_position=patient_position)

        print(f"Updated plan info for {plan_name if plan else 'None'} ({len(rois)} ROIs, {len(points)} POIs) with patient position: {patient_position}")

    def update_trial_info(self, trial):
        """Update the trial information display."""
        self.current_trial = trial
        trial_name = getattr(trial, 'name', 'Unknown Trial')
        self.trial_var.set(f"Trial: {trial_name}")

        # Get patient position from CT viewer if available
        patient_position = None
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            patient_position = getattr(self.app.ct_viewer, 'patient_position', None)

        # Load beams into the beams panel with patient position
        if trial and hasattr(trial, 'beam_list'):
            self.current_beams = trial.beam_list
            self.beams_panel.load_data(trial.beam_list, self.current_points, patient_position=patient_position)
        else:
            self.current_beams = []
            self.beams_panel.load_data([], self.current_points, patient_position=patient_position)

        print(f"Updated trial info for {trial_name} ({len(self.current_beams)} beams) with patient position: {patient_position}")

    def update_dose_info(self, dose, patient_position=None):
        """Update the dose information display."""
        self.current_dose = dose

        # Get patient position from CT viewer if not provided
        if patient_position is None and hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            patient_position = getattr(self.app.ct_viewer, 'patient_position', None)

        # Load dose data into the dose panel
        prescription = self.current_trial.prescription_list[0]
        prescription_dose = prescription.prescription_dose * prescription.number_of_fractions
        self.dose_panel.load_data(dose, patient_position=patient_position, prescription_isodose_cgy=prescription_dose)

    def _on_slice_changed(self, slice_index):
        """Handle slice changes from the CT panel."""
        # print(f"Slice changed: {slice_index}")
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            self.app.ct_viewer.set_slice(slice_index)

    def _on_window_width_changed(self, width):
        """Handle window width changes from the CT panel."""
        # print(f"Window width changed: {width}")
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            self.app.ct_viewer.set_window_width(width)

    def _on_window_level_changed(self, level):
        """Handle window level changes from the CT panel."""
        # print(f"Window level changed: {level}")
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            self.app.ct_viewer.set_window_level(level)

    def _on_zoom_changed(self, zoom):
        """Handle zoom changes from the CT panel."""
        # print(f"Zoom changed: {zoom}")
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            self.app.ct_viewer.set_zoom(zoom)

    def _on_roi_visibility_changed(self, roi, is_visible):
        """Handle ROI visibility changes from the ROI panel."""
        roi_name = roi.name if roi.name else f"ROI {roi.roi_number}"
        # print(f"ROI visibility changed: {roi_name} -> {'visible' if is_visible else 'hidden'}")

        # Trigger overlay refresh in the CT viewer
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            self.app.ct_viewer.refresh_overlays()

    def _on_poi_visibility_changed(self, poi, is_visible):
        """Handle POI visibility changes from the POI panel."""
        poi_name = poi.name if poi.name else f"POI {poi.id}"
        # print(f"POI visibility changed: {poi_name} -> {'visible' if is_visible else 'hidden'}")

        # Trigger overlay refresh in the CT viewer
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            self.app.ct_viewer.refresh_overlays()

    def _on_beam_visibility_changed(self, beam, is_visible):
        """Handle beam visibility changes from the beams panel."""
        beam_name = beam.name if beam.name else f"Beam {beam.beam_number}"
        # print(f"Beam visibility changed: {beam_name} -> {'visible' if is_visible else 'hidden'}")

        # Trigger overlay refresh in the CT viewer
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            self.app.ct_viewer.refresh_overlays()

    def _on_isodose_visibility_changed(self, level, is_visible):
        """Handle isodose visibility changes from the dose panel."""
        # print(f"Isodose visibility changed: {level}% -> {'visible' if is_visible else 'hidden'}")

        # Trigger overlay refresh in the CT viewer
        if hasattr(self.app, 'ct_viewer') and self.app.ct_viewer:
            self.app.ct_viewer.refresh_overlays()

    def clear_data(self):
        """Clear all data from the right sidebar."""
        # Reset current data
        self.current_patient = None
        self.current_plan_id = None
        self.current_trial = None
        self.current_rois = []
        self.current_points = []
        self.current_beams = []
        self.current_dose = None
        self.current_image_set = None

        # Clear info panel
        self.patient_var.set("Patient: None")
        self.plan_var.set("Plan: None")
        self.trial_var.set("Trial: None")

        # Clear all panels
        self.ct_panel.clear_data()
        self.roi_panel.clear_data()
        self.poi_panel.clear_data()
        self.beams_panel.clear_data()
        self.dose_panel.clear_data()