"""
Writing data examples for pinnacle_io library.

This module demonstrates how to write Pinnacle data to directories.
Note: Writing functionality is currently not implemented in the library.
"""

from pinnacle_io import PinnacleReader
from pinnacle_io.models import Institution, Patient, Trial, ROI

def demonstrate_writing_limitations():
    """Demonstrate current limitations of writing functionality."""
    
    print("Writing Data Examples")
    print("=" * 30)
    
    print("IMPORTANT: Writing functionality is not yet implemented in pinnacle_io.")
    print("This example shows the intended API design for future implementation.")
    
    # Read some data first
    reader = PinnacleReader("/path/to/pinnacle/data")
    
    try:
        institution = reader.get_institution()
        print(f"\n✓ Successfully read institution: {institution.name}")
        
        # Show what the writing API would look like
        print("\nPlanned Writing API (not yet implemented):")
        print("from pinnacle_io import PinnacleWriter")
        print("")
        print("# Future implementation would look like:")
        print("writer = PinnacleWriter('/path/to/output')")
        print("writer.write_institution(institution)")
        print("writer.write_patient(patient)")
        print("writer.write_trials(trials)")
        print("")
        print("# Or using the direct writer classes:")
        print("from pinnacle_io.writers import InstitutionWriter, PatientWriter")
        print("InstitutionWriter.write('/path/to/output', institution)")
        print("PatientWriter.write('/path/to/output/Patient_1', patient)")
        
    except Exception as e:
        print(f"✗ Error reading data: {e}")

def show_model_serialization():
    """Show how models can be serialized for external use."""
    
    print("\nModel Serialization Examples:")
    print("-" * 30)
    
    reader = PinnacleReader("/path/to/pinnacle/data")
    
    try:
        institution = reader.get_institution()
        
        # Show model attributes that could be serialized
        print(f"Institution attributes:")
        print(f"  Name: {institution.name}")
        print(f"  Address: {institution.address}")
        print(f"  Phone: {institution.phone}")
        
        # Show how to access model data for custom serialization
        print(f"\nModel data access:")
        print(f"  Institution.__dict__ keys: {list(institution.__dict__.keys())}")
        
        # Example of converting to dictionary for JSON serialization
        print(f"\nCustom serialization example:")
        print("import json")
        print("# Convert model to dict (custom implementation needed)")
        print("institution_dict = {")
        print("    'name': institution.name,")
        print("    'address': institution.address,")
        print("    'phone': institution.phone")
        print("}")
        print("# Save as JSON")
        print("with open('institution.json', 'w') as f:")
        print("    json.dump(institution_dict, f, indent=2)")
        
    except Exception as e:
        print(f"✗ Error demonstrating serialization: {e}")

def show_data_modification():
    """Show how to modify data in memory before writing."""
    
    print("\nData Modification Examples:")
    print("-" * 30)
    
    reader = PinnacleReader("/path/to/pinnacle/data")
    
    try:
        institution = reader.get_institution()
        
        # Show how to modify data in memory
        print("Original institution name:", institution.name)
        
        # Modify the institution name
        original_name = institution.name
        institution.name = "Modified Institution Name"
        print("Modified institution name:", institution.name)
        
        # Restore original name
        institution.name = original_name
        print("Restored institution name:", institution.name)
        
        print("\nNote: These modifications are only in memory.")
        print("To persist changes, writing functionality would be needed.")
        
    except Exception as e:
        print(f"✗ Error demonstrating modification: {e}")

def show_export_alternatives():
    """Show alternative ways to export data."""
    
    print("\nExport Alternatives:")
    print("-" * 30)
    
    print("While native Pinnacle writing is not implemented, you can:")
    print("")
    print("1. Export to CSV:")
    print("   import csv")
    print("   # Export ROI data")
    print("   with open('rois.csv', 'w', newline='') as f:")
    print("       writer = csv.writer(f)")
    print("       writer.writerow(['Name', 'Type', 'Volume'])")
    print("       for roi in rois:")
    print("           writer.writerow([roi.name, roi.type, roi.volume])")
    print("")
    print("2. Export to JSON:")
    print("   import json")
    print("   # Export trial data")
    print("   trial_data = [{'name': t.name, 'dose': t.prescription_dose} for t in trials]")
    print("   with open('trials.json', 'w') as f:")
    print("       json.dump(trial_data, f, indent=2)")
    print("")
    print("3. Export to Excel:")
    print("   import pandas as pd")
    print("   # Export multiple datasets")
    print("   with pd.ExcelWriter('pinnacle_data.xlsx') as writer:")
    print("       roi_df.to_excel(writer, sheet_name='ROIs')")
    print("       trial_df.to_excel(writer, sheet_name='Trials')")
    print("")
    print("4. Export dose data as NIfTI:")
    print("   import nibabel as nib")
    print("   # Export dose distribution")
    print("   nii = nib.Nifti1Image(dose_array, affine_matrix)")
    print("   nib.save(nii, 'dose_distribution.nii.gz')")

def main():
    """Main example function."""
    
    print("Pinnacle IO Writing Examples")
    print("=" * 40)
    
    # Show current limitations
    demonstrate_writing_limitations()
    
    # Show model serialization
    show_model_serialization()
    
    # Show data modification
    show_data_modification()
    
    # Show export alternatives
    show_export_alternatives()
    
    print("\n" + "=" * 40)
    print("Writing examples completed!")
    print("\nSummary:")
    print("- Writing functionality is planned but not yet implemented")
    print("- Models can be modified in memory")
    print("- Data can be exported to CSV, JSON, Excel, or other formats")
    print("- Use alternative libraries for specific format exports")

if __name__ == "__main__":
    main()