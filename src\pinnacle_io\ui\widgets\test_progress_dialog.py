#!/usr/bin/env python3
"""
Test script for ProgressDialog and AsyncOperationRunner.

This script can be run independently to test the progress dialog functionality
without needing the full Pinnacle IO application.
"""

import os
import sys
import time
import threading
from pathlib import Path

# Add parent directories to path for importing
current_dir = Path(__file__).parent
root_dir = current_dir.parent.parent.parent
sys.path.insert(0, str(root_dir))

try:
    import ttkbootstrap as ttk
    from ttkbootstrap import Window
except ImportError:
    print("Error: ttkbootstrap is required but not installed.")
    print("Install it with: pip install ttkbootstrap")
    sys.exit(1)

from progress_dialog import ProgressDialog, AsyncOperationRunner
sys.path.insert(0, str(current_dir.parent))  # Add ui directory to path
from progress import ProgressManager, ProgressState


class ProgressDialogTestApp:
    """Test application for ProgressDialog functionality."""

    def __init__(self):
        """Initialize the test application."""
        self.root = Window(themename="darkly")
        self.root.title("Progress Dialog Test")
        self.root.geometry("400x600")  # Increased height for new buttons

        # Progress manager for testing
        self.progress_manager = None

        self.setup_ui()

    def setup_ui(self):
        """Set up the test UI."""
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Title
        title_label = ttk.Label(
            main_frame,
            text="Progress Dialog Tests",
            font=("default", 14, "bold")
        )
        title_label.pack(pady=(0, 20))

        # Test buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=10)

        # Simple progress test
        simple_btn = ttk.Button(
            button_frame,
            text="Simple Progress Test",
            command=self.test_simple_progress,
            width=25
        )
        simple_btn.pack(pady=5)

        # Cancelable progress test
        cancel_btn = ttk.Button(
            button_frame,
            text="Cancelable Progress Test",
            command=self.test_cancelable_progress,
            width=25
        )
        cancel_btn.pack(pady=5)

        # Async operation runner test
        async_btn = ttk.Button(
            button_frame,
            text="Async Operation Test",
            command=self.test_async_operation,
            width=25
        )
        async_btn.pack(pady=5)

        # Development mode test
        dev_btn = ttk.Button(
            button_frame,
            text="Development Mode Test",
            command=self.test_development_mode,
            width=25
        )
        dev_btn.pack(pady=5)

        # Fast operation test
        fast_btn = ttk.Button(
            button_frame,
            text="Fast Operation Test",
            command=self.test_fast_operation,
            width=25
        )
        fast_btn.pack(pady=5)

        # Separator
        separator = ttk.Separator(button_frame, orient="horizontal")
        separator.pack(fill="x", pady=(15, 10))

        # ProgressManager tests
        manager_label = ttk.Label(
            button_frame,
            text="ProgressManager Tests",
            font=("default", 10, "bold")
        )
        manager_label.pack(pady=(5, 10))

        # Multi-stage progress test
        multi_stage_btn = ttk.Button(
            button_frame,
            text="Multi-Stage Progress Test",
            command=self.test_multi_stage_progress,
            width=25
        )
        multi_stage_btn.pack(pady=5)

        # Progress state awareness test
        state_awareness_btn = ttk.Button(
            button_frame,
            text="Progress State Awareness Test",
            command=self.test_progress_state_awareness,
            width=25
        )
        state_awareness_btn.pack(pady=5)

        # Realistic data loading test
        realistic_btn = ttk.Button(
            button_frame,
            text="Realistic Data Loading Test",
            command=self.test_realistic_data_loading,
            width=25
        )
        realistic_btn.pack(pady=5)

        # Manual close test
        manual_close_btn = ttk.Button(
            button_frame,
            text="Manual Close Test",
            command=self.test_manual_close,
            width=25
        )
        manual_close_btn.pack(pady=5)

        # Environment info
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill="x", pady=(20, 0))

        env_var = os.getenv('PINNACLE_IO_DEV', 'Not Set')
        env_label = ttk.Label(
            info_frame,
            text=f"PINNACLE_IO_DEV: {env_var}",
            font=("default", 9)
        )
        env_label.pack()

        instructions = (
            "Set PINNACLE_IO_DEV=1 to enable development mode by default.\n"
            "Development mode shows detailed timing logs."
        )
        instr_label = ttk.Label(
            info_frame,
            text=instructions,
            font=("default", 8),
            justify="center"
        )
        instr_label.pack(pady=(10, 0))

    def test_simple_progress(self):
        """Test simple progress dialog without cancel capability."""
        dialog = ProgressDialog(
            parent=self.root,
            title="Simple Progress Test",
            cancelable=False,
            development_mode=False
        )

        def work():
            dialog.show("Starting simple test...", max_progress=50)

            for i in range(50):
                time.sleep(0.1)  # Simulate work

                if i % 5 == 0:
                    dialog.update_progress(i + 1, f"Processing item {i + 1}...")
                else:
                    dialog.update_progress(i + 1)

            dialog.complete_operation(success=True, final_message="Simple test completed!")

        threading.Thread(target=work, daemon=True).start()

    def test_cancelable_progress(self):
        """Test cancelable progress dialog."""
        dialog = ProgressDialog(
            parent=self.root,
            title="Cancelable Progress Test",
            cancelable=True,
            development_mode=False
        )

        def work():
            dialog.show("Starting cancelable test...", max_progress=100)

            for i in range(100):
                if dialog.is_cancelled_flag():
                    dialog.update_status("Cancelling...")
                    time.sleep(1)
                    break

                time.sleep(0.08)  # Simulate work

                if i % 10 == 0:
                    if not dialog.update_progress(i + 1, f"Step {i + 1}/100"):
                        break  # Cancelled
                else:
                    if not dialog.update_progress(i + 1):
                        break  # Cancelled

            if not dialog.is_cancelled_flag():
                dialog.complete_operation(success=True, final_message="Test completed!")
            else:
                dialog.complete_operation(success=False, final_message="Test cancelled")

        threading.Thread(target=work, daemon=True).start()

    def test_async_operation(self):
        """Test AsyncOperationRunner."""
        runner = AsyncOperationRunner(
            parent=self.root,
            title="Async Operation Test",
            cancelable=True,
            development_mode=False
        )

        def operation(progress_callback, status_callback, **kwargs):
            """Simulated async operation."""
            stages = [
                (10, "Initializing..."),
                (25, "Loading configuration..."),
                (40, "Processing data..."),
                (60, "Applying transformations..."),
                (80, "Validating results..."),
                (95, "Finalizing..."),
                (100, "Complete!")
            ]

            for progress, status in stages:
                if not progress_callback(progress, status):
                    raise InterruptedError("Operation cancelled")

                # Simulate variable work time
                time.sleep(0.5 + (progress * 0.01))

            return {"success": True, "message": "Operation completed successfully!"}

        def on_complete(result):
            print(f"Async operation completed: {result}")
            ttk.dialogs.Messagebox.show_info(
                title="Success",
                message=result.get('message', 'Operation completed'),
                parent=self.root
            )

        def on_error(error):
            print(f"Async operation failed: {error}")
            if not isinstance(error, InterruptedError):
                ttk.dialogs.Messagebox.show_error(
                    title="Error",
                    message=str(error),
                    parent=self.root
                )

        runner.run_operation(
            operation_func=operation,
            callback_on_complete=on_complete,
            callback_on_error=on_error
        )

    def test_development_mode(self):
        """Test development mode with timing logs."""
        dialog = ProgressDialog(
            parent=self.root,
            title="Development Mode Test",
            cancelable=True,
            development_mode=True  # Force development mode
        )

        def work():
            dialog.show("Starting development mode test...", max_progress=20)

            steps = [
                "Initializing system",
                "Loading configuration",
                "Parsing input data",
                "Validating data structure",
                "Processing dataset",
                "Applying filters",
                "Calculating metrics",
                "Generating reports",
                "Saving results",
                "Cleaning up"
            ]

            for i, step in enumerate(steps):
                if dialog.is_cancelled_flag():
                    dialog.update_status("Cancelling...")
                    time.sleep(1)
                    break

                # Variable work time to show timing differences
                work_time = 0.3 + (i * 0.1)
                time.sleep(work_time)

                progress = ((i + 1) / len(steps)) * 20
                if not dialog.update_progress(int(progress), f"{step}..."):
                    break

                # Add some sub-steps for detailed logging
                if i == 4:  # Processing dataset
                    time.sleep(0.2)
                    dialog.update_status(f"{step} - phase 1")
                    time.sleep(0.3)
                    dialog.update_status(f"{step} - phase 2")

            if not dialog.is_cancelled_flag():
                dialog.complete_operation(success=True, final_message="Development mode test completed!")
                print("\nTiming Report:")
                print(dialog.get_timing_report())
            else:
                dialog.complete_operation(success=False, final_message="Development mode test cancelled")

        threading.Thread(target=work, daemon=True).start()

    def test_fast_operation(self):
        """Test very fast operation to ensure dialog handles quick operations."""
        dialog = ProgressDialog(
            parent=self.root,
            title="Fast Operation Test",
            cancelable=False,
            development_mode=True
        )

        def work():
            dialog.show("Starting fast operation...", max_progress=10)

            for i in range(10):
                time.sleep(0.05)  # Very fast
                dialog.update_progress(i + 1, f"Quick step {i + 1}")

            dialog.complete_operation(success=True, final_message="Fast operation completed!")
            # In development mode, dialog stays open for review

        threading.Thread(target=work, daemon=True).start()

    # ProgressManager helper methods
    def start_progress_operation(self, operation_id: str, title: str, cancelable: bool = True) -> ProgressManager:
        """Start a new progress operation (mimicking MainWindow method)."""
        self.progress_manager = ProgressManager.get_or_create_instance(
            parent=self.root,
            operation_id=operation_id,
            title=title,
            cancelable=cancelable,
            development_mode=True  # Enable development mode for testing
        )
        return self.progress_manager

    def get_progress_manager(self) -> ProgressManager:
        """Get current progress manager (mimicking MainWindow method)."""
        return self.progress_manager

    # ProgressManager test methods
    def test_multi_stage_progress(self):
        """Test multi-stage progress management using ProgressManager."""
        def simulate_multi_stage_operation():
            """Simulate a multi-stage operation."""

            # Start progress operation
            progress_manager = self.start_progress_operation(
                operation_id="multi_stage_test",
                title="Multi-Stage Progress Test",
                cancelable=True
            )

            try:
                # Show initial dialog
                progress_manager.show("Initializing multi-stage test...", max_progress=100)

                # Stage 1: Data Loading (45% weight)
                progress_manager.register_stage("data_loading", weight=45, name="Data Loading")
                progress_manager.set_current_stage("data_loading")

                data_steps = ["Loading configuration", "Reading input files", "Validating data"]
                for i, step in enumerate(data_steps):
                    if progress_manager.is_cancelled_flag():
                        break

                    stage_progress = ((i + 1) / len(data_steps)) * 100
                    status = f"Stage 1: {step}..."

                    if not progress_manager.update_progress(stage_progress, status):
                        break  # Cancelled

                    time.sleep(0.5)

                progress_manager.complete_stage("data_loading")

                if progress_manager.is_cancelled_flag():
                    return

                # Stage 2: Processing (35% weight)
                progress_manager.register_stage("processing", weight=35, name="Data Processing")
                progress_manager.set_current_stage("processing")

                processing_steps = ["Transforming data", "Applying algorithms", "Computing results"]
                for i, step in enumerate(processing_steps):
                    if progress_manager.is_cancelled_flag():
                        break

                    stage_progress = ((i + 1) / len(processing_steps)) * 100
                    status = f"Stage 2: {step}..."

                    if not progress_manager.update_progress(stage_progress, status):
                        break

                    time.sleep(0.6)

                progress_manager.complete_stage("processing")

                if progress_manager.is_cancelled_flag():
                    return

                # Stage 3: Finalization (20% weight)
                progress_manager.register_stage("finalization", weight=20, name="Finalization")
                progress_manager.set_current_stage("finalization")

                final_steps = ["Generating output", "Saving results", "Cleanup"]
                for i, step in enumerate(final_steps):
                    if progress_manager.is_cancelled_flag():
                        break

                    stage_progress = ((i + 1) / len(final_steps)) * 100
                    status = f"Stage 3: {step}..."

                    if not progress_manager.update_progress(stage_progress, status):
                        break

                    time.sleep(0.4)

                progress_manager.complete_stage("finalization")

                # Complete the overall operation
                if not progress_manager.is_cancelled_flag():
                    progress_manager.complete_operation(
                        success=True,
                        final_message="Multi-stage test completed successfully!"
                    )
                    print("Multi-stage test completed successfully!")
                else:
                    progress_manager.complete_operation(
                        success=False,
                        final_message="Test cancelled by user"
                    )
                    print("Multi-stage test cancelled by user")

            except Exception as e:
                progress_manager.complete_operation(
                    success=False,
                    final_message=f"Test failed: {str(e)}"
                )
                print(f"Multi-stage test failed: {e}")

        # Run in separate thread
        threading.Thread(target=simulate_multi_stage_operation, daemon=True).start()

    def test_progress_state_awareness(self):
        """Test progress state awareness and operation coordination."""
        def simulate_aware_operation():
            """Simulate an operation that's aware of existing progress."""

            # Check if progress is already active
            existing_manager = self.get_progress_manager()

            if existing_manager and existing_manager.is_active():
                print("Another progress operation is already active!")
                print(f"Active operation: {existing_manager.operation_id}")

                # Join existing operation as a new stage
                existing_manager.register_stage("additional_work", weight=15, name="Additional Work")
                existing_manager.set_current_stage("additional_work")

                # Do our work within the existing operation
                work_items = ["Extra processing", "Additional validation", "Bonus cleanup"]
                for i, item in enumerate(work_items):
                    if existing_manager.is_cancelled_flag():
                        break

                    stage_progress = ((i + 1) / len(work_items)) * 100
                    status = f"Additional: {item}..."

                    if not existing_manager.update_progress(stage_progress, status):
                        break

                    time.sleep(0.7)

                existing_manager.complete_stage("additional_work")
                print("Additional work completed within existing operation")

                # Show a message to user
                self.root.after(0, lambda: ttk.dialogs.Messagebox.show_info(
                    title="State Awareness Test",
                    message="Successfully joined existing operation as an additional stage!",
                    parent=self.root
                ))

            else:
                # No active operation, start our own
                progress_manager = self.start_progress_operation(
                    operation_id="aware_operation",
                    title="State-Aware Operation Test",
                    cancelable=True
                )

                try:
                    progress_manager.show("Starting state-aware test...", max_progress=100)

                    # Single stage operation
                    items = ["Item 1", "Item 2", "Item 3", "Item 4", "Item 5"]
                    for i, item in enumerate(items):
                        if progress_manager.is_cancelled_flag():
                            break

                        progress = ((i + 1) / len(items)) * 100
                        status = f"Processing {item}..."

                        if not progress_manager.update_progress(progress, status):
                            break

                        time.sleep(0.8)

                    if not progress_manager.is_cancelled_flag():
                        progress_manager.complete_operation(
                            success=True,
                            final_message="State-aware operation completed!"
                        )
                        print("State-aware operation completed!")
                    else:
                        progress_manager.complete_operation(
                            success=False,
                            final_message="Operation cancelled"
                        )
                        print("State-aware operation cancelled")

                except Exception as e:
                    progress_manager.complete_operation(
                        success=False,
                        final_message=f"Operation failed: {str(e)}"
                    )
                    print(f"State-aware operation failed: {e}")

        # Run in separate thread
        threading.Thread(target=simulate_aware_operation, daemon=True).start()

    def test_realistic_data_loading(self):
        """Test realistic Pinnacle-like data loading workflow."""
        def simulate_realistic_loading():
            """Simulate the actual Pinnacle data loading workflow."""

            progress_manager = self.start_progress_operation(
                operation_id="pinnacle_data_load_test",
                title="Pinnacle Data Loading Simulation",
                cancelable=True
            )

            try:
                progress_manager.show("Initializing Pinnacle data loading simulation...", max_progress=100)

                # Stage 1: MainWindow Initial Data Loading (40% of total)
                progress_manager.register_stage("main_window_init", weight=40, name="Initial Data Loading")
                progress_manager.set_current_stage("main_window_init")

                main_steps = [
                    ("Institution data", 0.4),
                    ("Patient list", 0.3),
                    ("Auto-select patient", 0.5),
                    ("Patient data", 0.6),
                    ("Plan selection", 0.3)
                ]

                for i, (step_name, sleep_time) in enumerate(main_steps):
                    if progress_manager.is_cancelled_flag():
                        break

                    stage_progress = ((i + 1) / len(main_steps)) * 100
                    if not progress_manager.update_progress(stage_progress, f"Loading {step_name}..."):
                        break

                    time.sleep(sleep_time)

                progress_manager.complete_stage("main_window_init")

                # Stage 2: Plan Data Loading (35% of total)
                progress_manager.register_stage("plan_loading", weight=35, name="Plan Data Processing")
                progress_manager.set_current_stage("plan_loading")

                plan_steps = [
                    ("Trials data", 0.7),
                    ("ROIs data", 0.8),
                    ("Points data", 0.5),
                    ("CT image set", 1.0)
                ]

                for i, (step_name, sleep_time) in enumerate(plan_steps):
                    if progress_manager.is_cancelled_flag():
                        break

                    stage_progress = ((i + 1) / len(plan_steps)) * 100
                    if not progress_manager.update_progress(stage_progress, f"Loading {step_name}..."):
                        break

                    time.sleep(sleep_time)

                progress_manager.complete_stage("plan_loading")

                # Stage 3: UI Component Updates (25% of total)
                progress_manager.register_stage("ui_updates", weight=25, name="UI Component Updates")
                progress_manager.set_current_stage("ui_updates")

                ui_components = [
                    ("CT Viewer initialization", 0.4),
                    ("ROI Panel setup", 0.5),
                    ("POI Panel setup", 0.3),
                    ("Beams Panel calculation", 0.6),
                    ("Dose Panel processing", 0.8)
                ]

                for i, (component, sleep_time) in enumerate(ui_components):
                    if progress_manager.is_cancelled_flag():
                        break

                    component_progress = ((i + 1) / len(ui_components)) * 100
                    status = f"{component}..."

                    if not progress_manager.update_progress(component_progress, status):
                        break

                    time.sleep(sleep_time)

                progress_manager.complete_stage("ui_updates")

                # Complete the overall operation
                if not progress_manager.is_cancelled_flag():
                    progress_manager.complete_operation(
                        success=True,
                        final_message="Pinnacle data loading simulation completed successfully!"
                    )
                    print("Realistic data loading test completed!")
                else:
                    progress_manager.complete_operation(
                        success=False,
                        final_message="Data loading simulation cancelled by user"
                    )
                    print("Realistic data loading test cancelled")

            except Exception as e:
                progress_manager.complete_operation(
                    success=False,
                    final_message=f"Data loading simulation failed: {str(e)}"
                )
                print(f"Realistic data loading test failed: {e}")

        # Run in separate thread
        threading.Thread(target=simulate_realistic_loading, daemon=True).start()

    def test_manual_close(self):
        """Test manual close functionality in development mode."""
        def simulate_manual_close_operation():
            """Simulate an operation that demonstrates manual close feature."""

            progress_manager = self.start_progress_operation(
                operation_id="manual_close_test",
                title="Manual Close Test",
                cancelable=False  # No cancel, only manual close when done
            )

            try:
                progress_manager.show("Testing manual close functionality...", max_progress=100)

                # Single stage with detailed timing
                progress_manager.register_stage("detailed_work", weight=100, name="Detailed Processing")
                progress_manager.set_current_stage("detailed_work")

                detailed_steps = [
                    ("Initialization phase", 0.3),
                    ("Quick setup", 0.1),
                    ("Heavy computation", 1.2),
                    ("Data validation", 0.4),
                    ("More heavy work", 0.9),
                    ("Final verification", 0.2),
                    ("Cleanup phase", 0.3)
                ]

                for i, (step_name, sleep_time) in enumerate(detailed_steps):
                    stage_progress = ((i + 1) / len(detailed_steps)) * 100
                    status = f"{step_name}..."

                    progress_manager.update_progress(stage_progress, status)
                    time.sleep(sleep_time)

                progress_manager.complete_stage("detailed_work")

                # Complete operation - dialog will stay open in dev mode for review
                progress_manager.complete_operation(
                    success=True,
                    final_message="Operation completed! Dialog will remain open for timing review in development mode."
                )

                print("Manual close test completed - check the timing report in the dialog!")
                print("In development mode, the dialog stays open so you can review the timing data.")

            except Exception as e:
                progress_manager.complete_operation(
                    success=False,
                    final_message=f"Test failed: {str(e)}"
                )
                print(f"Manual close test failed: {e}")

        # Run in separate thread
        threading.Thread(target=simulate_manual_close_operation, daemon=True).start()

    def run(self):
        """Start the test application."""
        print("Starting Progress Dialog Test Application")
        print("=" * 60)
        print("Use the buttons to test different progress dialog features.")
        print(f"Development mode default: {os.getenv('PINNACLE_IO_DEV', 'Not Set')}")
        print("")
        print("NEW ProgressManager Tests:")
        print("• Multi-Stage Progress: Tests weighted stage coordination")
        print("• State Awareness: Tests joining existing operations")
        print("• Realistic Data Loading: Simulates Pinnacle workflow")
        print("• Manual Close: Tests development mode persistence")
        print("=" * 60)

        self.root.mainloop()


def main():
    """Main entry point for the test script."""
    print("Progress Dialog Test Script")
    print("Testing ProgressDialog and AsyncOperationRunner functionality")

    # Create and run test app
    app = ProgressDialogTestApp()
    app.run()


if __name__ == "__main__":
    main()