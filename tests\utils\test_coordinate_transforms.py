"""
Unit tests for coordinate transformation utilities.

This module tests the coordinate transformation functions for ROI and dose data
based on patient position (HFS, HFP, FFS, FFP).
"""

import pytest
import numpy as np
from unittest.mock import Mock, MagicMock
import copy

from pinnacle_io.utils.coordinate_transforms import (
    transform_coordinates_for_curve,
    transform_coordinates_for_roi,
    transform_coordinates_for_rois,
    transform_dose,
    needs_coordinate_transform,
    _get_coordinate_flips,
    _apply_coordinate_flip,
)
from pinnacle_io.utils.patient_enum import PatientSetupEnum


class TestCoordinateFlips:
    """Test the coordinate flip logic for different patient positions."""

    def test_get_coordinate_flips_hfs(self):
        """Test HFS (Head-First Supine) flips Y coordinate."""
        flip_x, flip_y, flip_z = _get_coordinate_flips("HFS")
        assert not flip_x
        assert flip_y
        assert not flip_z

    def test_get_coordinate_flips_hfp(self):
        """Test HFP (Head-First Prone) flips X coordinate."""
        flip_x, flip_y, flip_z = _get_coordinate_flips("HFP")
        assert flip_x
        assert not flip_y
        assert not flip_z

    def test_get_coordinate_flips_ffs(self):
        """Test FFS (Feet-First Supine) doesn't flip any coordinates."""
        flip_x, flip_y, flip_z = _get_coordinate_flips("FFS")
        assert not flip_x
        assert not flip_y
        assert not flip_z

    def test_get_coordinate_flips_ffp(self):
        """Test FFP (Feet-First Prone) flips both X and Y coordinates."""
        flip_x, flip_y, flip_z = _get_coordinate_flips("FFP")
        assert flip_x
        assert flip_y
        assert not flip_z

    def test_get_coordinate_flips_invalid_position(self):
        """Test invalid patient position raises ValueError."""
        with pytest.raises(ValueError, match="Unsupported patient position"):
            _get_coordinate_flips("INVALID")

    def test_get_coordinate_flips_case_insensitive(self):
        """Test that patient position is case insensitive."""
        flip_x, flip_y, flip_z = _get_coordinate_flips("hfs")
        assert not flip_x
        assert flip_y
        assert not flip_z


class TestApplyCoordinateFlip:
    """Test the coordinate flipping application to point arrays."""

    def test_apply_coordinate_flip_no_flips(self):
        """Test no coordinate flips returns original array."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        result = _apply_coordinate_flip(points, False, False, False)
        np.testing.assert_array_equal(result, points)
        # Ensure it's a copy, not the same array
        assert result is not points

    def test_apply_coordinate_flip_x_only(self):
        """Test flipping only X coordinates."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        expected = np.array([[-1.0, 2.0, 3.0], [-4.0, 5.0, 6.0]], dtype=np.float32)
        result = _apply_coordinate_flip(points, True, False, False)
        np.testing.assert_array_equal(result, expected)

    def test_apply_coordinate_flip_y_only(self):
        """Test flipping only Y coordinates."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        expected = np.array([[1.0, -2.0, 3.0], [4.0, -5.0, 6.0]], dtype=np.float32)
        result = _apply_coordinate_flip(points, False, True, False)
        np.testing.assert_array_equal(result, expected)

    def test_apply_coordinate_flip_z_only(self):
        """Test flipping only Z coordinates."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        expected = np.array([[1.0, 2.0, -3.0], [4.0, 5.0, -6.0]], dtype=np.float32)
        result = _apply_coordinate_flip(points, False, False, True)
        np.testing.assert_array_equal(result, expected)

    def test_apply_coordinate_flip_all_axes(self):
        """Test flipping all coordinates."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        expected = np.array([[-1.0, -2.0, -3.0], [-4.0, -5.0, -6.0]], dtype=np.float32)
        result = _apply_coordinate_flip(points, True, True, True)
        np.testing.assert_array_equal(result, expected)

    def test_apply_coordinate_flip_empty_array(self):
        """Test flipping empty array returns empty array."""
        points = np.array([], dtype=np.float32).reshape(0, 3)
        result = _apply_coordinate_flip(points, True, True, True)
        assert result.shape == (0, 3)


class TestNeedsCoordinateTransform:
    """Test the needs_coordinate_transform utility function."""

    def test_needs_transform_hfs(self):
        """Test HFS needs transformation (Y flip)."""
        assert needs_coordinate_transform("HFS") is True

    def test_needs_transform_hfp(self):
        """Test HFP needs transformation (X flip)."""
        assert needs_coordinate_transform("HFP") is True

    def test_needs_transform_ffs(self):
        """Test FFS doesn't need transformation."""
        assert needs_coordinate_transform("FFS") is False

    def test_needs_transform_ffp(self):
        """Test FFP needs transformation (X and Y flip)."""
        assert needs_coordinate_transform("FFP") is True

    def test_needs_transform_invalid_position(self):
        """Test invalid position returns False."""
        assert needs_coordinate_transform("INVALID") is False


class TestCurveTransformation:
    """Test curve coordinate transformation."""

    def create_mock_curve(self, points_data=None):
        """Create a mock Curve object for testing."""
        curve = Mock()
        if points_data is not None:
            curve.points = np.array(points_data, dtype=np.float32)
        else:
            # Match the real Curve model behavior: return empty array for no points
            curve.points = np.zeros((0, 3), dtype=np.float32)

        # Mock the to_dict method to return a proper dictionary structure
        def mock_to_dict():
            return {
                'points_data': curve.points.tobytes() if curve.points.size > 0 else None,
                'contour_geometric_type': 'CLOSED_PLANAR',
                'flags': 0,
                'block_size': None,
                'num_points': len(curve.points),
                'curve_number': 1,
                'slice_index': None,
                'z_position': None,
                'roi_id': 1,
                # Don't include points in to_dict since it's a computed property
            }

        curve.to_dict = mock_to_dict
        return curve

    def test_transform_curve_hfs(self):
        """Test transforming curve for HFS position (Y flip)."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        curve = self.create_mock_curve(points)

        result = transform_coordinates_for_curve(curve, "HFS")

        expected_points = np.array([[1.0, -2.0, 3.0], [4.0, -5.0, 6.0]], dtype=np.float32)
        np.testing.assert_array_equal(result.points, expected_points)

    def test_transform_curve_ffp(self):
        """Test transforming curve for FFP position (X and Y flip)."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        curve = self.create_mock_curve(points)

        result = transform_coordinates_for_curve(curve, "FFP")

        expected_points = np.array([[-1.0, -2.0, 3.0], [-4.0, -5.0, 6.0]], dtype=np.float32)
        np.testing.assert_array_equal(result.points, expected_points)

    def test_transform_curve_ffs_no_change(self):
        """Test transforming curve for FFS position (no change)."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        curve = self.create_mock_curve(points)

        result = transform_coordinates_for_curve(curve, "FFS")

        np.testing.assert_array_equal(result.points, points)

    def test_transform_curve_empty_points(self):
        """Test transforming curve with no points."""
        curve = self.create_mock_curve()

        result = transform_coordinates_for_curve(curve, "HFS")

        # The Curve model returns an empty array, not None, for empty points
        assert result.points is not None
        assert result.points.size == 0
        assert result.points.shape == (0, 3)

    def test_transform_curve_preserves_original(self):
        """Test that transformation doesn't modify original curve."""
        points = np.array([[1.0, 2.0, 3.0]], dtype=np.float32)
        curve = self.create_mock_curve(points)
        original_points = curve.points.copy()

        transform_coordinates_for_curve(curve, "HFS")

        np.testing.assert_array_equal(curve.points, original_points)


class TestROITransformation:
    """Test ROI coordinate transformation."""

    def create_mock_roi(self, curves=None):
        """Create a mock ROI object for testing."""
        roi = Mock()
        roi.curve_list = curves or []
        return roi

    def create_mock_curve(self, points_data):
        """Create a mock Curve object for testing."""
        curve = Mock()
        curve.points = np.array(points_data, dtype=np.float32)
        return curve

    def test_transform_roi_single_curve(self):
        """Test transforming ROI with single curve."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        curve = self.create_mock_curve(points)
        roi = self.create_mock_roi([curve])

        result = transform_coordinates_for_roi(roi, "HFS")

        assert len(result.curve_list) == 1
        expected_points = np.array([[1.0, -2.0, 3.0], [4.0, -5.0, 6.0]], dtype=np.float32)
        np.testing.assert_array_equal(result.curve_list[0].points, expected_points)

    def test_transform_roi_multiple_curves(self):
        """Test transforming ROI with multiple curves."""
        points1 = np.array([[1.0, 2.0, 3.0]], dtype=np.float32)
        points2 = np.array([[4.0, 5.0, 6.0]], dtype=np.float32)
        curve1 = self.create_mock_curve(points1)
        curve2 = self.create_mock_curve(points2)
        roi = self.create_mock_roi([curve1, curve2])

        result = transform_coordinates_for_roi(roi, "HFP")

        assert len(result.curve_list) == 2
        expected_points1 = np.array([[-1.0, 2.0, 3.0]], dtype=np.float32)
        expected_points2 = np.array([[-4.0, 5.0, 6.0]], dtype=np.float32)
        np.testing.assert_array_equal(result.curve_list[0].points, expected_points1)
        np.testing.assert_array_equal(result.curve_list[1].points, expected_points2)

    def test_transform_roi_empty_curves(self):
        """Test transforming ROI with no curves."""
        roi = self.create_mock_roi([])

        result = transform_coordinates_for_roi(roi, "HFS")

        assert result.curve_list == []

    def test_transform_rois_list(self):
        """Test transforming list of ROIs."""
        points = np.array([[1.0, 2.0, 3.0]], dtype=np.float32)
        curve = self.create_mock_curve(points)
        roi1 = self.create_mock_roi([curve])
        roi2 = self.create_mock_roi([curve])

        result = transform_coordinates_for_rois([roi1, roi2], "HFS")

        assert len(result) == 2
        for roi in result:
            expected_points = np.array([[1.0, -2.0, 3.0]], dtype=np.float32)
            np.testing.assert_array_equal(roi.curve_list[0].points, expected_points)


class TestDoseTransformation:
    """Test dose coordinate transformation."""

    def create_mock_dose_grid(self, origin_x=10.0, origin_y=20.0, origin_z=30.0,
                             voxel_x=2.0, voxel_y=2.0, voxel_z=2.5,
                             dim_x=50, dim_y=50, dim_z=40):
        """Create a mock DoseGrid object for testing."""
        dose_grid = Mock()

        # Mock origin
        origin = Mock()
        origin.x = origin_x
        origin.y = origin_y
        origin.z = origin_z
        dose_grid.origin = origin

        # Mock voxel size
        voxel_size = Mock()
        voxel_size.x = voxel_x
        voxel_size.y = voxel_y
        voxel_size.z = voxel_z
        dose_grid.voxel_size = voxel_size

        # Mock dimension
        dimension = Mock()
        dimension.x = dim_x
        dimension.y = dim_y
        dimension.z = dim_z
        dose_grid.dimension = dimension

        # Mock the to_dict method to return a proper dictionary structure
        def mock_to_dict():
            return {
                'voxel_size_x': voxel_x,
                'voxel_size_y': voxel_y,
                'voxel_size_z': voxel_z,
                'dimension_x': dim_x,
                'dimension_y': dim_y,
                'dimension_z': dim_z,
                'origin_x': origin_x,
                'origin_y': origin_y,
                'origin_z': origin_z,
                'vol_rot_delta_x': None,
                'vol_rot_delta_y': None,
                'vol_rot_delta_z': None,
                'display_2d': None,
                'dose_summation_type': None,
                'trial_id': 1,
            }

        dose_grid.to_dict = mock_to_dict
        return dose_grid

    def create_mock_dose(self, dose_grid=None):
        """Create a mock Dose object for testing."""
        dose = Mock()
        dose.dose_grid = dose_grid or self.create_mock_dose_grid()
        dose.pixel_data = np.random.random((50, 50, 40))  # Add mock pixel data
        dose._image_position_patient = [0, 0, 0]  # Add mock image position

        # Add to_dict method that returns a proper dictionary structure
        def mock_to_dict():
            return {
                'dose_id': None,
                'dose_type': 'PHYSICAL',
                'dose_unit': 'GY',
                'datatype': 1,
                'bitpix': 32,
                'bytes_pix': 4,
                'vol_max': 0.0,
                'vol_min': 0.0,
                'dose_comment': '',
                'dose_grid_scaling': 1.0,
                'dose_summation_type': 'PLAN',
                'referenced_plan_id': None,
                '_referenced_beam_numbers': None,
                'dose_grid_id': 1,
                'beam_id': None,
                'trial_id': None,
                # Return the dose_grid's dictionary for the relationship
                'dose_grid': dose.dose_grid.to_dict(),
                # Don't include pixel_data or _image_position_patient in to_dict
                # as these are typically handled separately
            }

        dose.to_dict = mock_to_dict
        return dose

    def test_transform_dose_hfs(self):
        """Test dose transformation for HFS position."""
        dose_grid = self.create_mock_dose_grid(origin_x=10.0, origin_y=20.0, origin_z=30.0)
        dose = self.create_mock_dose(dose_grid)

        result = transform_dose(dose, "HFS")

        # HFS: Calculate expected values with dose shift
        y_dose_shift = 2.0 * (50 - 1)  # voxel_size.y * (dimension.y - 1) = 98.0
        expected_origin_y = -20.0 - y_dose_shift  # -20.0 - 98.0 = -118.0

        assert result.dose_grid.origin.x == 10.0  # X unchanged
        assert result.dose_grid.origin.y == expected_origin_y  # Y flipped and shifted
        assert result.dose_grid.origin.z == 30.0  # Z unchanged

    def test_transform_dose_hfp(self):
        """Test dose transformation for HFP position."""
        dose_grid = self.create_mock_dose_grid(origin_x=10.0, origin_y=20.0, origin_z=30.0)
        dose = self.create_mock_dose(dose_grid)

        result = transform_dose(dose, "HFP")

        # HFP: Calculate expected values with dose shift
        y_dose_shift = 2.0 * (50 - 1)  # voxel_size.y * (dimension.y - 1) = 98.0
        expected_origin_y = 20.0 + y_dose_shift  # 20.0 + 98.0 = 118.0

        assert result.dose_grid.origin.x == 10.0  # X unchanged
        assert result.dose_grid.origin.y == expected_origin_y  # Y shifted (positive direction)
        assert result.dose_grid.origin.z == 30.0  # Z unchanged

    def test_transform_dose_ffs(self):
        """Test dose transformation for FFS position."""
        dose_grid = self.create_mock_dose_grid(origin_x=10.0, origin_y=20.0, origin_z=30.0)
        dose = self.create_mock_dose(dose_grid)

        result = transform_dose(dose, "FFS")

        # FFS: Calculate expected values with dose shift
        y_dose_shift = 2.0 * (50 - 1)  # voxel_size.y * (dimension.y - 1) = 98.0
        expected_origin_y = -20.0 - y_dose_shift  # -20.0 - 98.0 = -118.0

        assert result.dose_grid.origin.x == 10.0  # X unchanged
        assert result.dose_grid.origin.y == expected_origin_y  # Y flipped and shifted
        assert result.dose_grid.origin.z == 30.0  # Z unchanged

    def test_transform_dose_ffp(self):
        """Test dose transformation for FFP position."""
        dose_grid = self.create_mock_dose_grid(origin_x=10.0, origin_y=20.0, origin_z=30.0)
        dose = self.create_mock_dose(dose_grid)

        result = transform_dose(dose, "FFP")

        # FFP: Calculate expected values with dose shift
        y_dose_shift = 2.0 * (50 - 1)  # voxel_size.y * (dimension.y - 1) = 98.0
        expected_origin_y = 20.0 + y_dose_shift  # 20.0 + 98.0 = 118.0

        assert result.dose_grid.origin.x == 10.0  # X unchanged
        assert result.dose_grid.origin.y == expected_origin_y  # Y shifted (positive direction)
        assert result.dose_grid.origin.z == 30.0  # Z unchanged

    def test_transform_dose_invalid_position(self):
        """Test dose transformation with invalid patient position."""
        dose = self.create_mock_dose()

        with pytest.raises(ValueError, match="Unsupported patient position"):
            transform_dose(dose, "INVALID")

    def test_transform_dose_missing_dose_grid(self):
        """Test dose transformation with missing dose grid."""
        dose = Mock()
        dose.dose_grid = None

        with pytest.raises(ValueError, match="Dose object must have a dose_grid"):
            transform_dose(dose, "HFS")

    def test_transform_dose_incomplete_dose_grid(self):
        """Test dose transformation with incomplete dose grid data."""
        dose_grid = Mock()
        dose_grid.origin = None

        # Mock the to_dict method for the incomplete dose grid
        def incomplete_to_dict():
            return {
                'voxel_size_x': None,
                'voxel_size_y': None,
                'voxel_size_z': None,
                'dimension_x': None,
                'dimension_y': None,
                'dimension_z': None,
                'origin_x': None,
                'origin_y': None,
                'origin_z': None,
                'vol_rot_delta_x': None,
                'vol_rot_delta_y': None,
                'vol_rot_delta_z': None,
                'display_2d': None,
                'dose_summation_type': None,
                'trial_id': 1,
            }
        dose_grid.to_dict = incomplete_to_dict

        dose = self.create_mock_dose(dose_grid)

        # The actual error occurs when trying to access voxel_size.y when voxel_size is None
        with pytest.raises(AttributeError, match="'NoneType' object has no attribute 'y'"):
            transform_dose(dose, "HFS")

    def test_transform_dose_preserves_original(self):
        """Test that dose transformation doesn't modify original dose."""
        dose_grid = self.create_mock_dose_grid(origin_x=10.0, origin_y=20.0, origin_z=30.0)
        dose = self.create_mock_dose(dose_grid)
        original_x = dose.dose_grid.origin.x

        transform_dose(dose, "HFS")

        # Original should be unchanged
        assert dose.dose_grid.origin.x == original_x

    def test_transform_dose_image_position_calculation(self):
        """Test ImagePositionPatient calculation for all positions."""
        dose_grid = self.create_mock_dose_grid(
            origin_x=10.0, origin_y=20.0, origin_z=30.0,
            voxel_x=2.0, voxel_y=3.0, voxel_z=2.5,
            dim_x=50, dim_y=60, dim_z=40
        )
        dose = self.create_mock_dose(dose_grid)

        # Calculate expected dose shift (only Y is transformed with shift in this implementation)
        y_dose_shift = 3.0 * (60 - 1)  # 177.0

        # Test HFS - Y flipped and shifted
        result_hfs = transform_dose(dose, "HFS")
        expected_origin_y_hfs = -20.0 - y_dose_shift  # -197.0
        assert result_hfs.dose_grid.origin.x == 10.0
        assert result_hfs.dose_grid.origin.y == expected_origin_y_hfs
        assert result_hfs.dose_grid.origin.z == 30.0

        # Test HFP - Y shifted (positive direction)
        result_hfp = transform_dose(dose, "HFP")
        expected_origin_y_hfp = 20.0 + y_dose_shift  # 197.0
        assert result_hfp.dose_grid.origin.x == 10.0
        assert result_hfp.dose_grid.origin.y == expected_origin_y_hfp
        assert result_hfp.dose_grid.origin.z == 30.0

        # Test FFS - Y flipped and shifted
        result_ffs = transform_dose(dose, "FFS")
        expected_origin_y_ffs = -20.0 - y_dose_shift  # -197.0
        assert result_ffs.dose_grid.origin.x == 10.0
        assert result_ffs.dose_grid.origin.y == expected_origin_y_ffs
        assert result_ffs.dose_grid.origin.z == 30.0

        # Test FFP - Y shifted (positive direction)
        result_ffp = transform_dose(dose, "FFP")
        expected_origin_y_ffp = 20.0 + y_dose_shift  # 197.0
        assert result_ffp.dose_grid.origin.x == 10.0
        assert result_ffp.dose_grid.origin.y == expected_origin_y_ffp
        assert result_ffp.dose_grid.origin.z == 30.0


class TestIntegrationWithTestData:
    """Integration tests using real test data from tests/test_data/01/."""

    def test_coordinate_transforms_with_real_data(self):
        """Test coordinate transformations with real Pinnacle data."""
        # This test would load actual data from the test_data directory
        # and verify transformations work with real data structures

        # Note: This test is a placeholder for actual integration testing
        # with the test data. Implementation would require:
        # 1. Loading real ROI and dose data from test_data/01/
        # 2. Applying transformations
        # 3. Verifying results match expected behavior

        pytest.skip("Integration test placeholder - requires real data loading implementation")

    def test_patient_position_detection_with_real_data(self):
        """Test patient position detection with real ImageSet data."""
        # This would test reading patient_position from real ImageSet files
        # and verifying the coordinate transforms work correctly

        pytest.skip("Integration test placeholder - requires real data loading implementation")


if __name__ == "__main__":
    pytest.main([__file__])