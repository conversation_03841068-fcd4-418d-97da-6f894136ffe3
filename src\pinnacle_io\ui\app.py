"""
Main entry point for the Pinnacle IO UI application.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the Python path so we can import pinnacle_io
current_dir = Path(__file__).parent
root_dir = current_dir.parent.parent
sys.path.insert(0, str(root_dir))

try:
    # Import the main window
    from .main_window import MainWindow

    def main():
        """Main entry point for the application."""
        # Check if a data path was provided as command line argument
        data_path = None
        export_path = None
        if len(sys.argv) > 1:
            data_path = sys.argv[1]
        
        if len(sys.argv) > 2:
            export_path = sys.argv[2]
            if export_path.endswith("/tests/test_data/01/Dicom"):
                for file in os.listdir(export_path):
                    if file.endswith(".dcm"):
                        os.remove(os.path.join(export_path, file))
        
        try:
            # Create and run the application
            app = MainWindow(data_path, export_path)
            app.run()
        except Exception as e:
            print(f"Error starting application: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

    if __name__ == "__main__":
        main()

except ImportError as e:
    print("Error: Required dependencies are missing.")
    print("Please install the required packages:")
    print("  pip install ttkbootstrap pillow numpy")
    print(f"Import error: {e}")
    sys.exit(1)