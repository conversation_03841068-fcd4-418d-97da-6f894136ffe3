"""
Base overlay panel for standardized overlay rendering interface.
"""

import tkinter as tk
import ttkbootstrap as ttk
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple


class BaseOverlayPanel(ttk.Frame, ABC):
    """Abstract base class for panels that provide overlay rendering capabilities."""

    def __init__(self, parent, ct_viewer=None):
        """
        Initialize the base overlay panel.

        Args:
            parent: Parent widget
            ct_viewer: Reference to CT viewer for coordinate system access
        """
        super().__init__(parent)
        self.ct_viewer = ct_viewer

        # Common overlay state
        self.visibility_state: Dict[Any, bool] = {}
        self.color_state: Dict[Any, str] = {}
        self.data_objects: List[Any] = []
        self.transformed_data: List[Any] = []

        # Display parameters cache
        self.last_display_params: Optional[Dict[str, Any]] = None

        # Performance optimization: refresh throttling
        self._pending_refresh = False
        self._refresh_after_id: Optional[str] = None

        self._setup_ui()

    @abstractmethod
    def _setup_ui(self):
        """Set up the panel UI components."""
        pass

    @abstractmethod
    def load_data(self, data: List[Any], **kwargs):
        """
        Load domain-specific data.

        Args:
            data: List of domain objects (ROIs, POIs, beams, etc.)
            **kwargs: Additional parameters like patient_position
        """
        pass

    @abstractmethod
    def render_overlays(self, canvas: tk.Canvas, current_slice_z: float, display_params: Dict[str, Any]):
        """
        Render overlays on the CT viewer canvas.

        Args:
            canvas: Canvas to draw on
            current_slice_z: Current slice Z-coordinate
            display_params: Display parameters (scale, center, etc.)
        """
        pass

    @abstractmethod
    def update_visibility(self, item_id: Any, is_visible: bool):
        """
        Update visibility of specific overlay item.

        Args:
            item_id: Identifier for the overlay item
            is_visible: Whether item should be visible
        """
        pass

    @abstractmethod
    def get_visible_items(self) -> List[Any]:
        """
        Get list of currently visible items.

        Returns:
            List of visible domain objects
        """
        pass

    @abstractmethod
    def apply_coordinate_transforms(self, patient_position: Optional[str]):
        """
        Apply coordinate transformations for patient position.

        Args:
            patient_position: Patient position for coordinate transformation
        """
        pass

    def register_ct_viewer(self, ct_viewer):
        """Register CT viewer for coordinate system access."""
        self.ct_viewer = ct_viewer

    def request_refresh(self, immediate: bool = False):
        """Request CT viewer to refresh display with throttling optimization.

        Args:
            immediate: If True, bypass throttling and refresh immediately
        """
        if not self.ct_viewer:
            return

        if immediate:
            # Cancel any pending refresh and do immediate refresh
            if self._refresh_after_id:
                self.after_cancel(self._refresh_after_id)
                self._refresh_after_id = None
                self._pending_refresh = False
            self.ct_viewer.refresh_overlays()
            return

        # Throttle refresh requests to avoid excessive redraws
        if self._pending_refresh:
            return  # Already have a refresh scheduled

        self._pending_refresh = True
        self._refresh_after_id = self.after_idle(self._do_refresh)

    def get_display_params_from_ct_viewer(self) -> Optional[Dict[str, Any]]:
        """
        Get current display parameters from CT viewer.

        Note: This method is maintained for compatibility, but overlay panels
        should prefer using display_params passed to render_overlays() method.
        """
        if not self.ct_viewer:
            return None

        try:
            # Use the CT viewer's internal method to get display parameters
            # This ensures consistency with the CT viewer's scaling logic
            return self.ct_viewer._get_display_params()
        except Exception as e:
            print(f"Error getting display parameters: {e}")
            return None

    def _cache_display_params(self, display_params: Dict[str, Any]) -> bool:
        """Cache display parameters if they have changed.

        Args:
            display_params: Current display parameters

        Returns:
            True if parameters changed, False if unchanged
        """
        if self.last_display_params != display_params:
            self.last_display_params = display_params.copy() if display_params else None
            return True
        return False

    def world_to_display_coordinates(self, world_x: float, world_y: float, world_z: float,
                                   display_params: Dict[str, Any]) -> Optional[Tuple[float, float]]:
        """
        Convert world coordinates to display coordinates for current orientation.

        Args:
            world_x: World X coordinate
            world_y: World Y coordinate
            world_z: World Z coordinate (used for sagittal and coronal views)
            display_params: Display parameters from CT viewer

        Returns:
            Tuple of display coordinates or None if conversion fails
        """
        try:
            # Get current orientation from CT viewer
            orientation = getattr(self.ct_viewer, 'view_orientation', 'axial')

            # Get orientation-specific voxel spacing (same as CT viewer)
            if orientation == "axial":
                # Axial: X and Y dimensions
                voxel_width = display_params['pixel_spacing'][0]  # X spacing
                voxel_height = display_params['pixel_spacing'][1]  # Y spacing
                # Convert world coordinates to pixel coordinates
                pixel_x = (world_x - display_params['image_origin'][0]) / display_params['pixel_spacing'][0]
                pixel_y = (world_y - display_params['image_origin'][1]) / display_params['pixel_spacing'][1]
            elif orientation == "sagittal":
                # Sagittal: Y and Z dimensions (Y=width, Z=height)
                voxel_width = display_params['pixel_spacing'][1]  # Y spacing
                voxel_height = display_params['pixel_spacing'][2]  # Z spacing
                # Map Y to display X, Z to display Y
                if world_z is None:
                    return None
                pixel_x = (world_y - display_params['image_origin'][1]) / display_params['pixel_spacing'][1]
                pixel_y = (world_z - display_params['image_origin'][2]) / display_params['pixel_spacing'][2]
            elif orientation == "coronal":
                # Coronal: X and Z dimensions
                voxel_width = display_params['pixel_spacing'][0]  # X spacing
                voxel_height = display_params['pixel_spacing'][2]  # Z spacing
                # Map X to display X, Z to display Y
                if world_z is None:
                    return None
                pixel_x = (world_x - display_params['image_origin'][0]) / display_params['pixel_spacing'][0]
                pixel_y = (world_z - display_params['image_origin'][2]) / display_params['pixel_spacing'][2]
            else:
                return None

            # Convert pixel coordinates to physical coordinates (same as CT viewer)
            physical_x = pixel_x * voxel_width
            physical_y = pixel_y * voxel_height

            # Calculate physical dimensions for the current orientation
            physical_width = display_params['original_width'] * voxel_width
            physical_height = display_params['original_height'] * voxel_height

            # Convert to display coordinates using physical-based scaling (same as CT viewer)
            # The display_params['scale'] is based on physical dimensions
            display_x = display_params['center_x'] + (physical_x - physical_width / 2) * display_params['scale']
            display_y = display_params['center_y'] + (physical_y - physical_height / 2) * display_params['scale']

            return (display_x, display_y)

        except Exception as e:
            print(f"Error converting coordinates: {e}")
            return None

    def clear_overlays(self, canvas: tk.Canvas, tag: str):
        """
        Clear overlay items with specific tag from canvas.

        Args:
            canvas: Canvas to clear from
            tag: Tag to identify overlay items
        """
        canvas.delete(tag)

    def _create_color_indicator(self, parent_frame: ttk.Frame, color: str):
        """Create a small color indicator widget."""
        try:
            color_frame = tk.Frame(
                parent_frame,
                width=12,
                height=12,
                bg=color,
                relief="solid",
                borderwidth=1
            )
            color_frame.pack(side=tk.RIGHT, padx=(5, 0))
            color_frame.pack_propagate(False)
        except Exception:
            pass  # Skip color indicator if creation fails

    def show_all_items(self):
        """Show all items managed by this panel."""
        for item_id in self.visibility_state:
            self.visibility_state[item_id] = True
        self.request_refresh()

    def _do_refresh(self):
        """Internal method to perform actual refresh."""
        self._pending_refresh = False
        self._refresh_after_id = None
        if self.ct_viewer:
            self.ct_viewer.refresh_overlays()

    def hide_all_items(self):
        """Hide all items managed by this panel."""
        for item_id in self.visibility_state:
            self.visibility_state[item_id] = False
        self.request_refresh()