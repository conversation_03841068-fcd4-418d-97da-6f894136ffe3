"""
Centralized progress management for multi-stage operations.

This module provides a progress management system that coordinates progress tracking
across multiple UI components and data loading stages in the Pinnacle IO application.
"""

import threading
import tkinter as tk
from datetime import datetime
from typing import Optional, Dict, Any, List, Callable
from pinnacle_io.ui.widgets.progress_dialog import ProgressDialog


class ProgressState:
    """Global state tracking for progress operations."""

    def __init__(self):
        self.active_operations: Dict[str, 'ProgressManager'] = {}
        self.operation_hierarchy: Dict[str, List[str]] = {}  # parent_operation -> child_operations
        self._lock = threading.Lock()

    def is_progress_active(self, operation_id: Optional[str] = None) -> bool:
        """
        Check if any progress dialog is currently active.

        Args:
            operation_id: Specific operation to check, or None for any active operation

        Returns:
            True if specified operation (or any operation) is active
        """
        with self._lock:
            if operation_id:
                return operation_id in self.active_operations
            return len(self.active_operations) > 0

    def get_active_progress_manager(self, operation_id: Optional[str] = None) -> Optional['ProgressManager']:
        """
        Get active progress manager for operation or any active one.

        Args:
            operation_id: Specific operation to get, or None for any active one

        Returns:
            ProgressManager instance or None if not found
        """
        with self._lock:
            if operation_id and operation_id in self.active_operations:
                return self.active_operations[operation_id]
            elif self.active_operations:
                return next(iter(self.active_operations.values()))
            return None

    def register_operation(self, operation_id: str, progress_manager: 'ProgressManager') -> None:
        """Register a new operation with the global state."""
        with self._lock:
            self.active_operations[operation_id] = progress_manager

    def unregister_operation(self, operation_id: str) -> None:
        """Remove an operation from the global state."""
        with self._lock:
            if operation_id in self.active_operations:
                del self.active_operations[operation_id]
            if operation_id in self.operation_hierarchy:
                del self.operation_hierarchy[operation_id]


# Global progress state instance
_global_progress_state = ProgressState()


class ProgressManager:
    """Centralized progress management for multi-stage operations."""

    _active_instance: Optional['ProgressManager'] = None

    @classmethod
    def get_or_create_instance(cls, parent, operation_id: str, **kwargs) -> 'ProgressManager':
        """
        Get existing instance or create new one for operation.

        Args:
            parent: Parent window for the progress dialog
            operation_id: Unique identifier for this operation
            **kwargs: Additional arguments for ProgressManager initialization

        Returns:
            ProgressManager instance
        """
        existing = _global_progress_state.get_active_progress_manager(operation_id)

        if existing and existing.operation_id == operation_id:
            # Check if existing manager is truly usable
            if existing.dialog and existing.dialog.is_active and existing.is_active_flag:
                return existing
            else:
                # Existing manager has destroyed dialog, will be reused but dialog recreated
                return existing
        else:
            # Create new instance
            cls._active_instance = cls(parent, operation_id, **kwargs)
            return cls._active_instance

    def __init__(self, parent, operation_id: str, title: str = "Processing...",
                 total_stages: int = 3, cancelable: bool = True, development_mode: Optional[bool] = None,
                 auto_close: Optional[bool] = None):
        """
        Initialize the progress manager.

        Args:
            parent: Parent window for the progress dialog
            operation_id: Unique identifier for this operation
            title: Dialog title
            total_stages: Expected number of stages in the operation
            cancelable: Whether operation can be cancelled
            development_mode: Enable development features
            auto_close: Whether to auto-close on completion (None = auto-detect from dev mode)
        """
        self.parent = parent
        self.operation_id = operation_id
        self.dialog = ProgressDialog(parent, title, cancelable, development_mode)
        self.total_stages = total_stages

        # Stage management
        self.stage_registry: Dict[str, Dict[str, Any]] = {}
        self.current_stage: Optional[str] = None
        self.current_stage_progress = 0
        self.overall_progress = 0

        # Default stage weights for typical Pinnacle IO workflow
        self.default_stage_weights = [40, 35, 25]  # MainWindow, RightSidebar, Panels
        self.stage_weights = self.default_stage_weights[:total_stages]

        # Operation state
        self.is_active_flag = False
        self.operation_completed = False
        self.operation_phase = "running"  # running, completed, cancelled, failed

        # Auto-close behavior
        if auto_close is None:
            self.auto_close = not self.dialog.development_mode
        else:
            self.auto_close = auto_close

        # Button state management
        self.button_states = {
            'cancel': cancelable,
            'close': False,
            'dev_controls': False
        }

        # Register with global state
        _global_progress_state.register_operation(operation_id, self)

    def is_active(self) -> bool:
        """Check if this progress manager is currently active."""
        return self.is_active_flag and self.dialog and self.dialog.is_active

    def show(self, status: str = "Starting...", max_progress: int = 100) -> None:
        """
        Show the progress dialog.

        Args:
            status: Initial status message
            max_progress: Maximum progress value
        """
        # Only return early if truly active (dialog exists and is shown)
        if self.is_active_flag and self.dialog and self.dialog.is_active:
            return

        # Recreate dialog if it was destroyed or doesn't exist
        if not self.dialog or not hasattr(self.dialog, 'dialog') or self.dialog.dialog is None:
            # Reset operation state for new dialog
            self.operation_completed = False
            self.operation_phase = "running"

            # Create new dialog with same parameters
            from pinnacle_io.ui.widgets.progress_dialog import ProgressDialog
            title = getattr(self.dialog, 'title', 'Processing...') if self.dialog else 'Processing...'
            cancelable = getattr(self.dialog, 'cancelable', True) if self.dialog else True
            development_mode = getattr(self.dialog, 'development_mode', None) if self.dialog else None
            auto_close = getattr(self.dialog, 'auto_close', None) if self.dialog else None

            self.dialog = ProgressDialog(self.parent, title, cancelable, development_mode, auto_close)

        # Reset state for new operation
        self.is_active_flag = True
        self.operation_phase = "running"
        self.operation_completed = False

        # Re-register with global state if needed
        if self.operation_id not in _global_progress_state.active_operations:
            _global_progress_state.register_operation(self.operation_id, self)

        self.dialog.show(status, max_progress)
        self._update_button_states()

    def hide(self) -> None:
        """Hide and destroy the progress dialog."""
        if not self.is_active_flag:
            return

        self.is_active_flag = False
        self.dialog.hide()
        _global_progress_state.unregister_operation(self.operation_id)

    def register_stage(self, stage_id: str, weight: int = None, name: str = None) -> None:
        """
        Register a new stage in the operation.

        Args:
            stage_id: Unique identifier for the stage
            weight: Relative weight of this stage (default: equal distribution)
            name: Human-readable name for the stage
        """
        if weight is None:
            # Equal distribution among registered stages
            weight = 100 // max(1, len(self.stage_registry) + 1)

        self.stage_registry[stage_id] = {
            'weight': weight,
            'name': name or stage_id,
            'completed': False,
            'progress': 0
        }

        # Recalculate weights to ensure they sum to 100
        self._recalculate_stage_weights()

    def set_current_stage(self, stage_id: str) -> None:
        """Set the currently active stage."""
        if stage_id not in self.stage_registry:
            self.register_stage(stage_id)

        self.current_stage = stage_id
        self.current_stage_progress = 0

    def update_progress(self, progress: int, status: str = "", context: Optional[str] = None) -> bool:
        """
        Update progress with automatic context detection.

        Args:
            progress: Progress value (0-100 for stage, or absolute based on context)
            status: Status message
            context: Optional context override

        Returns:
            False if operation was cancelled, True otherwise
        """
        if not self.is_active_flag:
            return False

        # Determine if this is a sub-operation or main operation
        if self.current_stage and context != "absolute":
            # We're in a registered stage - update stage progress
            return self._update_stage_progress(progress, status)
        else:
            # Direct progress update
            return self.dialog.update_progress(progress, status)

    def update_status(self, status: str) -> bool:
        """
        Update status without changing progress.

        Args:
            status: Status message

        Returns:
            False if operation was cancelled, True otherwise
        """
        if not self.is_active_flag:
            return False

        return self.dialog.update_status(status)

    def complete_stage(self, stage_id: Optional[str] = None, elapsed_time: Optional[float] = None) -> None:
        """Mark a stage as completed."""
        stage_to_complete = stage_id or self.current_stage
        if stage_to_complete and stage_to_complete in self.stage_registry:
            self.stage_registry[stage_to_complete]['completed'] = True
            self.stage_registry[stage_to_complete]['progress'] = 100

            # Create stage completion message
            completed_stages = sum(1 for stage in self.stage_registry.values() if stage['completed'])
            total_stages = len(self.stage_registry)
            total_progress = self._calculate_total_progress()

            if elapsed_time is not None:
                stage_message = f"Stage {completed_stages} of {total_stages} completed in {elapsed_time:.2f} seconds ({int(total_progress)}/100)"
            else:
                stage_message = f"Stage {completed_stages} of {total_stages} completed ({int(total_progress)}/100)"

            # Update progress with the stage completion message
            self.dialog.update_status(stage_message)

    def complete_operation(self, success: bool = True, final_message: str = "Operation completed") -> None:
        """
        Mark operation as completed and update UI accordingly.

        Args:
            success: Whether the operation completed successfully
            final_message: Final status message to display
        """
        self.operation_phase = "completed" if success else "failed"
        self.operation_completed = True

        # Check if dialog is still active before accessing it
        if not self.is_active_flag or not self.dialog or not self.dialog.is_active:
            return

        # Update dialog's operation completed flag
        self.dialog.operation_completed = True

        # Complete the operation in the dialog (this handles timing, final message, and step completion)
        self.dialog.complete_operation(success, final_message)

        # Update button states
        self._update_button_states()

        # Auto-close behavior (only in production mode and on success)
        if self.auto_close and self.operation_phase == "completed":
            self.parent.after(2000, self.hide)  # 2 second delay
        else:
            # In development mode or on failure, stay open for review
            self._prepare_for_manual_close()

    def is_cancelled_flag(self) -> bool:
        """Check if cancellation was requested."""
        return self.dialog.is_cancelled_flag()

    def set_cancel_callback(self, callback: Callable) -> None:
        """Set callback function to call when cancel is requested."""
        self.dialog.set_cancel_callback(callback)

    def finish_current_step(self) -> None:
        """
        Finish/flush the current progress step to generate accurate timing
        without updating status or progress.

        This method logs the completion time for the current step and can be
        called to ensure accurate performance benchmarking.
        """
        if self.is_active_flag and self.dialog:
            self.dialog.finish_current_step()

    def get_timing_report(self) -> str:
        """Get formatted timing report."""
        return self.dialog.get_timing_report()

    def _update_stage_progress(self, stage_progress: int, status: str) -> bool:
        """Update progress within the current stage."""
        if not self.current_stage:
            return False

        self.current_stage_progress = stage_progress
        self.stage_registry[self.current_stage]['progress'] = stage_progress

        # Calculate overall progress based on stage weights
        total_progress = self._calculate_total_progress()

        # Update timing logs with stage context using stage name instead of ID
        stage_name = self.stage_registry[self.current_stage]['name']
        stage_status = f"{stage_name}: {status}" if status else f"{stage_name}: Progress: {stage_progress}%"

        return self.dialog.update_progress(int(total_progress), stage_status)

    def _calculate_total_progress(self) -> float:
        """Calculate total progress based on stage weights and progress."""
        if not self.stage_registry:
            return 0.0

        total_weighted_progress = 0.0
        total_weight = 0.0

        for stage_id, stage_info in self.stage_registry.items():
            weight = stage_info['weight']
            progress = stage_info['progress']
            total_weighted_progress += (progress / 100.0) * weight
            total_weight += weight

        if total_weight == 0:
            return 0.0

        return (total_weighted_progress / total_weight) * 100


    def _recalculate_stage_weights(self) -> None:
        """Recalculate stage weights to ensure they sum to 100."""
        if not self.stage_registry:
            return

        total_current_weight = sum(stage['weight'] for stage in self.stage_registry.values())

        if total_current_weight != 100 and total_current_weight > 0:
            # Normalize weights to sum to 100
            for stage_info in self.stage_registry.values():
                stage_info['weight'] = int((stage_info['weight'] / total_current_weight) * 100)

    def _update_button_states(self) -> None:
        """Update button states based on operation phase."""
        # Check if dialog is still active before updating
        if not self.is_active_flag or not self.dialog or not self.dialog.is_active:
            return

        # Call the dialog's button state update method
        self.dialog._update_button_states()

        # Additional development mode controls if available
        if self.operation_phase in ["completed", "cancelled", "failed"]:
            if self.dialog.development_mode and hasattr(self.dialog, 'dev_control_buttons'):
                try:
                    for button in getattr(self.dialog, 'dev_control_buttons', []):
                        button.config(state='normal')
                except tk.TclError:
                    # Widgets have been destroyed, ignore
                    pass

    def _prepare_for_manual_close(self) -> None:
        """Prepare dialog for manual close after operation completion."""
        # Check if dialog is still active before accessing it
        if not self.is_active_flag or not self.dialog or not self.dialog.is_active:
            return

        # Call the dialog's preparation method
        self.dialog._prepare_for_manual_close()

        # In development mode, auto-scroll to show timing summary
        if self.dialog.development_mode and hasattr(self.dialog, 'timing_text'):
            try:
                self.dialog.timing_text.see('end')

                # Add separator and summary to timing log
                summary = f"\n{'='*50}\n{self._get_timing_summary()}\n{'='*50}"
                self.dialog.timing_text.config(state='normal')
                self.dialog.timing_text.insert('end', summary)
                self.dialog.timing_text.config(state='disabled')
                self.dialog.timing_text.see('end')
            except Exception:
                pass  # Ignore timing display errors

    def _get_timing_summary(self) -> str:
        """Get condensed timing summary for quick review."""
        if not hasattr(self.dialog, 'timing_log') or not self.dialog.timing_log:
            return "No timing data available"

        total_time = (datetime.now() - self.dialog.start_time).total_seconds() if self.dialog.start_time else 0
        total_steps = len(self.dialog.timing_log)

        # Find slowest steps
        timing_log = getattr(self.dialog, 'timing_log', [])
        slowest_steps = sorted(timing_log, key=lambda x: x.get('elapsed_step', 0), reverse=True)[:3]

        summary_lines = [
            "TIMING SUMMARY:",
            f"Total Time: {total_time:.3f}s",
            f"Total Steps: {total_steps}",
            f"Average Step Time: {total_time/total_steps:.3f}s" if total_steps > 0 else "Average Step Time: N/A",
            "",
            "Slowest Steps:"
        ]

        for i, step in enumerate(slowest_steps, 1):
            step_name = step.get('step_name', 'Unknown')
            elapsed_step = step.get('elapsed_step', 0)
            summary_lines.append(f"  {i}. {step_name}: {elapsed_step:.3f}s")

        return "\n".join(summary_lines)