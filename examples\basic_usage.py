"""
Basic usage examples for pinnacle_io library.

This module demonstrates the fundamental usage patterns for reading
Pinnacle treatment planning data using the PinnacleReader class.
Features both modern ID-based methods and legacy path-based methods.
"""

from pinnacle_io import PinnacleReader
from pinnacle_io.models import Institution, Patient, Trial, ROI, Dose

def basic_reader_usage():
    """Demonstrate basic PinnacleReader usage."""
    
    # Initialize reader with path to Pinnacle data
    # This works with directories, tar files, or zip files
    reader = PinnacleReader("/path/to/pinnacle/data")
    
    # Read institution data (root level)
    institution = reader.get_institution()
    print(f"Institution: {institution.name}")
    print(f"Address: {institution.address}")
    
    # List available patients
    print("\nAvailable patients:")
    for patient_lite in institution.patient_lite_list:
        print(f"  - {patient_lite.patient_path}")
    
    return reader, institution

def read_patient_data(reader: PinnacleReader):
    """Demonstrate reading patient data using ID-based methods."""
    
    # Modern ID-based approach (recommended)
    try:
        patient = reader.get_patient(institution=1, patient=1)
        
        print(f"\nPatient Details (ID-based):")
        print(f"Name: {patient.first_name} {patient.last_name}")
        print(f"MRN: {patient.medical_record_number}")
        print(f"Birth Date: {patient.birth_date}")
        print(f"Sex: {patient.sex}")
        
        return patient
    except Exception as e:
        print(f"Error with ID-based method: {e}")
        
        # Fallback to legacy path-based approach
        institution = reader.get_institution()
        if institution.patient_lite_list:
            patient_path = institution.patient_lite_list[0].patient_path
            patient = reader.get_patient(patient_path)
            
            print(f"\nPatient Details (legacy path-based):")
            print(f"Name: {patient.first_name} {patient.last_name}")
            print(f"MRN: {patient.medical_record_number}")
            print(f"Birth Date: {patient.birth_date}")
            print(f"Sex: {patient.sex}")
            
            return patient
        
        return None

def read_imaging_data(reader: PinnacleReader):
    """Demonstrate reading imaging data using ID-based methods."""
    
    # Modern ID-based approach (recommended)
    try:
        image_set = reader.get_image_set(institution=1, patient=1, image_set=0)
        print(f"\nImage Set (ID-based):")
        print(f"Number of images: {len(image_set.images) if image_set.images else 0}")
        print(f"Image dimensions: {image_set.dimension_x}x{image_set.dimension_y}x{image_set.dimension_z}")
        
        # Read image info using IDs
        image_info_list = reader.get_image_info(institution=1, patient=1, image_set=0)
        print(f"Image info entries: {len(image_info_list)}")
        
        # Demonstrate getting image header only (excludes pixel data)
        image_header = reader.get_image_header(institution=1, patient=1, image_set=0)
        print(f"Image header loaded (no pixel data): {image_header.dimension_x}x{image_header.dimension_y}x{image_header.dimension_z}")
        
        return image_set, image_info_list
    except Exception as e:
        print(f"Error with ID-based imaging methods: {e}")
        
        # Fallback to legacy path-based approach
        try:
            image_set = reader.get_image_set("Institution_1/Mount_0/Patient_1", image_set_id=0)
            print(f"\nImage Set (legacy path-based):")
            print(f"Number of images: {len(image_set.images) if image_set.images else 0}")
            print(f"Image dimensions: {image_set.dimension_x}x{image_set.dimension_y}x{image_set.dimension_z}")
            
            image_info_list = reader.get_image_info("Institution_1/Mount_0/Patient_1", image_set_id=0)
            print(f"Image info entries: {len(image_info_list)}")
            
            return image_set, image_info_list
        except Exception as e:
            print(f"Error reading imaging data: {e}")
            return None, None

def read_plan_data(reader: PinnacleReader):
    """Demonstrate reading treatment plan data using ID-based methods."""
    
    # Modern ID-based approach (recommended)
    try:
        # Read trials using IDs
        trials = reader.get_trials(institution=1, patient=1, plan=1)
        print(f"\nTreatment Trials (ID-based): {len(trials)}")
        for trial in trials:
            print(f"  - {trial.name}: {trial.prescription_dose} cGy")
        
        # Read ROIs (regions of interest) using IDs
        rois = reader.get_rois(institution=1, patient=1, plan=1)
        print(f"\nROIs: {len(rois)}")
        for roi in rois:
            print(f"  - {roi.name}: {roi.type}")
        
        # Read treatment points using IDs
        points = reader.get_points(institution=1, patient=1, plan=1)
        print(f"\nTreatment Points: {len(points)}")
        
        # Read patient setup using IDs
        patient_setup = reader.get_patient_setup(institution=1, patient=1, plan=1)
        print(f"\nPatient Setup: {patient_setup.couch_angle}° couch angle")
        
        # Read machines using IDs
        machines = reader.get_machines(institution=1, patient=1, plan=1)
        print(f"\nTreatment Machines: {len(machines)}")
        for machine in machines:
            print(f"  - {machine.name}: {machine.energy} MV")
        
        return trials, rois, points, patient_setup, machines
    except Exception as e:
        print(f"Error with ID-based plan methods: {e}")
        
        # Fallback to legacy path-based approach
        plan_path = "Institution_1/Mount_0/Patient_1/Plan_1"
        
        try:
            trials = reader.get_trials(plan_path)
            print(f"\nTreatment Trials (legacy path-based): {len(trials)}")
            for trial in trials:
                print(f"  - {trial.name}: {trial.prescription_dose} cGy")
            
            rois = reader.get_rois(plan_path)
            print(f"\nROIs: {len(rois)}")
            for roi in rois:
                print(f"  - {roi.name}: {roi.type}")
            
            points = reader.get_points(plan_path)
            print(f"\nTreatment Points: {len(points)}")
            
            patient_setup = reader.get_patient_setup(plan_path)
            print(f"\nPatient Setup: {patient_setup.couch_angle}° couch angle")
            
            machines = reader.get_machines(plan_path)
            print(f"\nTreatment Machines: {len(machines)}")
            for machine in machines:
                print(f"  - {machine.name}: {machine.energy} MV")
            
            return trials, rois, points, patient_setup, machines
        except Exception as e:
            print(f"Error reading plan data: {e}")
            return None, None, None, None, None

def read_dose_data(reader: PinnacleReader, trials: list[Trial] = None):
    """Demonstrate reading dose data using ID-based methods."""
    
    # Modern ID-based approach (recommended)
    # Note: Dose reading requires a Trial object, not just a trial ID
    try:
        if not trials:
            # Get trials first if not provided
            trials = reader.get_trials(institution=1, patient=1, plan=1)
        
        if trials:
            # Read dose distribution using IDs and trial object
            dose = reader.get_dose(institution=1, patient=1, plan=1, trial=trials[0])
            print(f"\nDose Distribution (ID-based):")
            print(f"Grid size: {dose.dimension_x}x{dose.dimension_y}x{dose.dimension_z}")
            print(f"Voxel size: {dose.pixel_spacing_x}x{dose.pixel_spacing_y}x{dose.slice_spacing}")
            print(f"Max dose: {dose.max_dose} cGy")
            print(f"Associated trial: {trials[0].name}")
            
            return dose
        else:
            print("No trials found for dose reading")
            return None
            
    except Exception as e:
        print(f"Error with ID-based dose method: {e}")
        
        # Fallback to legacy path-based approach
        plan_path = "Institution_1/Mount_0/Patient_1/Plan_1"
        
        try:
            if not trials:
                trials = reader.get_trials(plan_path)
            
            if trials:
                dose = reader.get_dose(plan_path, trial=trials[0])
                print(f"\nDose Distribution (legacy path-based):")
                print(f"Grid size: {dose.dimension_x}x{dose.dimension_y}x{dose.dimension_z}")
                print(f"Voxel size: {dose.pixel_spacing_x}x{dose.pixel_spacing_y}x{dose.slice_spacing}")
                print(f"Max dose: {dose.max_dose} cGy")
                
                return dose
            else:
                print("No trials found for dose reading")
                return None
                
        except Exception as e:
            print(f"Error reading dose data: {e}")
            return None

def demonstrate_id_vs_path_methods(reader: PinnacleReader):
    """Compare ID-based vs path-based method usage."""
    
    print("\n" + "=" * 50)
    print("ID-Based vs Path-Based Method Comparison")
    print("=" * 50)
    
    # ID-based approach (Modern, Recommended)
    print("\n🆕 ID-Based Approach (Recommended):")
    print("   Clean, readable, and follows semantic structure")
    try:
        patient = reader.get_patient(institution=1, patient=1)
        trials = reader.get_trials(institution=1, patient=1, plan=1)
        image_set = reader.get_image_set(institution=1, patient=1, image_set=0)
        
        print(f"   ✓ Patient: {patient.first_name} {patient.last_name}")
        print(f"   ✓ Trials: {len(trials)} found")
        print(f"   ✓ Image Set: {image_set.dimension_x}x{image_set.dimension_y}x{image_set.dimension_z}")
        
    except Exception as e:
        print(f"   ✗ Error: {e}")
    
    # Path-based approach (Legacy, still supported)
    print("\n📁 Path-Based Approach (Legacy):")
    print("   Still supported for backward compatibility")
    try:
        patient = reader.get_patient("Institution_1/Mount_0/Patient_1")
        trials = reader.get_trials("Institution_1/Mount_0/Patient_1/Plan_1")
        image_set = reader.get_image_set("Institution_1/Mount_0/Patient_1", image_set_id=0)
        
        print(f"   ✓ Patient: {patient.first_name} {patient.last_name}")
        print(f"   ✓ Trials: {len(trials)} found")
        print(f"   ✓ Image Set: {image_set.dimension_x}x{image_set.dimension_y}x{image_set.dimension_z}")
        
    except Exception as e:
        print(f"   ✗ Error: {e}")
    
    print("\n💡 Benefits of ID-Based Approach:")
    print("   • More intuitive and readable")
    print("   • No need to construct file paths manually")
    print("   • Consistent with medical record organization")
    print("   • Less error-prone (no path typos)")
    print("   • Works with Institution model objects")

def main():
    """Main example function."""
    
    print("Pinnacle IO Basic Usage Examples")
    print("=" * 40)
    
    try:
        # Basic usage
        reader, institution = basic_reader_usage()
        
        # Read patient data
        patient = read_patient_data(reader)
        
        # Read imaging data
        image_set, image_info = read_imaging_data(reader)
        
        # Read plan data
        trials, rois, points, setup, machines = read_plan_data(reader)
        
        # Read dose data (pass trials from previous step)
        dose = read_dose_data(reader, trials)
        
        # Demonstrate ID vs path method comparison
        demonstrate_id_vs_path_methods(reader)
        
        print("\n" + "=" * 40)
        print("All examples completed successfully!")
        print("💡 Recommendation: Use ID-based methods for new development")
        
    except Exception as e:
        print(f"Error in main execution: {e}")
        print("Make sure you have valid Pinnacle data at the specified path.")

if __name__ == "__main__":
    main()