import pytest
from abc import <PERSON>
from typing import Any, IO
from io import String<PERSON>, BytesIO
from pinnacle_io.services.base_reader_service import BaseReaderService


class MockReaderService(BaseReaderService):
    """Mock implementation of BaseReaderService for testing."""

    def __init__(self, files=None):
        self.files = files or {}

    def open_file(self, filepath: str, filename: str | None = None, mode: str = "r") -> IO[Any]:
        # Handle both old and new calling conventions for backwards compatibility
        if filename is None:
            # New API: filepath is actually the full path
            full_path = filepath
        else:
            # New API: combine filepath and filename
            full_path = f"{filepath}/{filename}" if filepath else filename

        # Normalize path separators
        full_path = full_path.replace("\\", "/")

        if full_path not in self.files:
            raise FileNotFoundError(f"File {full_path} not found")
        content = self.files[full_path]

        if "b" in mode:
            if isinstance(content, str):
                content = content.encode("utf-8")
            return Bytes<PERSON>(content)
        else:
            if isinstance(content, bytes):
                content = content.decode("utf-8")
            return StringIO(content)

    def exists(self, filepath: str, filename: str | None = None) -> bool:
        # Handle both old and new calling conventions for backwards compatibility
        if filename is None:
            # New API: filepath is actually the full path
            full_path = filepath
        else:
            # New API: combine filepath and filename
            full_path = f"{filepath}/{filename}" if filepath else filename

        # Normalize path separators
        full_path = full_path.replace("\\", "/")

        return full_path in self.files


class TestBaseReaderService:
    """Test cases for BaseReaderService abstract base class.

    Tests the updated BaseReaderService interface which requires path parameters
    for all get_* methods and provides unified file service abstraction.
    """

    def test_is_abstract_class(self):
        """Test that BaseReaderService is an abstract class."""
        assert issubclass(BaseReaderService, ABC)

    def test_cannot_instantiate_directly(self):
        """Test that BaseReaderService cannot be instantiated directly."""
        with pytest.raises(TypeError):
            BaseReaderService()

    def test_abstract_methods_exist(self):
        """Test that abstract methods are defined."""
        assert hasattr(BaseReaderService, "open_file")
        assert hasattr(BaseReaderService, "exists")

    def test_concrete_methods_exist(self):
        """Test that concrete get_* methods are defined."""
        methods = [
            "get_institution",
            "get_patient",
            "get_image_set",
            "get_image_info",
            "get_trials",
            "get_rois",
            "get_points",
            "get_patient_setup",
            "get_machines",
            "get_dose",
        ]
        for method in methods:
            assert hasattr(BaseReaderService, method)

    def test_get_institution(self):
        """Test get_institution method with mock data."""
        institution_content = 'Name = "Test Institution";\nAddress = "123 Test St";\n'
        # InstitutionReader looks for a file called "Institution" in the base path ("./Institution")
        service = MockReaderService({"Institution": institution_content})

        # This would normally return an Institution object, but we're testing the file access
        # The actual parsing is tested in the reader tests
        institution = service.get_institution()
        assert institution is not None

    def test_get_patient(self):
        """Test get_patient method with mock data."""
        patient_content = 'Name = "Test Patient";\nMedicalRecordNumber = "12345";\n'
        service = MockReaderService({"Institution_1/Mount_0/Patient_1/Patient": patient_content})

        patient = service.get_patient(institution=1, patient=1)
        assert patient is not None

    def test_get_image_set(self):
        """Test get_image_set method with mock data."""
        image_set_content = "VoxelSize = { x = 1.0; y = 1.0; z = 1.0; };\n"
        image_info_content = 'Modality = "CT";\nSliceThickness = 2.5;\n'
        # ImageSetReader looks for ImageSet_0.header and ImageSet_0.ImageInfo files
        service = MockReaderService(
            {"Institution_1/Mount_0/Patient_1/ImageSet_0.header": image_set_content, 
             "Institution_1/Mount_0/Patient_1/ImageSet_0.ImageInfo": image_info_content}
        )

        image_set = service.get_image_header(institution=1, patient=1, image_set=0)
        assert image_set is not None

    def test_get_image_info(self):
        """Test get_image_info method with mock data."""
        image_info_content = 'Modality = "CT";\nSliceThickness = 2.5;\n'
        service = MockReaderService({"Institution_1/Mount_0/Patient_1/ImageSet_0.ImageInfo": image_info_content})

        image_info = service.get_image_info(institution=1, patient=1, image_set=0)
        assert image_info is not None

    def test_get_image_set_with_institution_object(self):
        """Test get_image_set method with Institution object."""
        from pinnacle_io.models import Institution
        
        image_set_content = "VoxelSize = { x = 1.0; y = 1.0; z = 1.0; };\n"
        image_info_content = 'Modality = "CT";\nSliceThickness = 2.5;\n'
        service = MockReaderService({
            "Institution_1/Mount_0/Patient_1/ImageSet_0.header": image_set_content,
            "Institution_1/Mount_0/Patient_1/ImageSet_0.ImageInfo": image_info_content
        })
        
        # Create Institution object with institution_id
        institution = Institution(institution_id=1)
        
        image_set = service.get_image_header(institution=institution, patient=1, image_set=0)
        assert image_set is not None

    def test_get_image_info_with_institution_object(self):
        """Test get_image_info method with Institution object."""
        from pinnacle_io.models import Institution
        
        image_info_content = 'Modality = "CT";\nSliceThickness = 2.5;\n'
        service = MockReaderService({"Institution_1/Mount_0/Patient_1/ImageSet_0.ImageInfo": image_info_content})
        
        # Create Institution object with institution_id
        institution = Institution(institution_id=1)
        
        image_info = service.get_image_info(institution=institution, patient=1, image_set=0)
        assert image_info is not None

    def test_get_trials(self):
        """Test get_trials method with mock data."""
        trial_content = 'Trial = {\n  Name = "Test Trial";\n};\n'
        service = MockReaderService({"Institution_1/Mount_0/Patient_1/Plan_0/plan.Trial": trial_content})

        trials = service.get_trials(institution=1, patient=1, plan=0)
        assert trials is not None

    def test_get_rois(self):
        """Test get_rois method with mock data."""
        roi_content = 'ROI = {\n  Name = "Test ROI";\n};\n'
        service = MockReaderService({"Institution_1/Mount_0/Patient_1/Plan_0/plan.roi": roi_content})

        rois = service.get_rois(institution=1, patient=1, plan=0)
        assert rois is not None

    def test_get_points(self):
        """Test get_points method with mock data."""
        points_content = 'PoiList = {\n  Point = {\n  Name = "Test Point";\n};\n};\n'
        service = MockReaderService({"Institution_1/Mount_0/Patient_1/Plan_0/plan.Points": points_content})

        points = service.get_points(institution=1, patient=1, plan=0)
        assert points is not None

    def test_get_patient_setup(self):
        """Test get_patient_setup method with mock data."""
        setup_content = 'PatientSetup = {\n  Position = "HFS";\n};\n'
        service = MockReaderService({"Institution_1/Mount_0/Patient_1/Plan_0/plan.PatientSetup": setup_content})

        setup = service.get_patient_setup(institution=1, patient=1, plan=0)
        assert setup is not None

    def test_get_machines(self):
        """Test get_machines method with mock data."""
        machines_content = 'Machine = {\n  Name = "Test Machine";\n};\n'
        # MachineReader looks for plan.Pinnacle.Machines file in the plan path
        service = MockReaderService({"Institution_1/Mount_0/Patient_1/Plan_0/plan.Pinnacle.Machines": machines_content})

        machines = service.get_machines(institution=1, patient=1, plan=0)
        assert machines is not None

    def test_get_dose(self):
        """Test get_dose method with mock data."""
        # Create a test trial first, since DoseReader requires it
        from pinnacle_io.models import (
            Trial,
            DoseGrid,
            Beam,
            Dimension,
            VoxelSize,
            Coordinate,
            MonitorUnitInfo,
            Prescription,
        )

        trial = Trial(trial_id=1, name="Test Trial")
        trial.dose_grid = DoseGrid(
            dimension=Dimension(x=10, y=10, z=5),
            voxel_size=VoxelSize(x=2.0, y=2.0, z=3.0),
            origin=Coordinate(x=-10.0, y=-10.0, z=0.0),
            trial=trial,
        )

        # Create beam and prescription
        beam = Beam(
            beam_number=1,
            name="Test Beam",
            dose_volume="test:1",
            monitor_unit_info=MonitorUnitInfo(monitor_units=100.0, prescription_dose=200.0),
            prescription_name="Test Prescription",
            prescription_point_name="test_point",
            trial=trial,
        )
        trial.beam_list = [beam]

        prescription = Prescription(name="Test Prescription", prescription_dose=200.0, number_of_fractions=10)
        trial.prescription_list = [prescription]

        # Create mock files for dose reading
        points_content = """Poi = {
    Name = "test_point";
    XCoord = 0.0;
    YCoord = 0.0;
    ZCoord = 0.0;
    Radius = 1.0;
    Color = "red";
    CoordSys = "CT";
    Display2d = "Off";
    Display3d = "Off";
    VolumeName = "Test Volume";
    PoiInterpretedType = "MARKER";
    PoiDisplayOnOtherVolumes = 1;
    IsLocked = 0;
};
"""
        # Create mock binary dose data
        import numpy as np

        dose_data = np.ones((10 * 10 * 5), dtype=np.float32) * 1.0

        service = MockReaderService(
            {"Institution_1/Mount_0/Patient_1/Plan_0/plan.Points": points_content, "Institution_1/Mount_0/Patient_1/Plan_0/plan.Trial.binary.001": dose_data.tobytes()}
        )

        dose = service.get_dose(institution=1, patient=1, plan=0, trial=trial)
        assert dose is not None

    def test_file_not_found_error(self):
        """Test that FileNotFoundError is raised when file doesn't exist."""
        service = MockReaderService({})

        with pytest.raises(FileNotFoundError):
            service.get_institution()

    def test_exists_method(self):
        """Test the exists method."""
        service = MockReaderService({"test_file": "content"})

        assert service.exists("test_file") is True
        assert service.exists("nonexistent_file") is False

        # Test with filepath and filename
        service2 = MockReaderService({"dir/test_file": "content"})
        assert service2.exists("dir", "test_file") is True
        assert service2.exists("dir", "nonexistent_file") is False

    def test_open_file_method(self):
        """Test the open_file method."""
        service = MockReaderService({"test_file.txt": "Hello World"})

        # Test text mode
        with service.open_file("test_file.txt") as f:
            content = f.read()
            assert content == "Hello World"

        # Test binary mode
        binary_data = b"\x00\x01\x02\x03"
        service_binary = MockReaderService({"test_file.bin": binary_data})
        with service_binary.open_file("test_file.bin", mode="rb") as f:
            data = f.read()
            assert data == binary_data

        # Test with filepath and filename
        service3 = MockReaderService({"dir/test_file.txt": "Hello World"})
        with service3.open_file("dir", "test_file.txt") as f:
            content = f.read()
            assert content == "Hello World"
