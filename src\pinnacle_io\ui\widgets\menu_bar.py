"""
Menu bar widget for the main application window.
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import yaml
import os
from pathlib import Path

from ...utils.path_parser import parse_pinnacle_path


class MenuBar:
    """Application menu bar with File, Edit, View, and Help menus."""

    def __init__(self, parent, app, load_data_callback, save_dicom_files_callback, export_dicom_files_callback):
        """
        Initialize the menu bar.

        Args:
            parent: Parent window (root)
            app: Main application instance
            load_data_callback: Callback function to load data
        """
        self.parent = parent
        self.app = app
        self.load_data_callback = load_data_callback
        self.save_dicom_files_callback = save_dicom_files_callback
        self.export_dicom_files_callback = export_dicom_files_callback
        self.dicom_destinations = self._load_dicom_destinations()

        # Create menu bar
        self.menubar = tk.Menu(parent)
        parent.config(menu=self.menubar)

        self._create_file_menu()
        self._create_edit_menu()
        self._create_view_menu()
        self._create_export_menu()
        self._create_help_menu()
        self._bind_keyboard_shortcuts()

    def _load_dicom_destinations(self):
        """Load DICOM destinations from YAML configuration file."""
        try:
            # Path to dicom_destinations.yaml relative to this file
            config_path = Path(__file__).parent.parent.parent / "dicom_destinations.yaml"

            if not config_path.exists():
                print(f"Warning: DICOM destinations config not found at {config_path}")
                return {"destinations": []}

            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
                return config if config else {"destinations": []}
        except Exception as e:
            print(f"Error loading DICOM destinations config: {e}")
            return {"destinations": []}

    def _create_file_menu(self):
        """Create the File menu."""
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="File", menu=file_menu)

        file_menu.add_command(label="Open Folder...", command=self._file_open_folder, accelerator="Ctrl+O")
        file_menu.add_command(label="Open Archive...", command=self._file_open_archive, accelerator="Ctrl+Shift+O")
        file_menu.add_separator()
        file_menu.add_command(label="Close", command=self._file_close, accelerator="Ctrl+W")

    def _create_edit_menu(self):
        """Create the Edit menu."""
        edit_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Edit", menu=edit_menu)

        edit_menu.add_command(label="Undo...", command=self._edit_undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="Redo...", command=self._edit_redo, accelerator="Ctrl+Y")

    def _create_view_menu(self):
        """Create the View menu."""
        view_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="View", menu=view_menu)

        view_menu.add_command(label="Not Yet Implemented...", command=self._view_placeholder)

    def _create_export_menu(self):
        """Create the Export menu."""
        export_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Export", menu=export_menu)

        export_menu.add_command(label="Save DICOM Files...", command=self._export_save_dicom_files)

        # Add dynamic menu items for each DICOM destination
        destinations = self.dicom_destinations.get("destinations", [])
        if destinations:
            export_menu.add_separator()
            for destination in destinations:
                if destination.get("enabled", False):
                    name = destination.get("name", "Unknown")

                    export_menu.add_command(
                        label=f"Export to {name}...",
                        command=lambda dest=destination: self._export_dicom_files(dest)
                    )

    def _create_help_menu(self):
        """Create the Help menu."""
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Help", menu=help_menu)

        help_menu.add_command(label="About...", command=self._help_about)

    def _file_open_folder(self):
        """Handle File -> Open Folder menu action."""
        folder_path = filedialog.askdirectory(
            title="Select Pinnacle Institution Folder...",
            initialdir="."
        )

        if folder_path:
            self._file_close()
            print(f"Selected folder: {folder_path}")

            # Parse the path to detect archive structure and IDs
            path_info = parse_pinnacle_path(folder_path)
            print(f"Parsed path info: {path_info}")

            # Call the callback with the archive root and optional IDs
            self.load_data_callback(
                str(path_info.archive_root),
                # institution_id=path_info.institution_id,
                patient_id=path_info.patient_id,
                plan_id=path_info.plan_id
            )
        else:
            print("Folder selection cancelled")

    def _file_open_archive(self):
        """Handle File -> Open Archive menu action."""
        file_path = filedialog.askopenfilename(
            title="Select Pinnacle Archive File...",
            initialdir=".",
            filetypes=[
                ("All Supported Archives", ("*.tar", "*.tar.gz", "*.tgz", "*.zip")),
                ("TAR files", "*.tar"),
                ("Compressed TAR files", ("*.tar.gz", "*.tgz")),
                ("ZIP files", "*.zip"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self._file_close()
            print(f"Selected archive: {file_path}")
            self.load_data_callback(file_path)
        else:
            print("Archive selection cancelled")

    def _file_close(self):
        """Handle File -> Close menu action."""
        if self.app:
            self.app.close_data()

    def _edit_undo(self):
        """Handle Edit -> Undo menu action."""
        messagebox.showinfo("Menu", "Edit -> Undo clicked")

    def _edit_redo(self):
        """Handle Edit -> Redo menu action."""
        messagebox.showinfo("Menu", "Edit -> Redo clicked")

    def _view_placeholder(self):
        """Handle View menu placeholder action."""
        messagebox.showinfo("Menu", "View menu options not yet implemented")

    def _export_save_dicom_files(self):
        """Handle Export -> Save DICOM Files menu action."""
        
        # Get output directory from user
        save_dir = filedialog.askdirectory(
            title="Select DICOM Output Directory"
        )

        if not save_dir:
            return  # User cancelled directory selection

        # Validate output directory
        if not os.path.isdir(save_dir):
            show_error(self.root, "Save Error", f"Selected directory does not exist: {save_dir}")
            return

        self.save_dicom_files_callback(save_dir)

    def _export_dicom_files(self, destination):
        """Handle Export -> Export to [Destination] menu action."""
        if destination is None:
            messagebox.showwarning("Export", "No DICOM destinations configured")
            return

        destination_name = destination.get("name", "Unknown")
        host = destination.get("host", "unknown")
        port = destination.get("port", "unknown")
        ae_title = destination.get("ae_title", "unknown")

        print(f"Export to {destination_name}\nHost: {host}:{port}\nAE Title: {ae_title}")
        self.export_dicom_files_callback(destination)

    def _help_about(self):
        """Handle Help -> About menu action."""
        about_text = """Pinnacle IO - Radiotherapy Treatment Planning Viewer

Version: 1.0.0

A Python application for viewing and analyzing Pinnacle radiotherapy
treatment planning data.

Built with:
• Python
• ttkbootstrap (darkly theme)
• Pinnacle IO Library

© 2025 - WellSpan Radiation Oncology"""

        messagebox.showinfo("About Pinnacle IO", about_text)

    def _bind_keyboard_shortcuts(self):
        """Bind keyboard shortcuts to menu actions."""
        # File menu shortcuts
        self.parent.bind_all("<Control-o>", lambda e: self._file_open_folder())
        self.parent.bind_all("<Control-O>", lambda e: self._file_open_folder())
        self.parent.bind_all("<Control-Shift-O>", lambda e: self._file_open_archive())
        self.parent.bind_all("<Control-Shift-o>", lambda e: self._file_open_archive())
        self.parent.bind_all("<Control-w>", lambda e: self._file_close())
        self.parent.bind_all("<Control-W>", lambda e: self._file_close())

        # Edit menu shortcuts
        self.parent.bind_all("<Control-z>", lambda e: self._edit_undo())
        self.parent.bind_all("<Control-Z>", lambda e: self._edit_undo())
        self.parent.bind_all("<Control-y>", lambda e: self._edit_redo())
        self.parent.bind_all("<Control-Y>", lambda e: self._edit_redo())