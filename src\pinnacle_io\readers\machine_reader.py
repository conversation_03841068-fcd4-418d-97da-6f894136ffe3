"""
Reader for Pinnacle plan.Pinnacle.Machines files.
"""

from typing import List, Any
from pinnacle_io.models import Machine
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class MachineReader(BasePinnacleReader):
    """
    Reader for Pinnacle plan.Pinnacle.Machines files.
    """

    @staticmethod
    def read_from_ids(institution_id: int, patient_id: int, plan_id: int, mount_id: int = 0, file_service: Any = None) -> List[Machine]:
        """
        Read a Pinnacle plan.Pinnacle.Machines file using ID-based loading.

        Args:
            institution_id: Institution ID number
            patient_id: Patient ID number
            plan_id: Plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)
            file_service: File service object with open_file method

        Returns:
            List of Machine models populated with data from the files

        Usage:
            machines = MachineReader.read_from_ids(1, 1, 0, file_service=file_service)
            machines = MachineReader.read_from_ids(1, 1, 0, 0, file_service=file_service)
        """
        # Construct standard Pinnacle path format
        plan_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"
        # Delegate to path-based method
        return MachineReader.read_from_path(plan_path, file_service)

    @staticmethod
    def read_from_path(plan_path: str, file_service: Any = None) -> List[Machine]:
        """
        Read a Pinnacle plan.Pinnacle.Machines file using path-based loading.

        Args:
            plan_path: Path to the plan directory or plan.Pinnacle.Machines file
            file_service: File service object with open_file method

        Returns:
            List of Machine models populated with data from the files

        Usage:
            machines = MachineReader.read_from_path("/path/to/Patient_0/Plan_0/plan.Pinnacle.Machines")
            machines = MachineReader.read_from_path("/path/to/Patient_0/Plan_0")
            machines = MachineReader.read_from_path("/path/to/Patient_0/Plan_0", file_service=file_service)
        """
        # Resolve file path and name
        file_path, file_name = MachineReader._resolve_file_path(
            plan_path, "plan.Pinnacle.Machines", ["plan.pinnacle.Machines", "plan.Pinnacle.Machines"]
        )

        # Read file content using base class utility
        content_lines = MachineReader._read_file_lines(file_path, file_name, file_service)

        return MachineReader.parse(content_lines)

    @staticmethod
    def read(plan_path: str, file_service: Any = None) -> List[Machine]:
        """
        Legacy method for backward compatibility.
        
        Deprecated: Use read_from_path() or read_from_ids() instead.
        """
        return MachineReader.read_from_path(plan_path, file_service)

    @staticmethod
    def parse(content_lines: list[str]) -> List[Machine]:
        """
        Parse a Pinnacle Machine content string and create a list of Machine models.

        Args:
            content_lines: Pinnacle Machine content lines

        Returns:
            List of Machine models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        machines: List[Machine] = []
        for machine_data in data.values():
            machines.append(Machine(**machine_data))
        return machines
