# Reader Refactor Checklist: Unifying `parse` Methods and Extracting Reader Tests

This checklist guides the staged refactor to rename all `parse_[object]_content` methods in `pinnacle_io/readers` to `parse`, update all usages, update docstrings/documentation, and extract reader tests to `tests/readers/`. Complete each section before moving to the next. Run tests after each batch of changes.

---

## General Steps for Each Reader
- [ ] Rename `parse_[object]_content` to `parse` and mark as `@staticmethod` (if not already)
- [ ] Update all usages in the reader class and other readers
- [ ] Update all usages in the main codebase (if any)
- [ ] Update all usages in tests
- [ ] Update docstrings and comments to reference `parse`
- [ ] Extract relevant reader tests from model test files to `tests/readers/`
- [ ] Update or create new test(s) in `tests/readers/` for the reader
- [ ] Run the test suite and verify correctness

---

## 1. InstitutionReader
- [x] Rename `parse_institution_content` to `parse` (staticmethod)
- [x] Update usages in `institution_reader.py`
- [x] Update usages in tests (extract from `test_institution.py` to `tests/readers/test_institution_reader.py`)
- [x] Update docstrings/comments
- [x] Run tests

## 2. MachineReader
- [x] Rename `parse_machine_content` to `parse` (staticmethod)
- [x] Update usages in `machine_reader.py`
- [x] Update usages in tests (extract from `test_machine.py` to `tests/readers/test_machine_reader.py`)
- [x] Update docstrings/comments
- [x] Run tests

## 3. PatientReader
- [x] Rename `parse_patient_content` to `parse` (staticmethod)
- [x] Update usages in `patient_reader.py`
- [x] Update usages in tests (extract from `test_patient.py` to `tests/readers/test_patient_reader.py`)
- [x] Update docstrings/comments
- [x] Extract relevant reader tests from model test files to `tests/readers/test_patient_reader.py`
- [x] Update or create new test(s) in `tests/readers/` for the reader
- [x] Run the test suite and verify correctness

## 4. PatientSetupReader
- [x] Rename `parse_patient_setup_content` to `parse` (staticmethod)
- [x] Update usages in `patient_setup_reader.py`
- [x] Update usages in tests (extract from `test_patient_setup.py` to `tests/readers/test_patient_setup_reader.py`)
- [x] Update docstrings/comments
- [x] Update or create new test(s) in `tests/readers/` for the reader
- [x] Run tests

## 5. ImageSetReader
- [x] Rename `parse_header_content` to `parse` (staticmethod)
- [x] Rename `parse_image_info_content` to `parse_image_info` (or `parse_image_info_content` to `parse_image_info` for clarity)
- [x] Update usages in `image_set_reader.py`
- [x] Update usages in tests (extract from `test_image_set.py` to `tests/readers/test_image_set_reader.py`)
- [x] Update docstrings/comments
- [x] Update or create new test(s) in `tests/readers/` for the reader
- [x] Run tests

## 5b. ImageInfoReader
- [x] Rename `read_image_info` to `read` and `parse_image_info` to `parse` (staticmethod)
- [x] Update usages in `image_info_reader.py`, `image_set_reader.py`, and elsewhere
- [x] Update docstrings and comments to reference `read` and `parse`
- [x] Extract relevant reader tests from model test files to `tests/readers/test_image_info_reader.py`
- [x] Update or create new test(s) in `tests/readers/` for the reader
- [x] Run the test suite and verify correctness

## 6. PlanReader
- [x] Rename `parse_patient_content` to `parse` (staticmethod)
- [x] Update usages in `plan_reader.py`
- [x] Update usages in tests (extract from `test_plan.py` to `tests/readers/test_plan_reader.py`)
- [x] Update docstrings/comments
- [x] Run tests

## 7. TrialReader
- [x] Rename `parse_trial_content` to `parse` (staticmethod)
- [x] Update usages in `trial_reader.py`
- [x] Update usages in tests (extract from `test_trial.py` to `tests/readers/test_trial_reader.py`)
- [x] Update docstrings/comments
- [x] Run tests

## 8. PointReader
- [x] Rename `parse_point_content` to `parse` (staticmethod)
- [x] Update usages in `point_reader.py`
- [x] Update usages in tests (extract from `test_point.py` to `tests/readers/test_point_reader.py`)
- [x] Update docstrings/comments
- [x] Run tests

## 9. ROIReader
- [x] Rename `parse_roi_content` to `parse` (staticmethod)
- [x] Update usages in `roi_reader.py`
- [x] Update usages in tests (extract from `test_roi.py` to `tests/readers/test_roi_reader.py`)
- [x] Update docstrings/comments
- [x] Run tests

## 10. DoseReader
- [x] If any `parse_*_content` methods exist, rename to `parse` (staticmethod)
- [x] Update usages in `dose_reader.py`
- [x] Update usages in tests (extract from `test_dose.py` to `tests/readers/test_dose_reader.py`)
- [x] Update docstrings/comments
- [x] Run tests

## 11. PinnacleFileReader
- [x] Rename `parse_key_value_content` to `parse` (staticmethod)
- [x] Rename `parse_key_value_content_lines` to `parse_lines` (staticmethod)
- [x] Update usages in all readers and tests (notably `test_pinnacle_file_reader.py`)
- [x] Update docstrings/comments
- [x] Run tests

---

## Final Steps
- [x] Review and update any remaining documentation, comments, or usage examples referencing old method names
- [x] Ensure all tests pass
- [x] Clean up any unused imports or code
- [x] Commit changes with a clear message summarizing the refactor

---

**Tip:** After each reader is refactored and its tests are extracted and passing, commit your changes before proceeding to the next reader. This will make it easier to isolate and fix issues if they arise. 