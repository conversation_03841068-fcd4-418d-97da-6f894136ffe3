"""
Error handling examples for pinnacle_io library.

This module demonstrates comprehensive error handling patterns
when working with Pinnacle data.
"""

from pinnacle_io import PinnacleReader
from pathlib import Path
import logging

# Configure logging for better error tracking
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def handle_file_not_found():
    """Demonstrate handling FileNotFoundError."""
    
    print("FileNotFoundError Handling:")
    print("-" * 30)
    
    invalid_paths = [
        "/nonexistent/path/to/data",
        "/path/to/missing/data.tar.gz",
        "/path/to/missing/data.zip"
    ]
    
    for path in invalid_paths:
        try:
            reader = PinnacleReader(path)
            institution = reader.get_institution()
            print(f"✓ Successfully read: {path}")
            
        except FileNotFoundError as e:
            print(f"✗ File not found: {Path(path).name}")
            logger.error(f"FileNotFoundError: {e}")
            
        except Exception as e:
            print(f"✗ Unexpected error: {e}")
            logger.error(f"Unexpected error: {e}")

def handle_unsupported_formats():
    """Demonstrate handling unsupported file formats."""
    
    print("\nUnsupported Format Handling:")
    print("-" * 30)
    
    unsupported_files = [
        "/path/to/data.rar",
        "/path/to/data.7z",
        "/path/to/data.txt"
    ]
    
    for file_path in unsupported_files:
        try:
            reader = PinnacleReader(file_path)
            institution = reader.get_institution()
            print(f"✓ Successfully read: {Path(file_path).name}")
            
        except ValueError as e:
            print(f"✗ Unsupported format: {Path(file_path).name}")
            logger.error(f"ValueError: {e}")
            
        except FileNotFoundError:
            print(f"- File not found: {Path(file_path).name}")
            
        except Exception as e:
            print(f"✗ Unexpected error: {e}")
            logger.error(f"Unexpected error: {e}")

def handle_corrupted_data():
    """Demonstrate handling corrupted or invalid data."""
    
    print("\nCorrupted Data Handling:")
    print("-" * 30)
    
    # This would typically be a real path to corrupted data
    corrupted_path = "/path/to/corrupted/data.tar.gz"
    
    try:
        reader = PinnacleReader(corrupted_path)
        institution = reader.get_institution()
        print(f"✓ Successfully read institution: {institution.name}")
        
    except FileNotFoundError:
        print(f"- File not found: {Path(corrupted_path).name}")
        
    except Exception as e:
        print(f"✗ Data corruption or parsing error: {e}")
        logger.error(f"Data corruption error: {e}")
        
        # Provide guidance for troubleshooting
        print("  Troubleshooting tips:")
        print("  - Verify file integrity")
        print("  - Check if file was properly extracted")
        print("  - Ensure file is a valid Pinnacle archive")

def handle_missing_files():
    """Demonstrate handling missing specific files within archives."""
    
    print("\nMissing File Handling:")
    print("-" * 30)
    
    # Simulate reading from a path that exists but has missing files
    data_path = "/path/to/pinnacle/data"
    
    try:
        reader = PinnacleReader(data_path)
        
        # Try to read institution (most likely to exist)
        try:
            institution = reader.get_institution()
            print(f"✓ Institution: {institution.name}")
        except Exception as e:
            print(f"✗ Missing Institution file: {e}")
            logger.error(f"Institution error: {e}")
            return
        
        # Try to read patient data
        try:
            patient = reader.get_patient("Institution_1/Mount_0/Patient_1")
            print(f"✓ Patient: {patient.first_name} {patient.last_name}")
        except Exception as e:
            print(f"✗ Missing Patient file: {e}")
            logger.warning(f"Patient error: {e}")
        
        # Try to read plan data
        try:
            trials = reader.get_trials("Institution_1/Mount_0/Patient_1/Plan_1")
            print(f"✓ Trials: {len(trials)} found")
        except Exception as e:
            print(f"✗ Missing Plan files: {e}")
            logger.warning(f"Plan error: {e}")
        
        # Try to read dose data
        try:
            dose = reader.get_dose("Institution_1/Mount_0/Patient_1/Plan_1")
            print(f"✓ Dose: {dose.max_dose} cGy max")
        except Exception as e:
            print(f"✗ Missing Dose files: {e}")
            logger.warning(f"Dose error: {e}")
            
    except FileNotFoundError:
        print(f"- Data path not found: {Path(data_path).name}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        logger.error(f"Unexpected error: {e}")

def robust_data_reader():
    """Demonstrate a robust data reader with comprehensive error handling."""
    
    print("\nRobust Data Reader:")
    print("-" * 30)
    
    def safe_read_institution(reader):
        """Safely read institution data."""
        try:
            institution = reader.get_institution()
            logger.info(f"Successfully read institution: {institution.name}")
            return institution
        except Exception as e:
            logger.error(f"Failed to read institution: {e}")
            return None
    
    def safe_read_patients(reader, institution):
        """Safely read patient data."""
        if not institution or not institution.patient_lite_list:
            logger.warning("No patients available to read")
            return []
        
        patients = []
        for patient_lite in institution.patient_lite_list:
            try:
                patient = reader.get_patient(patient_lite.patient_path)
                patients.append(patient)
                logger.info(f"Successfully read patient: {patient.first_name} {patient.last_name}")
            except Exception as e:
                logger.error(f"Failed to read patient {patient_lite.patient_path}: {e}")
                continue
        
        return patients
    
    def safe_read_plans(reader, patient_paths):
        """Safely read plan data."""
        plans = []
        for patient_path in patient_paths:
            plan_path = f"{patient_path}/Plan_1"
            try:
                trials = reader.get_trials(plan_path)
                rois = reader.get_rois(plan_path)
                plans.append({
                    'patient_path': patient_path,
                    'trials': trials,
                    'rois': rois
                })
                logger.info(f"Successfully read plan for {patient_path}")
            except Exception as e:
                logger.error(f"Failed to read plan {plan_path}: {e}")
                continue
        
        return plans
    
    # Main robust reader logic
    data_path = "/path/to/pinnacle/data"
    
    try:
        reader = PinnacleReader(data_path)
        logger.info(f"Initialized reader for: {data_path}")
        
        # Read institution
        institution = safe_read_institution(reader)
        if not institution:
            print("✗ Cannot proceed without institution data")
            return
        
        # Read patients
        patients = safe_read_patients(reader, institution)
        print(f"✓ Successfully read {len(patients)} patients")
        
        # Read plans
        patient_paths = [p.patient_path for p in institution.patient_lite_list]
        plans = safe_read_plans(reader, patient_paths)
        print(f"✓ Successfully read {len(plans)} plans")
        
        # Summary
        print(f"\nSummary:")
        print(f"  Institution: {institution.name}")
        print(f"  Patients: {len(patients)}")
        print(f"  Plans: {len(plans)}")
        
    except Exception as e:
        print(f"✗ Failed to initialize reader: {e}")
        logger.error(f"Reader initialization error: {e}")

def validation_examples():
    """Demonstrate data validation."""
    
    print("\nData Validation Examples:")
    print("-" * 30)
    
    def validate_institution(institution):
        """Validate institution data."""
        if not institution:
            raise ValueError("Institution is None")
        
        if not institution.name:
            raise ValueError("Institution name is empty")
        
        if not institution.patient_lite_list:
            logger.warning("Institution has no patients")
        
        print(f"✓ Institution validation passed: {institution.name}")
        return True
    
    def validate_patient(patient):
        """Validate patient data."""
        if not patient:
            raise ValueError("Patient is None")
        
        if not patient.first_name and not patient.last_name:
            raise ValueError("Patient has no name")
        
        if not patient.medical_record_number:
            logger.warning("Patient has no medical record number")
        
        print(f"✓ Patient validation passed: {patient.first_name} {patient.last_name}")
        return True
    
    # Example validation usage
    try:
        reader = PinnacleReader("/path/to/pinnacle/data")
        
        # Validate institution
        institution = reader.get_institution()
        validate_institution(institution)
        
        # Validate first patient
        if institution.patient_lite_list:
            patient = reader.get_patient(institution.patient_lite_list[0].patient_path)
            validate_patient(patient)
        
    except ValueError as e:
        print(f"✗ Validation error: {e}")
        logger.error(f"Validation error: {e}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        logger.error(f"Unexpected error: {e}")

def main():
    """Main example function."""
    
    print("Pinnacle IO Error Handling Examples")
    print("=" * 40)
    
    # Demonstrate different error scenarios
    handle_file_not_found()
    handle_unsupported_formats()
    handle_corrupted_data()
    handle_missing_files()
    
    # Demonstrate robust reading
    robust_data_reader()
    
    # Demonstrate validation
    validation_examples()
    
    print("\n" + "=" * 40)
    print("Error handling examples completed!")
    print("\nBest Practices:")
    print("1. Always use try-except blocks")
    print("2. Log errors for debugging")
    print("3. Provide meaningful error messages")
    print("4. Validate data after reading")
    print("5. Handle missing files gracefully")
    print("6. Use logging for error tracking")

if __name__ == "__main__":
    main()