"""
DICOM Export Manager for SimpleDicomConverter.

This module provides selective data loading and modality export capabilities
for DICOM conversion. It optimizes data loading based on specific modality
requirements, enabling efficient export of any combination of CT, RTSTRUCT,
RTPLAN, and RTDOSE.

The DicomExportManager coordinates selective loading based on modality requirements:
- CT: Patient + Plan + Planning CT + CT pixel data
- RTSTRUCT: Patient + Plan + Planning CT + ROI data
- RTPLAN: Patient + Plan + Trial data
- RTDOSE: Patient + Plan + Trial + Planning CT + Dose data
"""

import logging
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from pinnacle_io import PinnacleReader
from pinnacle_io.models import ROI, Dose, ImageSet, Institution, Patient, Plan, Point, Trial

from pinnacle_io.services.dicom_export_service import DicomExportConfig, DicomExportResult, DicomExportService

logger = logging.getLogger(__name__)


@dataclass
class DataRequirements:
    """Data requirements based on modality needs."""

    needs_trial: bool = False
    needs_planning_ct: bool = False
    needs_ct_pixel_data: bool = False
    needs_roi_data: bool = False
    needs_dose_data: bool = False


@dataclass
class LoadedData:
    """Container for selectively loaded data components."""

    patient: Optional[Patient] = None
    plan: Optional[Plan] = None
    trial: Optional[Trial] = None
    planning_ct: Optional[ImageSet] = None
    roi_list: Optional[List[ROI]] = None
    point_list: Optional[List[Point]] = None
    dose: Optional[Dose] = None


class DicomExportManager:
    """
    Coordinates selective data loading and modality export.

    This class implements the selective loading strategy outlined in the
    modality-specific export plan, loading only the minimum required data
    based on requested modalities with performance optimization through
    lazy loading and caching.
    """

    # Modality requirement matrix from the plan
    MODALITY_REQUIREMENTS = {
        "CT": DataRequirements(needs_planning_ct=True, needs_ct_pixel_data=True),
        "RTSTRUCT": DataRequirements(needs_planning_ct=True, needs_roi_data=True),
        "RTPLAN": DataRequirements(needs_trial=True),
        "RTDOSE": DataRequirements(needs_trial=True, needs_planning_ct=True, needs_dose_data=True),
    }

    def __init__(self, pinnacle_reader: PinnacleReader):
        """
        Initialize the DICOM export manager.

        Args:
            pinnacle_reader: PinnacleReader instance for accessing data
        """
        self.pinnacle_reader = pinnacle_reader
        self.logger = logging.getLogger(__name__)

        # Performance optimization: cache frequently accessed data
        self._cache: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, float] = {}
        self._cache_ttl = 300  # 5 minutes cache TTL

    def export_modalities(
        self,
        modalities: List[str],
        institution: Institution,
        patient: Patient,
        plan: Plan,
        trial: Optional[Trial],
        output_path: str,
    ) -> DicomExportResult:
        """
        Export only requested modalities with optimized data loading.

        Args:
            modalities: List of modalities to export (CT, RTSTRUCT, RTPLAN, RTDOSE)
            institution: Institution model object
            patient: Patient model object
            plan: Plan model object
            trial: Trial model object (can be None for some modalities)
            output_path: Output directory path

        Returns:
            DicomExportResult containing success status and exported files
        """
        try:
            self.logger.info(f"Starting selective export for modalities: {modalities}")

            # Validate modalities
            valid_modalities = self._validate_modalities(modalities)
            if not valid_modalities:
                return DicomExportResult(success=False, error="No valid modalities specified")

            # Analyze data requirements for requested modalities
            data_requirements = self._analyze_modality_requirements(valid_modalities)
            self.logger.info(f"Data requirements: {data_requirements}")

            # Perform selective data loading
            loaded_data = self._selective_data_loading(data_requirements, institution, patient, plan, trial)

            # Export selected modalities using DicomExportService
            return self._export_selected_modalities(valid_modalities, loaded_data, output_path, institution)

        except Exception as e:
            error_msg = f"Error during selective export: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return DicomExportResult(success=False, error=error_msg)

    def _validate_modalities(self, modalities: List[str]) -> List[str]:
        """
        Validate requested modalities against supported types.

        Args:
            modalities: List of requested modalities

        Returns:
            List of valid modalities
        """
        valid_modalities = []
        supported = set(self.MODALITY_REQUIREMENTS.keys())

        for modality in modalities:
            modality = modality.upper()
            if modality in supported:
                valid_modalities.append(modality)
            else:
                self.logger.warning(f"Unsupported modality '{modality}' - skipping")

        return valid_modalities

    def _analyze_modality_requirements(self, modalities: List[str]) -> DataRequirements:
        """
        Analyze data requirements based on requested modalities.

        Args:
            modalities: List of valid modalities

        Returns:
            Consolidated DataRequirements object
        """
        requirements = DataRequirements()

        for modality in modalities:
            modality_req = self.MODALITY_REQUIREMENTS[modality]

            # Combine requirements using logical OR
            requirements.needs_trial |= modality_req.needs_trial
            requirements.needs_planning_ct |= modality_req.needs_planning_ct
            requirements.needs_ct_pixel_data |= modality_req.needs_ct_pixel_data
            requirements.needs_roi_data |= modality_req.needs_roi_data
            requirements.needs_dose_data |= modality_req.needs_dose_data

        return requirements

    def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get cached data if still valid."""
        if cache_key not in self._cache:
            return None

        # Check if cache is still valid
        cache_time = self._cache_timestamps.get(cache_key, 0)
        if time.time() - cache_time > self._cache_ttl:
            # Cache expired
            del self._cache[cache_key]
            del self._cache_timestamps[cache_key]
            return None

        return self._cache[cache_key]

    def _set_cached_data(self, cache_key: str, data: Any):
        """Cache data with timestamp."""
        self._cache[cache_key] = data
        self._cache_timestamps[cache_key] = time.time()

    def _selective_data_loading(
        self,
        requirements: DataRequirements,
        institution: Institution,
        patient: Patient,
        plan: Plan,
        trial: Optional[Trial],
    ) -> LoadedData:
        """
        Load only required data components based on modality needs.

        Args:
            requirements: Data requirements specification
            institution: Institution model object
            patient: Patient model object
            plan: Plan model object
            trial: Trial model object

        Returns:
            LoadedData object with only required data loaded
        """
        loaded_data = LoadedData()

        # Always load patient and plan data (required by all modalities)
        loaded_data.patient = patient
        loaded_data.plan = plan

        # Load trial only if RTPLAN or RTDOSE requested
        if requirements.needs_trial:
            loaded_data.trial = trial
            self.logger.info("Loaded trial data")

        # Load planning CT only if CT, RTSTRUCT, or RTDOSE requested
        if requirements.needs_planning_ct:
            # Use caching for planning CT data
            cache_key = f"planning_ct_{patient.patient_id}_{plan.plan_id}"
            planning_ct = self._get_cached_data(cache_key)

            if planning_ct is None:
                try:
                    if plan.primary_ct_image_set is None:
                        # Use primary_ct_image_set_id directly
                        image_set_id = plan.primary_ct_image_set_id or 0
                        planning_ct = self.pinnacle_reader.get_image_set(institution, patient, image_set_id)
                        plan.primary_ct_image_set = planning_ct
                    else:
                        # Check if the ImageSet has a valid image_set_id
                        if hasattr(plan.primary_ct_image_set, 'image_set_id') and plan.primary_ct_image_set.image_set_id is not None:
                            planning_ct = self.pinnacle_reader.get_image_set(institution, patient, plan.primary_ct_image_set)
                        else:
                            # Fallback to using primary_ct_image_set_id
                            image_set_id = plan.primary_ct_image_set_id or 0
                            planning_ct = self.pinnacle_reader.get_image_set(institution, patient, image_set_id)
                    self._set_cached_data(cache_key, planning_ct)
                    self.logger.info("Loaded and cached planning CT")
                except Exception as e:
                    self.logger.warning(f"Could not load planning CT: {e}")
                    planning_ct = None
            else:
                self.logger.info("Using cached planning CT data")

            if planning_ct:
                if requirements.needs_ct_pixel_data:
                    # Load full pixel data for CT export
                    loaded_data.planning_ct = planning_ct
                    self.logger.info("Planning CT with full pixel data")
                else:
                    # Minimal CT data for coordinate reference only
                    loaded_data.planning_ct = planning_ct
                    self.logger.info("Planning CT for coordinate reference")
            else:
                loaded_data.planning_ct = None

        # Load ROI data only if RTSTRUCT requested
        if requirements.needs_roi_data:
            # Use caching for ROI data
            roi_cache_key = f"roi_list_{patient.patient_id}_{plan.plan_id}"
            point_cache_key = f"point_list_{patient.patient_id}_{plan.plan_id}"

            roi_list = self._get_cached_data(roi_cache_key)
            if roi_list is None:
                try:
                    roi_list = self.pinnacle_reader.get_rois(institution, patient, plan)
                    self._set_cached_data(roi_cache_key, roi_list)
                    self.logger.info(f"Loaded and cached {len(roi_list)} ROIs")
                except Exception as e:
                    self.logger.warning(f"Could not load ROI data: {e}")
                    roi_list = []
            else:
                self.logger.info(f"Using cached ROI data ({len(roi_list)} ROIs)")
            loaded_data.roi_list = roi_list

            # Populate plan.roi_list so DicomExportService can access it
            plan.roi_list = roi_list

            # Also load point list if available
            point_list = self._get_cached_data(point_cache_key)
            if point_list is None:
                try:
                    point_list = self.pinnacle_reader.get_points(institution, patient, plan)
                    self._set_cached_data(point_cache_key, point_list)
                    self.logger.info(f"Loaded and cached {len(point_list)} points")
                except Exception as e:
                    self.logger.warning(f"Could not load points: {e}")
                    point_list = []
            else:
                self.logger.info(f"Using cached point data ({len(point_list)} points)")
            loaded_data.point_list = point_list

            # Populate plan.point_list so DicomExportService can access it
            plan.point_list = point_list

        # Load dose data only if RTDOSE requested
        if requirements.needs_dose_data:
            # Use caching for dose data
            dose_cache_key = f"dose_{patient.patient_id}_{plan.plan_id}_{trial.trial_id if trial else 'no_trial'}"
            dose = self._get_cached_data(dose_cache_key)

            if dose is None:
                try:
                    if trial:
                        dose = self.pinnacle_reader.get_dose(institution, patient, plan, trial)
                        self._set_cached_data(dose_cache_key, dose)
                        self.logger.info("Loaded and cached dose data")
                    else:
                        self.logger.warning("Cannot load dose data without trial")
                        dose = None
                except Exception as e:
                    self.logger.warning(f"Could not load dose data: {e}")
                    dose = None
            else:
                self.logger.info("Using cached dose data")

            loaded_data.dose = dose

        return loaded_data

    def get_image_set(self, institution: Institution, patient: Patient, plan: Plan) -> Optional[ImageSet]:
        """
        Delegate get_image_set calls to the pinnacle_reader.
        
        This method provides compatibility with DicomExportService which expects
        a file_service with get_image_set method.
        """
        if plan and plan.primary_ct_image_set_id is not None:
            return self.pinnacle_reader.get_image_set(institution, patient, plan.primary_ct_image_set_id)
        return None

    def _export_selected_modalities(
        self, modalities: List[str], loaded_data: LoadedData, output_path: str, institution: Institution
    ) -> DicomExportResult:
        """
        Export selected modalities using loaded data.

        Args:
            modalities: List of modalities to export
            loaded_data: Selectively loaded data
            output_path: Output directory path
            institution: Institution model object

        Returns:
            DicomExportResult from the export operation
        """
        # Create export configuration
        config = DicomExportConfig(output_directory=output_path, modalities=modalities, validate_output=True)

        # Create export service
        export_service = DicomExportService(self)

        # Export using the loaded data
        result = export_service.export_dicom_files(
            institution=institution,  # Pass the original institution
            patient=loaded_data.patient,
            plan=loaded_data.plan,
            trial=loaded_data.trial,
            config=config,
        )

        # Add selective loading info to result
        if result.success:
            self.loaded_data = loaded_data  # Store for reference
            self.logger.info(f"Successfully exported {len(result.exported_modalities)} modalities")

        return result
