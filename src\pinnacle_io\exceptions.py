"""
Custom exceptions for pinnacle_io DICOM conversion.

This module provides standardized exception classes for the DICOM conversion system,
enabling consistent error handling and meaningful error messages across all converters.
"""

from typing import Any, Dict, Optional


class DicomConversionError(Exception):
    """
    Base exception for all DICOM conversion errors.

    Provides structured error information with context for debugging and user feedback.
    """

    def __init__(
        self, message: str, context: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None
    ) -> None:
        """
        Initialize DICOM conversion error.

        Args:
            message: Human-readable error description
            context: Additional context information for debugging
            cause: Original exception that caused this error (if any)
        """
        super().__init__(message)
        self.message = message
        self.context = context or {}
        self.cause = cause

    def __str__(self) -> str:
        """Return formatted error message with context."""
        base_msg = self.message
        if self.context:
            context_info = ", ".join(f"{k}={v}" for k, v in self.context.items())
            base_msg += f" (Context: {context_info})"
        if self.cause:
            base_msg += f" (Caused by: {self.cause})"
        return base_msg


class DataValidationError(DicomConversionError):
    """Raised when required data is missing or invalid."""

    pass


class FileReadError(DicomConversionError):
    """Raised when file reading operations fail."""

    pass


class ModelDataError(DicomConversionError):
    """Raised when model data is inconsistent or corrupted."""

    pass


class CoordinateTransformError(DicomConversionError):
    """Raised when coordinate system transformations fail."""

    pass


class DoseDataError(DicomConversionError):
    """Raised when dose data processing fails."""

    pass


class StructureDataError(DicomConversionError):
    """Raised when structure/ROI data processing fails."""

    pass


class ImageDataError(DicomConversionError):
    """Raised when image data processing fails."""

    pass


class PlanDataError(DicomConversionError):
    """Raised when treatment plan data processing fails."""

    pass


class UIDGenerationError(DicomConversionError):
    """Raised when DICOM UID generation or management fails."""

    pass


class ConfigurationError(DicomConversionError):
    """Raised when configuration data is invalid or missing."""

    pass
