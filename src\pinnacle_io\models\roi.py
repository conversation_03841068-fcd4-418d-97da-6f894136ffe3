"""
SQLAlchemy model for Pinnacle ROI (Region of Interest) data.

This module provides the data models for representing structure sets in Pinnacle,
including ROIs and their constituent curves. The models support both contoured
structures and automatically generated regions, with full support for density
overrides and visualization properties.
"""

from __future__ import annotations
from typing import TYPE_CHECKING, Any

import numpy as np
from sqlalchemy import Column, Integer, String, Float, Boolean, ForeignKey, LargeBinary
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.plan import Plan


class Curve(PinnacleBase):
    """
    Model representing a single contour curve within an ROI structure.

    A Curve represents a single closed or open contour on an axial slice of a 3D image.
    Each curve consists of a series of 3D points that define the contour's shape.
    Multiple curves can exist on a single slice, and the collection of curves across
    all slices defines the complete 3D structure of an ROI.

    Technical Details:
        - Points are stored as binary data for efficient storage
        - Supports both planar and non-planar contours
        - Maintains slice location information for 3D reconstruction
        - Uses numpy arrays for point manipulation
        - Points are stored as 32-bit floats in [x, y, z] format

    Attributes:
        id (int): Primary key (inherited from PinnacleBase)
        points_data (bytes): Binary storage of point coordinates as float32
        contour_geometric_type (str): Type of contour (e.g., "CLOSED_PLANAR")
        flags (int): Bit flags defining curve properties
        block_size (int): Size of data blocks for storage
        num_points (int): Number of points in the curve
        curve_number (int): Sequential identifier within the ROI
        slice_index (int, optional): Index of the image slice
        z_position (float, optional): Z-coordinate of the slice plane

    Properties:
        points (np.ndarray): Array of shape (N, 3) containing point coordinates
        point_count (int): Number of points in the curve

    Relationships:
        roi (ROI): Parent ROI containing this curve (many-to-one)
    """

    __tablename__: str = "Curve"

    # Points stored as binary data (4-byte floats)
    points_data: Mapped[bytes | None] = Column("PointsData", LargeBinary, nullable=True)
    contour_geometric_type: Mapped[str | None] = Column("ContourGeometricType", String, nullable=True)

    flags: Mapped[int | None] = Column("Flags", Integer, nullable=True)
    block_size: Mapped[int | None] = Column("BlockSize", Integer, nullable=True)
    num_points: Mapped[int | None] = Column("NumPoints", Integer, nullable=True)
    curve_number: Mapped[int | None] = Column("CurveNumber", Integer, nullable=True)
    slice_index: Mapped[int | None] = Column("SliceIndex", Integer, nullable=True)
    z_position: Mapped[float | None] = Column("ZPosition", Float, nullable=True)

    # Parent relationship with optimized loading
    roi_id: Mapped[int] = Column("ROIID", Integer, ForeignKey("ROI.ID", ondelete="CASCADE"), nullable=False)
    roi: Mapped["ROI"] = relationship(
        "ROI",
        back_populates="curve_list",
        lazy="joined",  # Optimize loading as ROI info is frequently needed
    )

    def __init__(
        self,
        points: np.ndarray | list[list[float]] | None = None,
        **kwargs: dict[str, Any],
    ) -> None:
        """
        Initialize a Curve instance.

        Args:
            points: Optional array-like of points, shape (N, 3) where N is number of points.
                   Each point should be [x, y, z] coordinates.
            **kwargs: Additional keyword arguments for Curve attributes.

        Example:
            >>> points = [[0, 0, 0], [1, 0, 0], [1, 1, 0], [0, 1, 0]]
            >>> curve = Curve(
            ...     points=points,
            ...     contour_geometric_type="CLOSED_PLANAR",
            ...     curve_number=1,
            ...     slice_index=5
            ... )
        """
        super().__init__(**kwargs)
        if points is not None:
            self.points = points

    def __repr__(self) -> str:
        """Generate a string representation of the Curve."""
        return f"<Curve(id={self.id}, curve_number={self.curve_number}, point_count={self.point_count})>"

    @property
    def points(self) -> np.ndarray:
        """
        Get the curve points as a numpy array.

        Returns:
            Array of shape (N, 3) containing [x, y, z] coordinates for each point.
        """
        if self.points_data is None:
            return np.zeros((0, 3), dtype=np.float32)
        return np.frombuffer(self.points_data, dtype=np.float32).reshape(-1, 3)

    @points.setter
    def points(self, value: np.ndarray | list[list[float]]) -> None:
        """
        Set the curve points from an array-like object.

        Args:
            value: Array of points, shape (N, 3) where N is number of points.
                  Each point should be [x, y, z] coordinates.

        Raises:
            ValueError: If points array is not of shape (N, 3).
        """
        arr = np.asarray(value, dtype=np.float32)
        if arr.ndim != 2 or arr.shape[1] != 3:
            raise ValueError("Points must be a 2D array with shape (N, 3)")
        self.points_data = arr.tobytes()
        self.num_points = len(arr)

    @property
    def point_count(self) -> int:
        """Get the number of points in the curve."""
        return self.num_points or 0

    def get_curve_data(self) -> np.ndarray:
        """
        Get curve data as a flat array of coordinates.

        Returns:
            1D array of coordinates in form [x1, y1, z1, x2, y2, z2, ...].
        """
        return self.points.ravel()


class ROI(PinnacleBase):
    """
    Model representing a Region of Interest (ROI) in radiation therapy planning.

    An ROI defines a volumetric structure within the patient anatomy, used for
    treatment planning, dose calculation, and plan evaluation. ROIs can represent
    various types of structures including:
    - Target volumes (GTV, CTV, PTV)
    - Critical organs at risk (OARs)
    - External patient contour
    - Support structures and markers
    - Auto-generated volumes

    Technical Details:
        - Supports both manual and auto-generated contours
        - Maintains visualization properties for display
        - Handles density overrides for dose calculation
        - Manages structure hierarchies and relationships
        - Stores volumetric and statistical properties

    Attributes:
        id (int): Primary key (inherited from PinnacleBase)
        roi_number (int): Unique identifier within the plan
        name (str): Display name of the ROI
        volume_name (str): Name of the volume this ROI belongs to
        stats_volume_name (str): Volume used for statistics
        roi_description (str): Detailed description
        roi_generation_algorithm (str): Method used to create the ROI
        roi_type (str): Classification of the ROI
        structure_type (str): Anatomical classification
        author (str): Creator of the ROI
        organ_name (str): Standardized organ name
        flags (int): Bit flags for ROI properties
        color (str): Display color
        volume (float): Volume in cubic centimeters
        pixel_min (float): Minimum pixel value within the ROI
        pixel_max (float): Maximum pixel value within the ROI
        pixel_mean (float): Mean pixel value within the ROI
        pixel_std (float): Standard deviation of pixel values
        bev_drr_outline (bool): Whether to display BEV DRR outline
        display_on_other_vols (bool): Display ROI on other volumes
        is_linked (bool): Whether the ROI is linked to other ROIs

    Properties:
        point_count (int): Total number of points across all curves

    Relationships:
        plan (Plan): Parent plan containing this ROI
        curve_list (list[Curve]): Contour curves defining the ROI shape
    """

    __tablename__: str = "ROI"

    roi_number: Mapped[int | None] = Column("ROINumber", Integer, nullable=True)
    name: Mapped[str | None] = Column("Name", String, nullable=True)
    volume_name: Mapped[str | None] = Column("VolumeName", String, nullable=True)
    stats_volume_name: Mapped[str | None] = Column("StatsVolumeName", String, nullable=True)
    roi_description: Mapped[str | None] = Column("ROIDescription", String, nullable=True)
    roi_generation_algorithm: Mapped[str | None] = Column("ROIGenerationAlgorithm", String, nullable=True)
    roi_type: Mapped[str | None] = Column("ROIType", String, nullable=True)
    structure_type: Mapped[str | None] = Column("StructureType", String, nullable=True)

    author: Mapped[str | None] = Column("Author", String, nullable=True)
    organ_name: Mapped[str | None] = Column("OrganName", String, nullable=True)
    flags: Mapped[int | None] = Column("Flags", Integer, nullable=True)
    roi_interpreted_type: Mapped[str | None] = Column("ROIInterpretedType", String, nullable=True)
    color: Mapped[str | None] = Column("Color", String, nullable=True)
    box_size: Mapped[float | None] = Column("BoxSize", Float, nullable=True)
    line_2d_width: Mapped[int | None] = Column("Line2DWidth", Integer, nullable=True)
    line_3d_width: Mapped[int | None] = Column("Line3DWidth", Integer, nullable=True)
    paint_brush_radius: Mapped[float | None] = Column("PaintBrushRadius", Float, nullable=True)
    paint_allow_curve_closing: Mapped[bool | None] = Column("PaintAllowCurveClosing", Boolean, nullable=True)
    curve_min_area: Mapped[float | None] = Column("CurveMinArea", Float, nullable=True)
    curve_overlap_min: Mapped[float | None] = Column("CurveOverlapMin", Float, nullable=True)
    lower_threshold: Mapped[float | None] = Column("Lower", Float, nullable=True)
    upper_threshold: Mapped[float | None] = Column("Upper", Float, nullable=True)
    radius: Mapped[float | None] = Column("Radius", Float, nullable=True)
    density: Mapped[float | None] = Column("Density", Float, nullable=True)
    density_units: Mapped[str | None] = Column("DensityUnits", String, nullable=True)
    override_data: Mapped[bool | None] = Column("OverrideData", Boolean, nullable=True)
    override_order: Mapped[int | None] = Column("OverrideOrder", Integer, nullable=True)
    override_material: Mapped[bool | None] = Column("OverrideMaterial", Boolean, nullable=True)
    material: Mapped[str | None] = Column("Material", String, nullable=True)
    invert_density_loading: Mapped[bool | None] = Column("InvertDensityLoading", Boolean, nullable=True)
    volume: Mapped[float | None] = Column("Volume", Float, nullable=True)
    pixel_min: Mapped[float | None] = Column("PixelMin", Float, nullable=True)
    pixel_max: Mapped[float | None] = Column("PixelMax", Float, nullable=True)
    pixel_mean: Mapped[float | None] = Column("PixelMean", Float, nullable=True)
    pixel_std: Mapped[float | None] = Column("PixelStd", Float, nullable=True)
    bev_drr_outline: Mapped[bool | None] = Column("bBEVDRROutline", Boolean, nullable=True)
    display_on_other_vols: Mapped[bool | None] = Column("DisplayOnOtherVolumes", Boolean, nullable=True)
    is_linked: Mapped[bool | None] = Column("IsLinked", Boolean, nullable=True)

    # Parent relationship with optimized loading
    plan_id: Mapped[int | None] = Column("PlanID", Integer, ForeignKey("Plan.ID", ondelete="CASCADE"), nullable=True)
    plan: Mapped["Plan | None"] = relationship(
        "Plan",
        back_populates="roi_list",
        lazy="joined",  # Optimize loading as plan info is frequently needed
    )

    # Child relationship
    curve_list: Mapped[list["Curve"]] = relationship(
        "Curve",
        back_populates="roi",
        cascade="all, delete-orphan",
        lazy="selectin",  # Use selectin loading for collections that are frequently accessed
    )

    def __init__(self, **kwargs: Any):
        """Initialize an ROI instance.

        Args:
            **kwargs: Keyword arguments used to initialize ROI attributes

        Relationships:
            plan (Plan): The parent Plan to which this ROI belongs (many-to-one).
            curves (list[Curve]): List of Curves associated with this ROI (one-to-many).
        """
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        """String representation of the ROI instance."""
        return f"<ROI(id={self.id}, number={self.roi_number}, name='{self.name}')>"
