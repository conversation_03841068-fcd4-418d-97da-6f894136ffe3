"""
Image converter for SimpleDicomConverter.

This module provides functionality to convert Pinnacle image files to DICOM CT Image files.
It supports both direct file-based conversion from Pinnacle archives and in-memory conversion from loaded models.

Key Classes:
    - ImageConverter: Main class for converting Pinnacle image data to DICOM CT Image.

Typical Usage:
    # Image Converter From Models
    image_converter = ImageConverter(patient)
    image_ds = image_converter.convert(image_set)
    image_converter.save_dataset(image_ds, output_path)

    # Image Converter From Archive
    image_converter = ImageConverter.from_archive(patient_path)
    image_set = image_converter.load_planning_ct(plan_path)
    image_ds = image_converter.convert(image_set)
    image_converter.save_dataset(image_ds, output_path)
"""

import argparse
import logging
import os
import sys

import pydicom
from pinnacle_io.models import ImageSet, Patient
from pinnacle_io.readers import ImageSetReader
from pydicom.dataset import FileDataset

from pinnacle_io.converters.base_converter import PlanBaseConverter
from pinnacle_io.exceptions import DataValidation<PERSON>rror, FileReadError, ImageDataError
from pinnacle_io.converters.constants import (
    CT_IMAGE_PARAMETERS,
    CT_IMAGE_SOP_CLASS_UID,
    IMAGE_TYPE_ORIGINAL_PRIMARY_AXIAL,
    MODALITY_CT,
    PHOTOMETRIC_INTERPRETATION_MONOCHROME2,
)
from pinnacle_io.converters.uid_manager import DicomUidManager

logger = logging.getLogger(__name__)


class ImageConverter(PlanBaseConverter):
    """
    Converter for CT images.

    This class handles the conversion of Pinnacle image files to DICOM CT images. Inherit
    from the PlanBaseConverter to support loading the planning CT for a plan_path.
    """

    MODALITY = MODALITY_CT

    def __init__(self, patient: Patient, uid_manager: DicomUidManager | None = None):
        """
        Initialize the image converter with a Patient model.

        Args:
            patient: Patient model object.
            uid_manager: DicomUidManager instance for UID coordination.
        """
        super().__init__(patient, uid_manager)

    @classmethod
    def from_archive(cls, patient_path: str):
        """
        Create an ImageConverter instance from a Pinnacle archive path.

        Args:
            patient_path: Full path to the patient folder.

        Returns:
            ImageConverter instance initialized with patient data.
        """
        return super().from_archive(patient_path)

    def convert_from_models(self, image_set: ImageSet) -> list[FileDataset]:
        """
        Convert ImageSet model directly to DICOM datasets.

        This method provides a clean interface for model-based conversion,
        leveraging the existing convert method.

        Args:
            image_set: ImageSet model containing image data and metadata.

        Returns:
            List of FileDataset objects representing the CT image series.
        """
        return self.convert(image_set)

    def convert(self, image_set: ImageSet) -> list[FileDataset]:
        """
        Convert Pinnacle image files to DICOM CT images.

        Args:
            image_set: ImageSet to convert.

        Returns:
            List of FileDataset containing the converted DICOM images.

        Raises:
            ImageDataError: If image conversion fails
            DataValidationError: If image_set is invalid
        """
        try:
            # Validate input
            self._validate_required_data(image_set, "image_set")

            if not hasattr(image_set, "id") or getattr(image_set, "id", None) is None:
                raise DataValidationError(
                    "ImageSet missing required id attribute", context={"image_set_type": type(image_set).__name__}
                )

            self._log_conversion_context("convert_images", image_set_id=image_set.id, patient_path=self.patient_path)

            if self.patient_path is None:
                raise FileReadError("Patient path is not set")

            dicom_dir = os.path.join(self.patient_path, f"ImageSet_{image_set.id}.DICOM")

            if os.path.exists(dicom_dir):
                self.logger.info("Converting existing DICOM images")
                ct_datasets = self.convert_existing_images(image_set)
            else:
                self.logger.info("Creating new DICOM images from raw data")
                ct_datasets = self.create_ct_images(image_set)

            if not ct_datasets:
                raise ImageDataError(
                    "No DICOM datasets were created during conversion", context={"image_set_id": image_set.id}
                )

            self.logger.info(f"Image conversion completed successfully - created {len(ct_datasets)} datasets")
            return ct_datasets

        except (ImageDataError, DataValidationError):
            raise
        except Exception as e:
            raise ImageDataError(
                "Unexpected error during image conversion",
                context={"image_set_id": getattr(image_set, "id", "unknown") if image_set else "None"},
                cause=e,
            )

    def convert_existing_images(self, image_set: ImageSet) -> list[FileDataset]:
        """
        Convert existing DICOM images by updating their metadata.

        Args:
            image_set: ImageSet model object containing image metadata.

        Returns:
            List of FileDataset objects with updated metadata

        Raises:
            FileReadError: If DICOM directory doesn't exist or files can't be read
            ImageDataError: If no valid DICOM files are found
        """
        if self.patient_path is None:
            raise FileReadError("Patient path is not set")

        dicom_dir = os.path.join(self.patient_path, f"ImageSet_{image_set.id}.DICOM")

        if not os.path.exists(dicom_dir):
            raise FileReadError(
                f"DICOM directory not found: {dicom_dir}",
                context={"image_set_id": image_set.id, "patient_path": self.patient_path},
            )

        ct_datasets = []
        processed_files = 0

        try:
            files = os.listdir(dicom_dir)
            dicom_files = [f for f in files if f.endswith(".dcm") or f.endswith(".img")]

            if not dicom_files:
                raise ImageDataError(
                    "No DICOM files found in directory", context={"dicom_dir": dicom_dir, "total_files": len(files)}
                )

            self.logger.debug(f"Found {len(dicom_files)} DICOM files to process")

            for file in dicom_files:
                try:
                    image_path = os.path.join(dicom_dir, file)
                    ds = pydicom.dcmread(image_path, force=True)

                    # Update patient information with validation
                    patient_name = f"{self.last_name}^{self.first_name}^{self.middle_name}"
                    ds.PatientName = self._validate_optional_data(patient_name, "patient_name", "UNKNOWN^UNKNOWN^")
                    ds.PatientID = self._validate_optional_data(self.patient_id, "patient_id", "UNKNOWN")
                    ds.PatientBirthDate = self._validate_optional_data(
                        self.patient_birth_date, "patient_birth_date", ""
                    )

                    ct_datasets.append(ds)
                    processed_files += 1

                except Exception as e:
                    self.logger.warning(f"Failed to process DICOM file {file}: {e}")
                    continue

            if not ct_datasets:
                raise ImageDataError(
                    "No DICOM files could be successfully processed",
                    context={"dicom_dir": dicom_dir, "attempted_files": len(dicom_files)},
                )

            self.logger.info(f"Successfully processed {processed_files}/{len(dicom_files)} existing DICOM images")
            return ct_datasets

        except (FileReadError, ImageDataError):
            raise
        except Exception as e:
            raise FileReadError(
                "Failed to convert existing DICOM images",
                context={"dicom_dir": dicom_dir, "processed_files": processed_files},
                cause=e,
            )

    def create_ct_images(self, image_set: ImageSet) -> list[FileDataset]:
        """
        Create a series of DICOM CT image datasets from an ImageSet.

        This consolidated method handles both the creation of individual CT slices and
        the generation of a complete series of images from raw pixel data.

        Args:
            image_set: ImageSet model object containing image metadata.

        Returns:
            List of FileDataset objects representing the CT image series.

        Raises:
            ImageDataError: If image creation fails or data is invalid
            FileReadError: If image file cannot be read
        """
        try:
            if self.patient_path is None:
                raise FileReadError("Patient path is not set")

            image_file = os.path.join(self.patient_path, f"ImageSet_{image_set.id}.img")
            ct_datasets: list[FileDataset] = []

            # Read the full image data if needed
            if image_set.pixel_data is None:
                self.logger.debug(f"Loading pixel data from {image_file}")
                try:
                    image_set = ImageSetReader.read_from_path(image_file)
                except Exception as e:
                    raise FileReadError(
                        "Failed to read image data from file",
                        context={"image_file": image_file, "image_set_id": image_set.id},
                        cause=e,
                    )

            # Validate that image_set was loaded successfully
            if not hasattr(image_set, "image_info_list") or not image_set.image_info_list:
                raise ImageDataError(
                    "Image set has no image info list",
                    context={"image_file": image_file, "image_set_id": image_set.id},
                )

            # Extract UIDs from ImageSet to preserve original Pinnacle data
            original_study_uid = None
            original_series_uid = None
            original_frame_uid = None
            
            if hasattr(image_set, 'image_info_list') and image_set.image_info_list:
                first_image_info = image_set.image_info_list[0]
                original_study_uid = getattr(first_image_info, 'study_instance_uid', None)
                original_frame_uid = getattr(first_image_info, 'frame_uid', None)
                
            if hasattr(image_set, 'series_uid'):
                original_series_uid = getattr(image_set, 'series_uid', None)
                
            self.logger.debug(f"Creating {len(image_set.image_info_list)} CT image slices")

            for i, image_info in enumerate(image_set.image_info_list):
                try:
                    # Try to preserve original SOP Instance UID from Pinnacle data
                    original_instance_uid = getattr(image_info, "instance_uid", None)
                    if original_instance_uid:
                        instance_uid = original_instance_uid
                        self.logger.debug(f"Preserving original SOP Instance UID for slice {i}: {instance_uid}")
                    else:
                        # Generate new UID via UID manager if original not available
                        instance_uid = self.uid_manager.get_sop_instance_uid("CT", i)
                        self.logger.debug(f"Generated new SOP Instance UID for slice {i}: {instance_uid}")

                    # Create a dataset for this slice
                    file_meta = self.create_file_meta(CT_IMAGE_SOP_CLASS_UID, instance_uid)
                    ds = self.create_dataset(file_meta)

                    # Set common elements
                    self.set_common_elements(ds)
                    
                    # Override with original Pinnacle UIDs if available
                    if original_study_uid:
                        ds.StudyInstanceUID = original_study_uid
                        self.logger.debug(f"Preserved original Study Instance UID: {original_study_uid}")
                    
                    if original_series_uid:
                        ds.SeriesInstanceUID = original_series_uid
                        self.logger.debug(f"Preserved original Series Instance UID: {original_series_uid}")
                        
                    if original_frame_uid:
                        ds.FrameOfReferenceUID = original_frame_uid
                        self.logger.debug(f"Preserved original Frame of Reference UID: {original_frame_uid}")

                    # Set CT-specific elements with validation
                    ds.Modality = MODALITY_CT
                    ds.ImageType = IMAGE_TYPE_ORIGINAL_PRIMARY_AXIAL
                    ds.InstanceNumber = self._validate_optional_data(
                        getattr(image_info, "slice_number", None), f"slice_number_{i}", i + 1
                    )

                    couch_pos = self._validate_optional_data(
                        getattr(image_info, "couch_pos", None), f"couch_pos_{i}", 0.0
                    )
                    ds.SliceLocation = couch_pos

                    # Calculate image position with validation
                    x_pixdim = self._validate_optional_data(getattr(image_set, "x_pixdim", None), "x_pixdim", 1.0)
                    y_pixdim = self._validate_optional_data(getattr(image_set, "y_pixdim", None), "y_pixdim", 1.0)
                    x_dim = self._validate_optional_data(getattr(image_set, "x_dim", None), "x_dim", 256)
                    y_dim = self._validate_optional_data(getattr(image_set, "y_dim", None), "y_dim", 256)

                    ds.ImagePositionPatient = [
                        -x_pixdim * x_dim / 2,
                        -y_pixdim * y_dim / 2,
                        couch_pos,
                    ]

                    # Set patient position and orientation with graceful degradation
                    patient_position = self._validate_optional_data(
                        getattr(image_set, "patient_position", None), "patient_position", "HFS"
                    )

                    if "HFS" in patient_position or "FFS" in patient_position:
                        ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
                    elif "HFP" in patient_position or "FFP" in patient_position:
                        ds.ImageOrientationPatient = [-1.0, 0.0, 0.0, 0.0, -1.0, 0.0]
                    else:
                        # Default orientation if position is unknown
                        ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
                        if patient_position != "HFS":  # Only warn if not default
                            self.logger.warning(
                                f"Unknown patient position '{patient_position}', using default orientation"
                            )

                    # Set pixel-related elements
                    ds.SamplesPerPixel = 1
                    ds.PhotometricInterpretation = PHOTOMETRIC_INTERPRETATION_MONOCHROME2
                    ds.Rows = x_dim
                    ds.Columns = y_dim
                    ds.PixelSpacing = [x_pixdim, y_pixdim]
                    ds.BitsAllocated = CT_IMAGE_PARAMETERS.get("bits_allocated", 16)
                    ds.BitsStored = CT_IMAGE_PARAMETERS.get("bits_stored", 16)
                    ds.HighBit = CT_IMAGE_PARAMETERS.get("high_bit", 15)
                    ds.PixelRepresentation = CT_IMAGE_PARAMETERS.get("pixel_representation", 1)
                    ds.RescaleIntercept = CT_IMAGE_PARAMETERS.get("rescale_intercept", -1024)
                    ds.RescaleSlope = CT_IMAGE_PARAMETERS.get("rescale_slope", 1.0)

                    z_pixdim = self._validate_optional_data(getattr(image_set, "z_pixdim", None), "z_pixdim", 1.0)
                    ds.SliceThickness = z_pixdim

                    # Set pixel data if available
                    if (
                        hasattr(image_set, "pixel_data")
                        and image_set.pixel_data is not None
                        and len(image_set.pixel_data) > i
                    ):
                        ds.PixelData = image_set.pixel_data[i].tobytes()
                    else:
                        self.logger.debug(f"No pixel data available for slice {i}")

                    # Add the dataset to our collection
                    ct_datasets.append(ds)

                except Exception as e:
                    self.logger.warning(f"Failed to create CT dataset for slice {i}: {e}")
                    continue

            if not ct_datasets:
                raise ImageDataError(
                    "No CT image datasets could be created",
                    context={"image_set_id": image_set.id, "attempted_slices": len(image_set.image_info_list)},
                )

            self.logger.info(f"Successfully created {len(ct_datasets)} CT image slices")
            return ct_datasets

        except (ImageDataError, FileReadError):
            raise
        except Exception as e:
            raise ImageDataError(
                "Unexpected error creating CT images",
                context={"image_set_id": getattr(image_set, "id", "unknown") if image_set else "None"},
                cause=e,
            )


def main(patient_path: str, plan_path: str, image_set_index: int, output_path: str) -> None:
    """
    Main function to run the StructureConverter as a standalone script.

    Args:
        patient_path (str): Full path to the patient folder containing the image files.
        plan_path (str): Full path to the plan folder containing the structure files.
        image_set_index (int): Index of the image set to load for conversion, starting at 0.
        output_path (str): Directory where output DICOM files will be saved.

    Raises:
        SystemExit: If an error occurs during conversion.
    """
    # Initialize the image converter
    if patient_path == "" and plan_path == "":
        raise ValueError("Either the patient path or the plan path must be provided")
    elif patient_path == "":
        patient_path = os.path.dirname(plan_path)

    converter = ImageConverter.from_archive(patient_path)

    # Load the image set header info
    image_set = None
    if plan_path == "" and image_set_index < 0:
        raise ValueError("Either the plan path or the image set index must be provided")
    elif plan_path == "":
        # Get the CT with the given index
        image_set = converter.load_image_set(image_set_index)
    elif image_set_index < 0:
        # Get the planning CT
        image_set = converter.load_planning_ct(plan_path)
    else:
        raise ValueError("Either the plan path or the image set index must be provided (not both)")

    if not image_set:
        raise ValueError("No image set found for conversion")

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logger = logging.getLogger(__name__)
    logger.info(f"Starting structure conversion for primary image set {image_set.id} in {patient_path}")

    try:
        ct_datasets = converter.convert(image_set)
        for ct_dataset in ct_datasets:
            output_file = converter.save_dataset(ct_dataset, output_path)
            logger.info(f"CT conversion completed. Output file: {output_file}")
    except Exception as e:
        logger.error(f"Error during CT conversion: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    # Create argument parser
    parser = argparse.ArgumentParser(description="Convert Pinnacle CT images to DICOM CT files")
    # Add required arguments
    parser.add_argument(
        "--patient-path",
        "-p",
        required=False,
        default="",
        type=str,
        help="Path to the patient folder",
    )
    parser.add_argument(
        "--plan-path",
        "-f",
        required=False,
        default="",
        type=str,
        help="Path to the plan folder",
    )
    parser.add_argument(
        "--image-set-index",
        "-i",
        required=False,
        type=int,
        default=-1,
        help="Index of the image set to load for conversion, starting at 0",
    )
    parser.add_argument(
        "--output-path",
        "-o",
        required=True,
        type=str,
        help="Path where output DICOM files will be saved",
    )
    # Parse arguments
    args = parser.parse_args()
    # Call main function with parsed arguments
    main(
        patient_path=args.patient_path,
        plan_path=args.plan_path,
        image_set_index=args.image_set_index,
        output_path=args.output_path,
    )
