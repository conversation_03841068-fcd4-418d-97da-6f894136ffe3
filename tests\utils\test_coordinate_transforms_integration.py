"""
Integration tests for coordinate transformations using real test data.

This module tests the coordinate transformation functions with actual
Pinnacle data from the test_data directory.
"""

import pytest
import numpy as np
from pathlib import Path

from pinnacle_io.utils.coordinate_transforms import (
    transform_coordinates_for_rois,
    transform_dose,
    needs_coordinate_transform,
)
from pinnacle_io import PinnacleReader


class TestCoordinateTransformsIntegration:
    """Integration tests using real Pinnacle test data."""

    @pytest.fixture
    def test_data_path(self):
        """Get the path to test data directory."""
        current_dir = Path(__file__).parent
        test_data_path = current_dir.parent / "test_data" / "01"
        return test_data_path

    @pytest.fixture
    def pinnacle_reader(self, test_data_path):
        """Create a PinnacleReader for the test data."""
        if not test_data_path.exists():
            pytest.skip(f"Test data not found at {test_data_path}")
        return PinnacleReader(str(test_data_path))

    def test_load_image_set_patient_position(self, pinnacle_reader):
        """Test loading ImageSet and checking patient position."""
        try:
            image_set = pinnacle_reader.get_image_set(institution=1, patient=1, image_set=0)
            assert image_set is not None
            assert hasattr(image_set, 'patient_position')

            # Patient position should be one of the supported values
            if image_set.patient_position:
                assert image_set.patient_position in ["HFS", "HFP", "FFS", "FFP"]
                print(f"Test data patient position: {image_set.patient_position}")
            else:
                pytest.skip("Test data does not contain patient position information")
        except Exception as e:
            pytest.skip(f"Could not load image set from test data: {e}")

    def test_transform_rois_with_real_data(self, pinnacle_reader):
        """Test ROI coordinate transformation with real data."""
        try:
            # Load ROI data
            rois = pinnacle_reader.get_rois(institution=1, patient=1, plan=1)
            if not rois:
                pytest.skip("No ROI data found in test data")

            # Load image set to get patient position
            image_set = pinnacle_reader.get_image_set(institution=1, patient=1, image_set=0)
            if not image_set or not image_set.patient_position:
                pytest.skip("No patient position information in test data")

            patient_position = image_set.patient_position
            print(f"Testing ROI transformation for patient position: {patient_position}")

            # Count original points
            original_point_count = 0
            for roi in rois:
                for curve in roi.curve_list:
                    if curve.points is not None:
                        original_point_count += len(curve.points)

            # Transform the ROIs
            transformed_rois = transform_coordinates_for_rois(rois, patient_position)

            # Verify basic properties
            assert len(transformed_rois) == len(rois)

            # Count transformed points (should be same number)
            transformed_point_count = 0
            for roi in transformed_rois:
                for curve in roi.curve_list:
                    if curve.points is not None:
                        transformed_point_count += len(curve.points)

            assert transformed_point_count == original_point_count

            # Verify transformation was applied correctly
            if needs_coordinate_transform(patient_position):
                # For positions that need transformation, check that some coordinates changed
                coordinates_changed = False
                for orig_roi, trans_roi in zip(rois, transformed_rois):
                    for orig_curve, trans_curve in zip(orig_roi.curve_list, trans_roi.curve_list):
                        if orig_curve.points is not None and trans_curve.points is not None:
                            if not np.array_equal(orig_curve.points, trans_curve.points):
                                coordinates_changed = True
                                break
                    if coordinates_changed:
                        break

                assert coordinates_changed, f"Expected coordinates to change for position {patient_position}"
            else:
                # For FFS, coordinates should remain the same
                for orig_roi, trans_roi in zip(rois, transformed_rois):
                    for orig_curve, trans_curve in zip(orig_roi.curve_list, trans_roi.curve_list):
                        if orig_curve.points is not None and trans_curve.points is not None:
                            np.testing.assert_array_equal(orig_curve.points, trans_curve.points)

            print(f"Successfully transformed {len(rois)} ROIs with {original_point_count} total points")

        except Exception as e:
            pytest.skip(f"Could not test ROI transformation with real data: {e}")

    def test_transform_dose_with_real_data(self, pinnacle_reader):
        """Test dose coordinate transformation with real data."""
        try:
            # Load trial and dose data
            trials = pinnacle_reader.get_trials(institution=1, patient=1, plan=1)
            if not trials:
                pytest.skip("No trial data found in test data")

            # Try to get dose for the first trial
            dose = None
            for trial in trials:
                try:
                    dose = pinnacle_reader.get_dose(institution=1, patient=1, plan=1, trial=trial)
                    if dose:
                        break
                except Exception:
                    continue

            if not dose:
                pytest.skip("No dose data found in test data")

            # Load image set to get patient position
            image_set = pinnacle_reader.get_image_set(institution=1, patient=1, image_set=0)
            if not image_set or not image_set.patient_position:
                pytest.skip("No patient position information in test data")

            patient_position = image_set.patient_position
            print(f"Testing dose transformation for patient position: {patient_position}")

            # Verify dose has required components
            if not dose.dose_grid:
                pytest.skip("Dose does not have dose grid information")

            # Store original values
            original_origin_x = dose.dose_grid.origin.x
            original_origin_y = dose.dose_grid.origin.y
            original_origin_z = dose.dose_grid.origin.z

            # Transform the dose
            transformed_dose = transform_dose(dose, patient_position)

            # Verify dose grid was transformed
            assert transformed_dose.dose_grid is not None
            assert transformed_dose.dose_grid.origin is not None

            # Verify ImagePositionPatient was calculated
            assert hasattr(transformed_dose, '_image_position_patient')
            assert len(transformed_dose._image_position_patient) == 3

            # Verify original dose was not modified
            assert dose.dose_grid.origin.x == original_origin_x
            assert dose.dose_grid.origin.y == original_origin_y
            assert dose.dose_grid.origin.z == original_origin_z

            # Check transformation logic based on patient position
            if patient_position in ("HFP", "FFS"):
                assert transformed_dose.dose_grid.origin.x == -original_origin_x
            else:  # HFS, FFP
                assert transformed_dose.dose_grid.origin.x == original_origin_x

            if patient_position in ("HFS", "FFS"):
                assert transformed_dose.dose_grid.origin.y == -original_origin_y
            else:  # HFP, FFP
                assert transformed_dose.dose_grid.origin.y == original_origin_y

            if patient_position in ("HFS", "HFP"):
                assert transformed_dose.dose_grid.origin.z == -original_origin_z
            else:  # FFS, FFP
                assert transformed_dose.dose_grid.origin.z == original_origin_z

            print(f"Successfully transformed dose with origin ({original_origin_x}, {original_origin_y}, {original_origin_z})")
            print(f"Transformed origin: ({transformed_dose.dose_grid.origin.x}, {transformed_dose.dose_grid.origin.y}, {transformed_dose.dose_grid.origin.z})")
            print(f"ImagePositionPatient: {transformed_dose._image_position_patient}")

        except Exception as e:
            pytest.skip(f"Could not test dose transformation with real data: {e}")

    def test_coordinate_transformation_consistency(self, pinnacle_reader):
        """Test that coordinate transformations are consistent across data types."""
        try:
            # Load all data
            image_set = pinnacle_reader.get_image_set(institution=1, patient=1, image_set=0)
            rois = pinnacle_reader.get_rois(institution=1, patient=1, plan=1)
            trials = pinnacle_reader.get_trials(institution=1, patient=1, plan=1)

            if not image_set or not image_set.patient_position:
                pytest.skip("No patient position information in test data")

            patient_position = image_set.patient_position

            # Test that transformation functions work consistently
            transform_needed = needs_coordinate_transform(patient_position)

            print(f"Patient position: {patient_position}")
            print(f"Transformation needed: {transform_needed}")

            # If ROI data exists, test transformation
            if rois:
                transformed_rois = transform_coordinates_for_rois(rois, patient_position)
                assert len(transformed_rois) == len(rois)
                print(f"ROI transformation successful for {len(rois)} ROIs")

            # If dose data exists, test transformation
            dose = None
            if trials:
                for trial in trials:
                    try:
                        dose = pinnacle_reader.get_dose(institution=1, patient=1, plan=1, trial=trial)
                        if dose:
                            break
                    except Exception:
                        continue

            if dose:
                transformed_dose = transform_dose(dose, patient_position)
                assert transformed_dose is not None
                print("Dose transformation successful")

            print("All coordinate transformations completed successfully")

        except Exception as e:
            pytest.skip(f"Could not test coordinate transformation consistency: {e}")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])