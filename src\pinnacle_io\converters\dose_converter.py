"""
Dose converter for SimpleDicomConverter.

This module provides functionality to convert Pinnacle dose files to DICOM RT Dose files (RTDOSE).
It contains the DoseConverter class, which supports several workflows:
    1. From a Pinnacle archive (from_archive)
    2. From a pre-loaded trial (from_trial)
    3. From pre-loaded patient, trial, and dose data (from_dose)

The converter handles all necessary transformations based on patient position and
creates standardized DICOM RT Dose objects following the DICOM standard.

Typical Usage:
    # Dose Converter From Models
    dose_converter = DoseConverter(patient)
    dose_ds = dose_converter.convert(trial, dose)
    dose_converter.save_dataset(dose_ds, output_path)

    # Dose Converter From Archive
    dose_converter = DoseConverter.from_archive(patient_path)
    trial_list = dose_converter.load_trials(plan_path)
    trial = trial_list[trial_index]
    dose = dose_converter.load_dose(plan_path, trial)
    dose_ds = dose_converter.convert(trial, dose)
    dose_converter.save_dataset(dose_ds, output_path)
"""

import logging
from typing import Optional

import numpy as np
from pinnacle_io.models import Dose, ImageSet, Patient, Trial
from pinnacle_io.readers import DoseReader
from pinnacle_io.utils.patient_enum import PatientSetupEnum
from pydicom.dataset import Dataset, FileDataset
from pydicom.sequence import Sequence
from pydicom.uid import RTDoseStorage, RTPlanStorage, generate_uid

from pinnacle_io.converters.base_converter import TrialBaseConverter
from pinnacle_io.exceptions import CoordinateTransformError, DataValidationError, DoseDataError, FileReadError
from pinnacle_io.converters.constants import (
    MODALITY_RTDOSE,
    PHOTOMETRIC_INTERPRETATION_MONOCHROME2,
)
from pinnacle_io.converters.uid_manager import DicomUidManager

logger = logging.getLogger(__name__)


class DoseConverter(TrialBaseConverter):
    """
    Converter for Pinnacle RT Dose (RTDOSE) DICOM objects.

    This class provides methods to read Pinnacle dose files, apply patient position-specific
    transformations, and generate a DICOM-compliant RT Dose dataset. It supports both archive-based
    and in-memory workflows, and can be used as a command-line tool or as a Python class.
    """

    MODALITY = MODALITY_RTDOSE

    def __init__(self, patient: Patient, uid_manager: Optional[DicomUidManager] = None) -> None:
        """
        Initialize the DoseConverter.

        Args:
            patient (Patient): Patient model object.
            uid_manager: DicomUidManager instance for UID coordination.
        """
        super().__init__(patient, uid_manager)

    @classmethod
    def from_archive(cls, patient_path: str) -> "DoseConverter":
        """
        Create a DoseConverter instance from a Pinnacle archive.

        Args:
            patient_path (str): Full path to the patient folder in the Pinnacle archive.

        Returns:
            DoseConverter: Instance of DoseConverter.
        """
        return super().from_archive(patient_path)

    def load_dose(self, plan_path: str, trial: Trial) -> Dose:
        """
        Load the dose data from the plan files for the current trial.

        Args:
            plan_path (str): Full path to the plan folder containing the trial files.
            trial (Trial): Trial model object associated with the plan.

        Returns:
            Dose: Dose model object containing the loaded dose data.

        Raises:
            FileReadError: If plan path is invalid or dose loading fails
            DataValidationError: If trial data is invalid
        """
        try:
            self._validate_required_data(plan_path, "plan_path")
            self._validate_required_data(trial, "trial")

            self._log_conversion_context("load_dose", plan_path=plan_path, trial_name=getattr(trial, "name", "unknown"))

            dose = DoseReader.read(plan_path, trial)

            if not dose:
                raise DoseDataError(
                    "DoseReader returned None - no dose data found",
                    context={"plan_path": plan_path, "trial_name": getattr(trial, "name", "unknown")},
                )

            self.logger.debug("Successfully loaded dose data")
            return dose

        except (FileReadError, DataValidationError, DoseDataError):
            raise
        except Exception as e:
            raise FileReadError(
                "Failed to load dose data from plan",
                context={"plan_path": plan_path, "trial_name": getattr(trial, "name", "unknown")},
                cause=e,
            )

    def convert_from_models(self, trial: Trial, planning_ct: Optional[ImageSet] = None) -> FileDataset:
        """
        Convert Dose model to DICOM RT Dose dataset.

        This method provides a clean interface for model-based conversion,
        leveraging the existing convert method.

        Args:
            trial: Trial model containing treatment plan information.
            planning_ct: Optional ImageSet model for dose reference. When provided, UIDs will be preserved for spatial alignment.

        Returns:
            FileDataset containing the converted DICOM RT Dose.
        """
        return self.convert_dose(trial, planning_ct)

    def convert_dose(self, trial: Trial, planning_ct: Optional[ImageSet] = None) -> FileDataset:
        """
        Convert Pinnacle dose files to a DICOM RT Dose dataset.

        Args:
            trial (Trial): The Trial model object containing dose grid and geometry information.
            planning_ct (Optional[ImageSet]): The ImageSet model for spatial alignment. When provided, UIDs will be preserved.

        Returns:
            FileDataset: DICOM RT Dose dataset for the current trial.

        Raises:
            DoseDataError: If dose data is missing or invalid
            DataValidationError: If trial data is missing required components
            CoordinateTransformError: If coordinate transformation fails
        """
        try:
            # Validate input data
            dose = trial.dose
            self._validate_dose_conversion_inputs(trial, dose)

            self._log_conversion_context(
                "convert_dose",
                trial_name=getattr(trial, "name", "unknown"),
                has_pixel_data=dose.pixel_data is not None if dose else False,
            )

            # Create base DICOM dataset for RT dose
            plan_dose_ds = self.create_dose_dataset(trial, planning_ct)

            # Validate and process dose pixel data
            pixel_data = self._validate_and_extract_pixel_data(dose)
            pixel_array = pixel_data.copy()

            # Transform dose data based on patient position
            try:
                pixel_array = self.transform_dose_by_patient_position(pixel_array, trial)  # type: ignore
            except Exception as e:
                raise CoordinateTransformError(
                    "Failed to transform dose data based on patient position",
                    context={
                        "trial_name": getattr(trial, "name", "unknown"),
                        "patient_position": getattr(trial, "patient_position", "unknown"),
                    },
                    cause=e,
                )

            # Scale dose values to fit in 16-bit range with validation
            max_dose = np.max(pixel_array)
            if max_dose > 0:
                scale = max_dose / 65530.0  # Leave some margin below 65535
                plan_dose_ds.DoseGridScaling = scale
                pixel_array = np.round(pixel_array / scale).astype(np.uint32)
                self.logger.debug(f"Scaled dose data: max_dose={max_dose:.2f}, scale={scale:.6f}")
            else:
                self.logger.warning("Dose data contains no positive values, using scale=1")
                plan_dose_ds.DoseGridScaling = 1

            # Set pixel data and dose type
            plan_dose_ds.PixelData = pixel_array.tobytes()
            plan_dose_ds.DoseSummationType = "PLAN"

            self.logger.info("Dose conversion completed successfully")
            return plan_dose_ds

        except (DoseDataError, DataValidationError, CoordinateTransformError):
            raise
        except Exception as e:
            raise DoseDataError(
                "Unexpected error during dose conversion",
                context={"trial_name": getattr(trial, "name", "unknown") if trial else "None"},
                cause=e,
            )

    def _validate_dose_conversion_inputs(self, trial: Trial, dose: Dose) -> None:
        """
        Validate inputs for dose conversion.

        Args:
            trial: Trial object to validate
            dose: Dose object to validate

        Raises:
            DataValidationError: If required data is missing
        """
        self._validate_required_data(trial, "trial")
        self._validate_required_data(dose, "dose")

        # Check for dose grid in trial
        if not hasattr(trial, "dose_grid") or trial.dose_grid is None:
            raise DataValidationError(
                "Trial missing required dose_grid", context={"trial_name": getattr(trial, "name", "unknown")}
            )

        # Validate dose grid components
        dose_grid = trial.dose_grid
        if not hasattr(dose_grid, "dimension") or dose_grid.dimension is None:
            raise DataValidationError(
                "Dose grid missing required dimension information",
                context={"trial_name": getattr(trial, "name", "unknown")},
            )

        if not hasattr(dose_grid, "voxel_size") or dose_grid.voxel_size is None:
            raise DataValidationError(
                "Dose grid missing required voxel_size information",
                context={"trial_name": getattr(trial, "name", "unknown")},
            )

        if not hasattr(dose_grid, "origin") or dose_grid.origin is None:
            raise DataValidationError(
                "Dose grid missing required origin information",
                context={"trial_name": getattr(trial, "name", "unknown")},
            )

    def _validate_and_extract_pixel_data(self, dose: Dose) -> np.ndarray:
        """
        Validate and extract pixel data from dose object.

        Args:
            dose: Dose object containing pixel data

        Returns:
            Validated pixel data array

        Raises:
            DoseDataError: If pixel data is missing or invalid
        """
        if not hasattr(dose, "pixel_data") or dose.pixel_data is None:
            raise DoseDataError("Dose object missing pixel_data attribute", context={"dose_type": type(dose).__name__})

        pixel_data = dose.pixel_data

        if pixel_data.size == 0:
            raise DoseDataError(
                "Dose pixel data is empty",
                context={"pixel_data_shape": pixel_data.shape if hasattr(pixel_data, "shape") else "unknown"},
            )

        self.logger.debug(f"Validated pixel data: shape={pixel_data.shape}, dtype={pixel_data.dtype}")
        return np.asarray(pixel_data, dtype=pixel_data.dtype)

    @staticmethod
    def transform_dose_by_patient_position(
        pixel_array: np.ndarray,  # Expected to be float32
        trial: Trial,
    ) -> np.ndarray:  # Returns float32
        """
        Apply patient-position-specific transformations to a 1D dose pixel array.

        Args:
            pixel_array (np.ndarray): The 1D dose pixel array to transform.
            trial (Trial): The Trial model object containing dose grid and patient position info.

        Returns:
            np.ndarray: The transformed 1D dose pixel array.

        Raises:
            DataValidationError: If trial.dose_grid is missing or invalid.
        """
        result = pixel_array.copy()
        if not trial.dose_grid:
            raise DataValidationError("No dose grid information found in trial")

        # Reshape array to 3D using dose grid dimensions
        dimension = trial.dose_grid.dimension
        if not dimension:
            raise DataValidationError("No dose grid dimension found in trial")
        if not dimension.x or not dimension.y or not dimension.z:
            raise DataValidationError("No dose grid dimensions found in trial")
        shape = tuple(int(i) for i in (dimension.z, dimension.y, dimension.x))
        result = result.reshape(shape)

        # Get patient position or default to Unknown
        patient_position = getattr(trial, "patient_position", PatientSetupEnum.Unknown)

        # Apply transformations for standard patient positions
        if patient_position in [
            PatientSetupEnum.HFS,
            PatientSetupEnum.HFP,
            PatientSetupEnum.FFS,
            PatientSetupEnum.FFP,
        ]:
            # Flip array slices and flatten
            flat_array: list[float] = []
            for z in range(result.shape[0]):
                slice_data = result[z, :, :].flatten()
                flat_array.extend(np.flip(slice_data))
            result = np.array(flat_array)
            result = np.flip(result)
        return result

    def set_dose_position_by_patient_position(self, ds: Dataset, trial: Trial) -> None:
        """
        Set the dose grid's position and orientation in the DICOM dataset based on patient position.
        This ensures correct spatial alignment of the dose with the patient anatomy in DICOM viewers.

        Args:
            ds (Dataset): The DICOM dataset to update.
            trial (Trial): The Trial model object containing dose grid and patient position info.

        Raises:
            DataValidationError: If trial.dose_grid is missing.
        """
        if not trial.dose_grid:
            raise DataValidationError("No dose grid information found in trial")

        # Calculate dose grid shifts
        voxel_size = trial.dose_grid.voxel_size
        dimension = trial.dose_grid.dimension
        if not voxel_size:
            raise DataValidationError("No voxel size found in trial")

        if not dimension:
            raise DataValidationError("No dimension found in trial")

        if voxel_size.x is None or voxel_size.y is None or voxel_size.z is None:
            raise DataValidationError("No voxel size found in trial")

        if dimension.x is None or dimension.y is None or dimension.z is None:
            raise DataValidationError("No dimension found in trial")

        y_dose_shift = float(voxel_size.y) * float(dimension.y)
        z_dose_shift = float(voxel_size.z) * float(dimension.z)

        # Get patient position or default to Unknown
        patient_position = getattr(trial, "patient_position", PatientSetupEnum.Unknown)

        # Get the dose grid origin
        origin = trial.dose_grid.origin
        if not origin:
            raise DataValidationError("No dose grid origin found in trial")

        if origin.x is None or origin.y is None or origin.z is None:
            raise DataValidationError("No dose grid origin found in trial")

        # Set position and orientation based on patient setup
        if patient_position == PatientSetupEnum.HFS:
            ds.ImagePositionPatient = [
                float(origin.x),
                float(origin.y) - y_dose_shift,
                float(origin.z) - z_dose_shift,
            ]
            ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        elif patient_position == PatientSetupEnum.HFP:
            ds.ImagePositionPatient = [
                float(origin.x),
                float(origin.y) + y_dose_shift,
                float(origin.z) - z_dose_shift,
            ]
            ds.ImageOrientationPatient = [-1, 0, 0, 0, -1, 0]
        elif patient_position == PatientSetupEnum.FFS:
            ds.ImagePositionPatient = [
                float(origin.x),
                float(origin.y) - y_dose_shift,
                float(origin.z) + z_dose_shift,
            ]
            ds.ImageOrientationPatient = [-1, 0, 0, 0, 1, 0]
        elif patient_position == PatientSetupEnum.FFP:
            ds.ImagePositionPatient = [
                float(origin.x),
                float(origin.y) + y_dose_shift,
                float(origin.z) + z_dose_shift,
            ]
            ds.ImageOrientationPatient = [1, 0, 0, 0, -1, 0]
        else:
            # Default position for unknown setup
            ds.ImagePositionPatient = [
                float(origin.x),
                float(origin.y) - y_dose_shift,
                float(origin.z) - z_dose_shift,
            ]
            ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]

        # Set frame of reference
        ds.FrameOfReferenceUID = self.frame_of_reference_uid
        ds.PositionReferenceIndicator = ""

    def create_dose_dataset(self, trial: Trial, planning_ct: Optional[ImageSet] = None) -> FileDataset:
        """
        Create a new DICOM FileDataset for an RT Dose (RTDOSE).

        Args:
            trial (Trial): The Trial model object containing dose grid and geometry information.
            planning_ct (Optional[ImageSet]): The ImageSet model for spatial alignment.

        Returns:
            FileDataset: DICOM dataset for the RT Dose.

        Raises:
            DataValidationError: If trial.dose_grid is missing.
        """
        try:
            # Generate UIDs for the dataset
            sop_instance_uid = generate_uid()
            dose_series_uid = generate_uid()

            # Check for valid dose grid information
            dose_grid = trial.dose_grid
            if not dose_grid:
                raise DataValidationError("No dose grid information found in trial")
            voxel_size = dose_grid.voxel_size
            if not voxel_size:
                raise DataValidationError("No voxel size found in dose grid")
            dimension = dose_grid.dimension
            if not dimension:
                raise DataValidationError("No dimension found in dose grid")

            # Create base dataset
            file_meta = self.create_file_meta(sop_class_uid=RTDoseStorage, sop_instance_uid=sop_instance_uid)
            ds = self.create_dataset(file_meta)
            self.set_common_elements(ds)
            
            # Override with original Pinnacle UIDs if planning_ct is available for spatial alignment
            if planning_ct:
                original_study_uid = None
                original_frame_uid = None
                
                if hasattr(planning_ct, 'image_info_list') and planning_ct.image_info_list:
                    first_image_info = planning_ct.image_info_list[0]
                    original_study_uid = getattr(first_image_info, 'study_instance_uid', None)
                    original_frame_uid = getattr(first_image_info, 'frame_uid', None)
                    
                if original_study_uid:
                    ds.StudyInstanceUID = original_study_uid
                    self.logger.debug(f"Preserved original Study Instance UID: {original_study_uid}")
                    
                if original_frame_uid:
                    ds.FrameOfReferenceUID = original_frame_uid
                    self.logger.debug(f"Preserved original Frame of Reference UID: {original_frame_uid}")

            # Set dose-specific DICOM tags
            ds.Modality = self.MODALITY
            ds.SeriesInstanceUID = dose_series_uid
            ds.DoseUnits = "GY"
            ds.DoseType = "PHYSICAL"
            ds.DoseSummationType = "PLAN"

            # Set image-related tags
            ds.SamplesPerPixel = 1
            ds.PhotometricInterpretation = PHOTOMETRIC_INTERPRETATION_MONOCHROME2
            ds.NumberOfFrames = dimension.z
            ds.Rows = dimension.y
            ds.Columns = dimension.x
            ds.PixelSpacing = [voxel_size.x, voxel_size.y]

            # Set bit depth information
            ds.BitsAllocated = 32
            ds.BitsStored = 32
            ds.HighBit = 31
            ds.PixelRepresentation = 0

            # Set position and frame information
            if voxel_size.x is None or voxel_size.y is None or voxel_size.z is None:
                raise DataValidationError("No voxel size found in trial")

            if dimension.x is None or dimension.y is None or dimension.z is None:
                raise DataValidationError("No dimension found in trial")

            self.set_dose_position_by_patient_position(ds, trial)
            frame_offset_vector = [int(i * voxel_size.z) for i in range(int(dimension.z))]
            ds.GridFrameOffsetVector = frame_offset_vector
            offset_vector = ds.data_element("GridFrameOffsetVector")
            if offset_vector:
                ds.FrameIncrementPointer = offset_vector.tag

            # Add plan reference if available
            plan_sop_instance_uid = getattr(trial, "plan_sop_instance_uid", "")
            if plan_sop_instance_uid:
                ds.ReferencedRTPlanSequence = Sequence()
                ref_plan = Dataset()
                ds.ReferencedRTPlanSequence.append(ref_plan)
                ref_plan.ReferencedSOPClassUID = RTPlanStorage
                ref_plan.ReferencedSOPInstanceUID = plan_sop_instance_uid
                ref_plan.ReferencedFractionGroupSequence = Sequence()
                ref_fraction_group = Dataset()
                ref_plan.ReferencedFractionGroupSequence.append(ref_fraction_group)
                ref_fraction_group.ReferencedFractionGroupNumber = "1"

            ds.TissueHeterogeneityCorrection = "IMAGE"
            self.logger.debug("Created dose dataset with proper DICOM tags")
            return ds

        except DataValidationError:
            raise
        except Exception as e:
            raise DoseDataError(
                "Failed to create dose dataset", context={"trial_name": getattr(trial, "name", "unknown")}, cause=e
            )


def main(plan_path: str, trial_index: int, output_path: str) -> None:
    """
    Main function to run the DoseConverter as a standalone script.

    Args:
        plan_path (str): Name of the plan folder (optional for pre-loaded workflow).
        trial_index (int): Trial index for conversion, starting at 0 (optional for pre-loaded workflow).
        output_path (str): Directory where output DICOM files will be saved.
    """
    import os

    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logger = logging.getLogger(__name__)
    logger.info(f"Starting dose conversion for trial {trial_index} in {plan_path}")

    try:
        # Create converter and load data
        patient_path = os.path.dirname(plan_path)
        dose_converter = DoseConverter.from_archive(patient_path)
        trial_list = dose_converter.load_trials(plan_path)
        trial = trial_list[trial_index]
        dose = dose_converter.load_dose(plan_path, trial)

        # Convert and save dose
        dose_ds = dose_converter.convert_dose(trial, dose)
        output_file = dose_converter.save_dataset(dose_ds, output_path)
        logger.info(f"Dose conversion completed. Output file: {output_file}")
    except Exception as e:
        logger.error(f"Error during dose conversion: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    import argparse
    import sys

    # Create argument parser
    parser = argparse.ArgumentParser(description="Convert Pinnacle dose files to DICOM RT Dose files")

    # Add required arguments
    parser.add_argument("--plan-path", "-f", required=True, help="Path to the plan folder")
    parser.add_argument("--trial-index", "-n", type=int, default=0, help="Trial index (default: 0)")
    parser.add_argument(
        "--output-path",
        "-o",
        required=True,
        help="Path where output DICOM dataset will be saved",
    )

    # Parse arguments
    args = parser.parse_args()

    # Call main function with parsed arguments
    main(
        plan_path=args.plan_path,
        trial_index=args.trial_index,
        output_path=args.output_path,
    )
