"""
Unit tests for in-place coordinate transformation utilities.

This module specifically tests the in-place operations (inplace=True) for coordinate
transformation functions. These tests verify that:
1. In-place operations modify the original objects correctly
2. In-place operations return None (following NumPy convention)
3. Memory efficiency is maintained by avoiding unnecessary copies
4. Original data is properly transformed without creating new objects

The tests complement the existing test_coordinate_transforms.py which focuses on
copy-based transformations (inplace=False).
"""

import pytest
import numpy as np
from unittest.mock import Mock, MagicMock
import copy

from pinnacle_io.utils.coordinate_transforms import (
    transform_coordinates_for_curve,
    transform_coordinates_for_roi,
    transform_coordinates_for_rois,
    transform_dose,
    transform_coordinates_for_point,
    transform_coordinates_for_points,
    _apply_coordinate_flip,
)
from pinnacle_io.utils.patient_enum import PatientSetupEnum


class TestApplyCoordinateFlipInPlace:
    """Test the in-place coordinate flipping functionality."""

    def test_apply_coordinate_flip_inplace_no_flips(self):
        """Test in-place operation with no flips preserves original values."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        original_points = points.copy()
        
        result = _apply_coordinate_flip(points, False, False, False, inplace=True)
        
        # Should return None for in-place operations
        assert result is None
        # Original array should be unchanged since no flips
        np.testing.assert_array_equal(points, original_points)

    def test_apply_coordinate_flip_inplace_x_only(self):
        """Test in-place X coordinate flipping."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        expected = np.array([[-1.0, 2.0, 3.0], [-4.0, 5.0, 6.0]], dtype=np.float32)
        
        result = _apply_coordinate_flip(points, True, False, False, inplace=True)
        
        assert result is None
        np.testing.assert_array_equal(points, expected)

    def test_apply_coordinate_flip_inplace_y_only(self):
        """Test in-place Y coordinate flipping."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        expected = np.array([[1.0, -2.0, 3.0], [4.0, -5.0, 6.0]], dtype=np.float32)
        
        result = _apply_coordinate_flip(points, False, True, False, inplace=True)
        
        assert result is None
        np.testing.assert_array_equal(points, expected)

    def test_apply_coordinate_flip_inplace_z_only(self):
        """Test in-place Z coordinate flipping."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        expected = np.array([[1.0, 2.0, -3.0], [4.0, 5.0, -6.0]], dtype=np.float32)
        
        result = _apply_coordinate_flip(points, False, False, True, inplace=True)
        
        assert result is None
        np.testing.assert_array_equal(points, expected)

    def test_apply_coordinate_flip_inplace_all_axes(self):
        """Test in-place flipping of all coordinates."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        expected = np.array([[-1.0, -2.0, -3.0], [-4.0, -5.0, -6.0]], dtype=np.float32)
        
        result = _apply_coordinate_flip(points, True, True, True, inplace=True)
        
        assert result is None
        np.testing.assert_array_equal(points, expected)

    def test_apply_coordinate_flip_inplace_empty_array(self):
        """Test in-place flipping of empty array."""
        points = np.array([], dtype=np.float32).reshape(0, 3)
        
        result = _apply_coordinate_flip(points, True, True, True, inplace=True)
        
        assert result is None
        assert points.shape == (0, 3)


class TestCurveTransformationInPlace:
    """Test in-place curve coordinate transformation."""

    def create_mock_curve(self, points_data=None):
        """Create a mock Curve object for testing."""
        curve = Mock()
        if points_data is not None:
            curve.points = np.array(points_data, dtype=np.float32)
        else:
            curve.points = np.zeros((0, 3), dtype=np.float32)

        def mock_to_dict():
            return {
                'points_data': curve.points.tobytes() if curve.points.size > 0 else None,
                'contour_geometric_type': 'CLOSED_PLANAR',
                'flags': 0,
                'block_size': None,
                'num_points': len(curve.points),
                'curve_number': 1,
                'slice_index': None,
                'z_position': None,
                'roi_id': 1,
            }

        curve.to_dict = mock_to_dict
        return curve

    def test_transform_curve_inplace_hfs(self):
        """Test in-place curve transformation for HFS position."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        curve = self.create_mock_curve(points)
        original_curve_id = id(curve)
        
        result = transform_coordinates_for_curve(curve, "HFS", inplace=True)
        
        # Should return None for in-place operations
        assert result is None
        # Should be the same object
        assert id(curve) == original_curve_id
        # Points should be transformed (Y flipped)
        expected_points = np.array([[1.0, -2.0, 3.0], [4.0, -5.0, 6.0]], dtype=np.float32)
        np.testing.assert_array_equal(curve.points, expected_points)

    def test_transform_curve_inplace_ffp(self):
        """Test in-place curve transformation for FFP position."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        curve = self.create_mock_curve(points)
        
        result = transform_coordinates_for_curve(curve, "FFP", inplace=True)
        
        assert result is None
        # Points should be transformed (X and Y flipped)
        expected_points = np.array([[-1.0, -2.0, 3.0], [-4.0, -5.0, 6.0]], dtype=np.float32)
        np.testing.assert_array_equal(curve.points, expected_points)

    def test_transform_curve_inplace_ffs_no_change(self):
        """Test in-place curve transformation for FFS position (no change)."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        curve = self.create_mock_curve(points)
        original_points = points.copy()
        
        result = transform_coordinates_for_curve(curve, "FFS", inplace=True)
        
        assert result is None
        # Points should be unchanged for FFS
        np.testing.assert_array_equal(curve.points, original_points)

    def test_transform_curve_inplace_empty_points(self):
        """Test in-place transformation of curve with no points."""
        curve = self.create_mock_curve()
        
        result = transform_coordinates_for_curve(curve, "HFS", inplace=True)
        
        assert result is None
        assert curve.points.size == 0
        assert curve.points.shape == (0, 3)

    def test_transform_curve_inplace_none_points(self):
        """Test in-place transformation when points is None."""
        curve = Mock()
        curve.points = None
        
        result = transform_coordinates_for_curve(curve, "HFS", inplace=True)
        
        assert result is None
        assert curve.points is None


class TestROITransformationInPlace:
    """Test in-place ROI coordinate transformation."""

    def create_mock_roi(self, curves=None):
        """Create a mock ROI object for testing."""
        roi = Mock()
        roi.curve_list = curves or []
        return roi

    def create_mock_curve(self, points_data):
        """Create a mock Curve object for testing."""
        curve = Mock()
        curve.points = np.array(points_data, dtype=np.float32)
        return curve

    def test_transform_roi_inplace_single_curve(self):
        """Test in-place ROI transformation with single curve."""
        points = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        curve = self.create_mock_curve(points)
        roi = self.create_mock_roi([curve])
        original_roi_id = id(roi)
        original_curve_id = id(curve)
        
        result = transform_coordinates_for_roi(roi, "HFS", inplace=True)
        
        # Should return None for in-place operations
        assert result is None
        # Should be the same objects
        assert id(roi) == original_roi_id
        assert id(roi.curve_list[0]) == original_curve_id
        # Points should be transformed
        expected_points = np.array([[1.0, -2.0, 3.0], [4.0, -5.0, 6.0]], dtype=np.float32)
        np.testing.assert_array_equal(roi.curve_list[0].points, expected_points)

    def test_transform_roi_inplace_multiple_curves(self):
        """Test in-place ROI transformation with multiple curves."""
        points1 = np.array([[1.0, 2.0, 3.0]], dtype=np.float32)
        points2 = np.array([[4.0, 5.0, 6.0]], dtype=np.float32)
        curve1 = self.create_mock_curve(points1)
        curve2 = self.create_mock_curve(points2)
        roi = self.create_mock_roi([curve1, curve2])
        
        result = transform_coordinates_for_roi(roi, "HFP", inplace=True)
        
        assert result is None
        assert len(roi.curve_list) == 2
        # Both curves should be transformed (X flipped)
        expected_points1 = np.array([[-1.0, 2.0, 3.0]], dtype=np.float32)
        expected_points2 = np.array([[-4.0, 5.0, 6.0]], dtype=np.float32)
        np.testing.assert_array_equal(roi.curve_list[0].points, expected_points1)
        np.testing.assert_array_equal(roi.curve_list[1].points, expected_points2)

    def test_transform_roi_inplace_empty_curves(self):
        """Test in-place ROI transformation with no curves."""
        roi = self.create_mock_roi([])
        
        result = transform_coordinates_for_roi(roi, "HFS", inplace=True)
        
        assert result is None
        assert roi.curve_list == []

    def test_transform_rois_inplace_list(self):
        """Test in-place transformation of list of ROIs."""
        points = np.array([[1.0, 2.0, 3.0]], dtype=np.float32)
        curve1 = self.create_mock_curve(points.copy())
        curve2 = self.create_mock_curve(points.copy())
        roi1 = self.create_mock_roi([curve1])
        roi2 = self.create_mock_roi([curve2])
        roi_list = [roi1, roi2]
        original_roi1_id = id(roi1)
        original_roi2_id = id(roi2)
        
        result = transform_coordinates_for_rois(roi_list, "HFS", inplace=True)
        
        assert result is None
        assert len(roi_list) == 2
        # Should be the same objects
        assert id(roi_list[0]) == original_roi1_id
        assert id(roi_list[1]) == original_roi2_id
        # All ROIs should be transformed
        expected_points = np.array([[1.0, -2.0, 3.0]], dtype=np.float32)
        np.testing.assert_array_equal(roi_list[0].curve_list[0].points, expected_points)
        np.testing.assert_array_equal(roi_list[1].curve_list[0].points, expected_points)


class TestPointTransformationInPlace:
    """Test in-place Point coordinate transformation."""

    def create_mock_point(self, x=1.0, y=2.0, z=3.0):
        """Create a mock Point object for testing."""
        point = Mock()
        point.x_coord = x
        point.y_coord = y
        point.z_coord = z
        return point

    def test_transform_point_inplace_hfs(self):
        """Test in-place point transformation for HFS position."""
        point = self.create_mock_point(1.0, 2.0, 3.0)
        original_point_id = id(point)
        
        result = transform_coordinates_for_point(point, "HFS", inplace=True)
        
        # Should return None for in-place operations
        assert result is None
        # Should be the same object
        assert id(point) == original_point_id
        # Coordinates should be transformed (Y flipped)
        assert point.x_coord == 1.0
        assert point.y_coord == -2.0
        assert point.z_coord == 3.0

    def test_transform_point_inplace_ffp(self):
        """Test in-place point transformation for FFP position."""
        point = self.create_mock_point(1.0, 2.0, 3.0)
        
        result = transform_coordinates_for_point(point, "FFP", inplace=True)
        
        assert result is None
        # Coordinates should be transformed (X and Y flipped)
        assert point.x_coord == -1.0
        assert point.y_coord == -2.0
        assert point.z_coord == 3.0

    def test_transform_point_inplace_ffs_no_change(self):
        """Test in-place point transformation for FFS position (no change)."""
        point = self.create_mock_point(1.0, 2.0, 3.0)
        
        result = transform_coordinates_for_point(point, "FFS", inplace=True)
        
        assert result is None
        # Coordinates should be unchanged for FFS
        assert point.x_coord == 1.0
        assert point.y_coord == 2.0
        assert point.z_coord == 3.0

    def test_transform_point_inplace_missing_coordinates(self):
        """Test in-place transformation with missing coordinates."""
        point = Mock()
        point.x_coord = None
        point.y_coord = 2.0
        point.z_coord = 3.0
        
        result = transform_coordinates_for_point(point, "HFS", inplace=True)
        
        assert result is None
        # Coordinates should remain unchanged when any is None
        assert point.x_coord is None
        assert point.y_coord == 2.0
        assert point.z_coord == 3.0

    def test_transform_points_inplace_list(self):
        """Test in-place transformation of list of points."""
        point1 = self.create_mock_point(1.0, 2.0, 3.0)
        point2 = self.create_mock_point(4.0, 5.0, 6.0)
        points_list = [point1, point2]
        original_point1_id = id(point1)
        original_point2_id = id(point2)
        
        result = transform_coordinates_for_points(points_list, "HFS", inplace=True)
        
        assert result is None
        assert len(points_list) == 2
        # Should be the same objects
        assert id(points_list[0]) == original_point1_id
        assert id(points_list[1]) == original_point2_id
        # All points should be transformed (Y flipped)
        assert points_list[0].x_coord == 1.0
        assert points_list[0].y_coord == -2.0
        assert points_list[0].z_coord == 3.0
        assert points_list[1].x_coord == 4.0
        assert points_list[1].y_coord == -5.0
        assert points_list[1].z_coord == 6.0


class TestDoseTransformationInPlace:
    """Test in-place dose coordinate transformation."""

    def create_mock_dose_grid(self, origin_x=10.0, origin_y=20.0, origin_z=30.0,
                             voxel_x=2.0, voxel_y=2.0, voxel_z=2.5,
                             dim_x=50, dim_y=50, dim_z=40):
        """Create a mock DoseGrid object for testing."""
        dose_grid = Mock()

        # Mock origin
        origin = Mock()
        origin.x = origin_x
        origin.y = origin_y
        origin.z = origin_z
        dose_grid.origin = origin

        # Mock voxel size
        voxel_size = Mock()
        voxel_size.x = voxel_x
        voxel_size.y = voxel_y
        voxel_size.z = voxel_z
        dose_grid.voxel_size = voxel_size

        # Mock dimension
        dimension = Mock()
        dimension.x = dim_x
        dimension.y = dim_y
        dimension.z = dim_z
        dose_grid.dimension = dimension

        # Add origin_y attribute for in-place modification
        dose_grid.origin_y = origin_y

        def mock_to_dict():
            return {
                'voxel_size_x': voxel_x,
                'voxel_size_y': voxel_y,
                'voxel_size_z': voxel_z,
                'dimension_x': dim_x,
                'dimension_y': dim_y,
                'dimension_z': dim_z,
                'origin_x': origin_x,
                'origin_y': origin_y,
                'origin_z': origin_z,
                'vol_rot_delta_x': None,
                'vol_rot_delta_y': None,
                'vol_rot_delta_z': None,
                'display_2d': None,
                'dose_summation_type': None,
                'trial_id': 1,
            }

        dose_grid.to_dict = mock_to_dict
        return dose_grid

    def create_mock_dose(self, dose_grid=None):
        """Create a mock Dose object for testing."""
        dose = Mock()
        dose.dose_grid = dose_grid or self.create_mock_dose_grid()
        dose.pixel_data = np.random.random((50, 50, 40))
        dose._image_position_patient = [0, 0, 0]

        def mock_to_dict():
            return {
                'dose_id': None,
                'dose_type': 'PHYSICAL',
                'dose_unit': 'GY',
                'datatype': 1,
                'bitpix': 32,
                'bytes_pix': 4,
                'vol_max': 0.0,
                'vol_min': 0.0,
                'dose_comment': '',
                'dose_grid_scaling': 1.0,
                'dose_summation_type': 'PLAN',
                'referenced_plan_id': None,
                '_referenced_beam_numbers': None,
                'dose_grid_id': 1,
                'beam_id': None,
                'trial_id': None,
                'dose_grid': dose.dose_grid.to_dict(),
            }

        dose.to_dict = mock_to_dict
        return dose

    def test_transform_dose_inplace_hfs(self):
        """Test in-place dose transformation for HFS position."""
        dose_grid = self.create_mock_dose_grid(origin_x=10.0, origin_y=20.0, origin_z=30.0)
        dose = self.create_mock_dose(dose_grid)
        original_dose_id = id(dose)
        original_dose_grid_id = id(dose.dose_grid)
        original_pixel_data_id = id(dose.pixel_data)

        result = transform_dose(dose, "HFS", inplace=True)

        # Should return None for in-place operations
        assert result is None
        # Should be the same objects
        assert id(dose) == original_dose_id
        assert id(dose.dose_grid) == original_dose_grid_id
        assert id(dose.pixel_data) == original_pixel_data_id

        # Check dose grid transformation
        y_dose_shift = 2.0 * (50 - 1)  # voxel_size.y * (dimension.y - 1) = 98.0
        expected_origin_y = -20.0 - y_dose_shift  # -20.0 - 98.0 = -118.0
        assert dose.dose_grid.origin_y == expected_origin_y

    def test_transform_dose_inplace_hfp(self):
        """Test in-place dose transformation for HFP position."""
        dose_grid = self.create_mock_dose_grid(origin_x=10.0, origin_y=20.0, origin_z=30.0)
        dose = self.create_mock_dose(dose_grid)

        result = transform_dose(dose, "HFP", inplace=True)

        assert result is None
        # Check dose grid transformation
        y_dose_shift = 2.0 * (50 - 1)  # 98.0
        expected_origin_y = 20.0 + y_dose_shift  # 20.0 + 98.0 = 118.0
        assert dose.dose_grid.origin_y == expected_origin_y

    def test_transform_dose_inplace_ffs(self):
        """Test in-place dose transformation for FFS position."""
        dose_grid = self.create_mock_dose_grid(origin_x=10.0, origin_y=20.0, origin_z=30.0)
        dose = self.create_mock_dose(dose_grid)

        result = transform_dose(dose, "FFS", inplace=True)

        assert result is None
        # Check dose grid transformation
        y_dose_shift = 2.0 * (50 - 1)  # 98.0
        expected_origin_y = -20.0 - y_dose_shift  # -20.0 - 98.0 = -118.0
        assert dose.dose_grid.origin_y == expected_origin_y

    def test_transform_dose_inplace_ffp(self):
        """Test in-place dose transformation for FFP position."""
        dose_grid = self.create_mock_dose_grid(origin_x=10.0, origin_y=20.0, origin_z=30.0)
        dose = self.create_mock_dose(dose_grid)

        result = transform_dose(dose, "FFP", inplace=True)

        assert result is None
        # Check dose grid transformation
        y_dose_shift = 2.0 * (50 - 1)  # 98.0
        expected_origin_y = 20.0 + y_dose_shift  # 20.0 + 98.0 = 118.0
        assert dose.dose_grid.origin_y == expected_origin_y

    def test_transform_dose_inplace_missing_dose_grid(self):
        """Test in-place dose transformation with missing dose grid."""
        dose = Mock()
        dose.dose_grid = None

        with pytest.raises(ValueError, match="Dose object must have a dose_grid"):
            transform_dose(dose, "HFS", inplace=True)

    def test_transform_dose_inplace_invalid_position(self):
        """Test in-place dose transformation with invalid patient position."""
        dose = self.create_mock_dose()

        with pytest.raises(ValueError, match="Unsupported patient position"):
            transform_dose(dose, "INVALID", inplace=True)


class TestInPlaceMemoryEfficiency:
    """Test memory efficiency and object identity preservation for in-place operations."""

    def test_inplace_preserves_object_identity(self):
        """Test that in-place operations preserve object identity."""
        # Test curve
        points = np.array([[1.0, 2.0, 3.0]], dtype=np.float32)
        curve = Mock()
        curve.points = points
        original_curve_id = id(curve)
        original_points_id = id(curve.points)

        transform_coordinates_for_curve(curve, "HFS", inplace=True)

        assert id(curve) == original_curve_id
        assert id(curve.points) == original_points_id  # Same array, modified in-place

    def test_inplace_vs_copy_behavior_comparison(self):
        """Test that in-place and copy operations produce equivalent results."""
        # Create identical test data
        points1 = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float32)
        points2 = points1.copy()

        curve1 = Mock()
        curve1.points = points1
        curve1.to_dict = lambda: {'points_data': points1.tobytes()}

        curve2 = Mock()
        curve2.points = points2
        curve2.to_dict = lambda: {'points_data': points2.tobytes()}

        # Transform one in-place, one with copy
        transform_coordinates_for_curve(curve1, "HFS", inplace=True)
        result_copy = transform_coordinates_for_curve(curve2, "HFS", inplace=False)

        # Results should be equivalent
        np.testing.assert_array_equal(curve1.points, result_copy.points)

    def test_inplace_operations_return_none(self):
        """Test that all in-place operations return None following NumPy convention."""
        # Test curve
        curve = Mock()
        curve.points = np.array([[1.0, 2.0, 3.0]], dtype=np.float32)
        assert transform_coordinates_for_curve(curve, "HFS", inplace=True) is None

        # Test ROI
        roi = Mock()
        roi.curve_list = [curve]
        assert transform_coordinates_for_roi(roi, "HFS", inplace=True) is None

        # Test ROI list
        assert transform_coordinates_for_rois([roi], "HFS", inplace=True) is None

        # Test point
        point = Mock()
        point.x_coord = 1.0
        point.y_coord = 2.0
        point.z_coord = 3.0
        assert transform_coordinates_for_point(point, "HFS", inplace=True) is None

        # Test point list
        assert transform_coordinates_for_points([point], "HFS", inplace=True) is None


if __name__ == "__main__":
    pytest.main([__file__])
