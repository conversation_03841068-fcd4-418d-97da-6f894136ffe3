# Pinnacle IO UI Development Plan

## Overview

This document outlines the phased development plan for the Pinnacle IO UI application, a ttkbootstrap-based desktop application for viewing and analyzing Pinnacle radiotherapy treatment planning data.

## Application Architecture

### Core Components

1. **Main Window** (`main_window.py`)
   - Primary application window with darkly theme
   - Orchestrates all UI components and data loading
   - Handles application lifecycle and window management

2. **Widget Components** (`widgets/`)
   - **Menu Bar** (`menu_bar.py`) - File, Edit, View, Help menus
   - **Left Sidebar** (`left_sidebar.py`) - Patient/Plan/Trial navigation
   - **Right Sidebar** (`right_sidebar.py`) - Info panel with tabbed interface
   - **CT Viewer** (`ct_viewer.py`) - Central image display with overlays

3. **Data Integration**
   - Uses existing `PinnacleReader` from `pinnacle_io.api`
   - Loads from test data by default (`tests/test_data/01/`)
   - Supports directory, tar, and zip file formats

## UI Layout Specification

```
┌─────────────────────────────────────────────────────────────────────────┐
│ Menu Bar: File | Edit | View | Help                                     │
├──────────────┬───────────────────────────────────────┬──────────────────┤
│ Left Sidebar │                                       │ Right Sidebar    │
│              │                                       │                  │
│ ┌──────────┐ │            CT Viewer                  │ ┌──────────────┐ │
│ │Patients  │ │                                       │ │Current Info  │ │
│ │          │ │  ┌─────────────────────────────────┐  │ │              │ │
│ │          │ │  │                                 │  │ │              │ │
│ └──────────┘ │  │        Medical Image            │  │ └──────────────┘ │
│              │  │         Display                 │  │                  │
│ ┌──────────┐ │  │      (512x512 pixels)           │  │ ┌──────────────┐ │
│ │Plans     │ │  │                                 │  │ │   Notebook   │ │
│ │          │ │  │   With ROI/POI/Beam/Dose        │  │ │┌────────────┐│ │
│ │          │ │  │        Overlays                 │  │ ││ ROIs  POIs ││ │
│ └──────────┘ │  │                                 │  │ ││ Beams Dose ││ │
│              │  └─────────────────────────────────┘  │ │└────────────┘│ │
│ ┌──────────┐ │                                       │ │              │ │
│ │Trials    │ │  ┌─────────────────────────────────┐  │ │   Content    │ │
│ │          │ │  │     Control Panel               │  │ │   for Tab    │ │
│ │          │ │  │ Slice: [====|====] Win/Level    │  │ │              │ │
│ └──────────┘ │  └─────────────────────────────────┘  │ │              │ │
├──────────────┴───────────────────────────────────────┴──────────────────┤
│ Status Bar (future enhancement)                                         │
└─────────────────────────────────────────────────────────────────────────┘
```

## Phase-Based Development Plan

### Phase 1: Foundation (COMPLETED)
**Objective:** Establish core UI structure and basic functionality

#### Tasks Completed:
- ✅ Create main window with ttkbootstrap darkly theme
- ✅ Implement menu bar with File, Edit, View, Help menus
- ✅ Create left sidebar with patient/plan/trial navigation
- ✅ Build right sidebar with info panel and tabbed interface
- ✅ Add central CT viewer area with basic image display
- ✅ Integrate PinnacleReader for data loading from test data
- ✅ Set up auto-selection of first patient/plan/trial

#### Deliverables:
- Working UI framework with all major components
- Basic data loading and navigation
- Responsive layout with proper theming

### Phase 2: Enhanced Image Viewing
**Objective:** Improve CT viewer functionality and image interaction

#### Tasks:
- [ ] Implement proper image scaling and zoom functionality
- [ ] Add pan capability with mouse drag
- [ ] Enhance window/level controls with mouse interaction
- [ ] Add image information display (pixel values, coordinates)
- [ ] Implement slice navigation with mouse wheel
- [ ] Add image export functionality
- [ ] Optimize image rendering performance

#### Key Features:
- **Interactive Viewing:**
  - Mouse wheel slice navigation
  - Click-and-drag panning
  - Right-click context menu
  - Pixel value display on hover

- **Window/Level:**
  - Real-time adjustment with sliders
  - Preset buttons for common settings
  - Mouse interaction for quick adjustments
  - Histogram display (optional)

### Phase 3: ROI and Structure Visualization
**Objective:** Add comprehensive ROI/structure overlay functionality

#### Tasks:
- [ ] Implement ROI contour loading and parsing
- [ ] Add ROI contour visualization on CT slices
- [ ] Create ROI color coding and legend
- [ ] Implement ROI visibility controls
- [ ] Add ROI information tooltips
- [ ] Support ROI selection and highlighting
- [ ] Add ROI statistics display

#### Key Features:
- **ROI Display:**
  - Contour overlays with proper colors
  - Filled/outline display options
  - Individual ROI show/hide controls
  - ROI name labels and statistics

- **Interactive Controls:**
  - ROI tree view with checkboxes
  - Color picker for custom colors
  - Opacity/transparency controls
  - ROI measurement tools

### Phase 4: Point of Interest (POI) Display
**Objective:** Add POI visualization and management

#### Tasks:
- [ ] Implement POI marker display
- [ ] Add POI symbol customization
- [ ] Create POI information panels
- [ ] Add POI navigation features
- [ ] Implement POI editing capabilities
- [ ] Support different POI types

#### Key Features:
- **POI Visualization:**
  - Various marker shapes and sizes
  - Color-coded by type
  - Distance measurements between POIs
  - Cross-hair display for precise positioning

### Phase 5: Beam Geometry Visualization
**Objective:** Add beam geometry and field display

#### Tasks:
- [ ] Implement beam projection on CT slices
- [ ] Add beam's eye view (BEV) display
- [ ] Create beam field outline visualization
- [ ] Add beam information display
- [ ] Implement beam selection and highlighting
- [ ] Add isocenter visualization

#### Key Features:
- **Beam Display:**
  - Field edge projections on CT
  - Gantry angle indicators
  - Beam direction arrows
  - MLC leaf position display
  - Isocenter cross-hairs

### Phase 6: Dose Distribution Display
**Objective:** Add comprehensive dose visualization

#### Tasks:
- [ ] Implement dose overlay with color wash
- [ ] Add isodose line display
- [ ] Create dose color map customization
- [ ] Add dose-volume histogram (DVH) display
- [ ] Implement dose statistics
- [ ] Add dose profile tools

#### Key Features:
- **Dose Visualization:**
  - Color wash overlay with transparency
  - Isodose lines with percentage labels
  - Customizable color maps
  - Dose statistics and measurements
  - DVH curves for ROIs

### Phase 7: Advanced Analysis Tools
**Objective:** Add measurement and analysis capabilities

#### Tasks:
- [ ] Implement distance measurement tools
- [ ] Add angle measurement capabilities
- [ ] Create annotation tools
- [ ] Add screenshot and export functions
- [ ] Implement plan comparison features
- [ ] Add reporting capabilities

#### Key Features:
- **Measurement Tools:**
  - Linear distance measurements
  - Angular measurements
  - Area calculations
  - Volume measurements
  - Profile line tools

### Phase 8: Performance and Polish
**Objective:** Optimize performance and enhance user experience

#### Tasks:
- [ ] Optimize image loading and caching
- [ ] Implement progressive loading for large datasets
- [ ] Add keyboard shortcuts
- [ ] Enhance error handling and user feedback
- [ ] Add comprehensive help system
- [ ] Implement preferences/settings
- [ ] Add undo/redo functionality

#### Key Features:
- **Performance:**
  - Lazy loading of images
  - Background data processing
  - Memory management
  - Efficient rendering

- **User Experience:**
  - Consistent keyboard shortcuts
  - Context-sensitive help
  - Progress indicators
  - Error recovery

## Technical Implementation Details

### Data Flow Architecture

```
PinnacleReader (API)
       ↓
MainWindow (Orchestrator)
       ├── LeftSidebar (Navigation)
       ├── RightSidebar (Information)
       └── CTViewer (Visualization)
```

### Key Integration Points

1. **Data Loading:**
   - PinnacleReader handles all file format detection
   - MainWindow coordinates data requests
   - Widgets receive processed data objects

2. **Event Handling:**
   - Selection events propagate from LeftSidebar to MainWindow
   - MainWindow updates all dependent widgets
   - CTViewer responds to overlay control changes

3. **State Management:**
   - Current patient/plan/trial stored in MainWindow
   - Widget-specific state managed locally
   - Display settings preserved between sessions

### Error Handling Strategy

1. **Graceful Degradation:**
   - Show placeholder content when data unavailable
   - Disable features that require missing data
   - Provide clear error messages

2. **User Feedback:**
   - Progress indicators for long operations
   - Status messages for user actions
   - Error dialogs with actionable information

## Testing Strategy

### Phase 1 Testing (Current)
- Basic UI layout and theme verification
- Data loading with test dataset
- Navigation functionality
- Widget responsiveness

### Future Testing Phases
- Image display accuracy
- ROI contour rendering correctness
- Dose calculation verification
- Performance with large datasets
- Cross-platform compatibility

## Dependencies and Requirements

### Current Dependencies:
- `ttkbootstrap>=1.10.0` - Modern tkinter styling
- `pillow>=9.0.0` - Image processing and display
- `numpy>=1.21.0` - Numerical computations
- `sqlalchemy>=2.0.0` - Database ORM (existing)

### Future Dependencies:
- `matplotlib>=3.5.0` - DVH plotting and advanced visualization
- `scipy>=1.8.0` - Scientific computations for analysis tools

## Development Guidelines

### Code Organization
- Keep widgets modular and self-contained
- Use clear interfaces between components
- Maintain separation of concerns
- Follow existing project conventions

### UI/UX Principles
- Maintain dark theme consistency
- Provide immediate visual feedback
- Use standard medical imaging conventions
- Support both keyboard and mouse interaction

### Performance Considerations
- Lazy load images and dose data
- Use efficient numpy operations
- Implement proper caching strategies
- Optimize rendering pipelines

## Getting Started

### Running the Application
```bash
# Install dependencies
pip install -r requirements.txt

# Run with test data
python test_ui.py

# Run with custom data
python test_ui.py /path/to/pinnacle/data
```

### Development Setup
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
pytest

# Code formatting
black pinnacle_io/ui/
isort pinnacle_io/ui/

# Type checking
mypy pinnacle_io/ui/
```

## Future Enhancements

### Beyond Core Functionality
- Multi-patient comparison views
- Plan optimization visualization
- DICOM export capabilities
- Network-based data loading
- Plugin architecture for extensions
- Advanced rendering with OpenGL
- 3D visualization capabilities
- Treatment planning workflow integration

### Integration Opportunities
- DICOM RT import/export
- Treatment planning system connectivity
- Quality assurance workflow integration
- Reporting and documentation automation

---

## Implementation Status

**Current Status:** Phase 1 Complete ✅

**Next Priority:** Phase 2 - Enhanced Image Viewing

**Estimated Timeline:**
- Phase 2: 2-3 weeks
- Phase 3: 2-3 weeks
- Phase 4: 1-2 weeks
- Phase 5: 2-3 weeks
- Phase 6: 3-4 weeks
- Phases 7-8: 2-3 weeks each

**Total Estimated Development Time:** 14-20 weeks for complete implementation