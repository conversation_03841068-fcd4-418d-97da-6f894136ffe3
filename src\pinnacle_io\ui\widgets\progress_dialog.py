"""
Modal progress dialog for tracking long-running asynchronous operations.
"""

import os
import time
import tkinter as tk
from tkinter import messagebox
from typing import Optional, Callable, Any, Dict, List
import ttkbootstrap as ttk
from threading import Event, Thread
from datetime import datetime


class ProgressDialog:
    """
    Modal progress dialog window for tracking async operations.

    Features:
    - One-line status text in production mode
    - Scrolling timing log in development mode
    - Progress bar with percentage
    - Optional cancel button for interrupting operations
    - Benchmarking capabilities for performance analysis
    """

    def __init__(self, parent, title: str = "Processing...",
                 cancelable: bool = False,
                 development_mode: Optional[bool] = None,
                 auto_close: Optional[bool] = None):
        """
        Initialize the progress dialog.

        Args:
            parent: Parent window
            title: Dialog window title
            cancelable: Whether to show cancel button
            development_mode: Enable development features. If None, auto-detect from environment
            auto_close: Whether to auto-close on completion. If None, auto-detect from dev mode
        """
        self.parent = parent
        self.title = title
        self.cancelable = cancelable

        # Auto-detect development mode if not specified
        if development_mode is None:
            development_mode = os.getenv('PINNACLE_IO_DEV', '').lower() in ('1', 'true', 'yes', 'on')
        self.development_mode = development_mode

        # Auto-close behavior
        if auto_close is None:
            self.auto_close = not self.development_mode  # Stay open in dev mode
        else:
            self.auto_close = auto_close

        # Dialog state
        self.dialog: Optional[tk.Toplevel] = None
        self.is_cancelled = Event()
        self.is_active = False
        self.operation_completed = False
        self.cancel_callback: Optional[Callable] = None

        # Progress tracking
        self.current_progress = 0
        self.max_progress = 100
        self.status_text = ""

        # Timing and benchmarking
        self.start_time: Optional[datetime] = None
        self.last_step_time: Optional[datetime] = None
        self.timing_log: List[Dict[str, Any]] = []

        # Track previous step for accurate timing
        self.previous_step_name: Optional[str] = None
        self.previous_step_message: Optional[str] = None

        # UI components
        self.progress_var = None
        self.status_var = None
        self.progress_bar = None
        self.status_label = None
        self.timing_text = None
        self.cancel_button = None
        self.close_button = None

    def show(self, status: str = "Starting...", max_progress: int = 100) -> None:
        """
        Show the progress dialog.

        Args:
            status: Initial status message
            max_progress: Maximum progress value
        """
        if self.is_active:
            return

        self.is_active = True
        self.max_progress = max_progress
        self.current_progress = 0
        self.status_text = status
        self.start_time = datetime.now()
        self.last_step_time = self.start_time
        self.timing_log.clear()
        self.is_cancelled.clear()

        # Reset previous step tracking
        self.previous_step_name = None
        self.previous_step_message = None

        self._create_dialog()
        self._log_timing("Dialog shown", status)

    def hide(self) -> None:
        """Hide and destroy the progress dialog."""
        if not self.is_active:
            return

        self.is_active = False

        if self.dialog:
            try:
                self.dialog.destroy()
            except tk.TclError:
                pass  # Dialog already destroyed
            self.dialog = None

        # Log final timing only if operation wasn't already completed
        if self.start_time and not self.operation_completed:
            total_time = (datetime.now() - self.start_time).total_seconds()
            self._log_timing("Dialog closed", f"Total elapsed time: {total_time:.3f} seconds")

    def update_progress(self, progress: int, status: str = "") -> bool:
        """
        Update progress and status.

        Args:
            progress: Current progress value (0 to max_progress)
            status: Status message

        Returns:
            False if operation was cancelled, True otherwise
        """
        if not self.is_active or self.is_cancelled.is_set():
            return False

        # Log completion of previous step before updating to new status
        if status and self.previous_step_name and self.previous_step_message:
            self._log_previous_step_completion(self.previous_step_name, self.previous_step_message)

        # Update progress
        self.current_progress = max(0, min(progress, self.max_progress))

        # Update status if provided
        if status:
            self.status_text = status

            # Store current step as previous for next update
            self.previous_step_name = "Progress update"
            self.previous_step_message = f"{status} ({self.current_progress}/{self.max_progress})"

            # Start timing for this new step
            self._log_timing("Progress update", f"{status} ({self.current_progress}/{self.max_progress})")

        # Update UI on main thread
        if self.dialog:
            try:
                # self.parent.after_idle(self._update_ui)
                self.parent.after(0, self._update_ui)
            except tk.TclError:
                pass  # Parent destroyed

        return not self.is_cancelled.is_set()

    def update_status(self, status: str) -> bool:
        """
        Update status without changing progress.

        Args:
            status: Status message

        Returns:
            False if operation was cancelled, True otherwise
        """
        if not self.is_active or self.is_cancelled.is_set():
            return False

        # Log completion of previous step before updating to new status
        if self.previous_step_name and self.previous_step_message:
            self._log_previous_step_completion(self.previous_step_name, self.previous_step_message)

        self.status_text = status

        # Store current step as previous for next update
        self.previous_step_name = "Status update"
        self.previous_step_message = status

        # Start timing for this new step
        self._log_timing("Status update", status)

        # Update UI on main thread
        if self.dialog:
            try:
                self.parent.after_idle(self._update_ui)
            except tk.TclError:
                pass  # Parent destroyed

        return not self.is_cancelled.is_set()

    def finish_current_step(self) -> None:
        """
        Finish/flush the current progress step to generate accurate timing
        without updating status or progress.

        This method logs the completion time for the current step and can be
        called to ensure accurate performance benchmarking.
        """
        if self.previous_step_name and self.previous_step_message:
            self._log_previous_step_completion(self.previous_step_name, self.previous_step_message)
            # Clear previous step tracking since we've logged its completion
            self.previous_step_name = None
            self.previous_step_message = None

    def set_cancel_callback(self, callback: Callable) -> None:
        """
        Set callback function to call when cancel is requested.

        Args:
            callback: Function to call for cancellation
        """
        self.cancel_callback = callback

    def is_cancelled_flag(self) -> bool:
        """Check if cancellation was requested."""
        return self.is_cancelled.is_set()

    def complete_operation(self, success: bool = True, final_message: str = "Operation completed") -> None:
        """
        Mark operation as completed and update UI accordingly.

        Args:
            success: Whether the operation completed successfully
            final_message: Final status message to display
        """
        self.operation_completed = True

        # Finish the current step before completing
        self.finish_current_step()

        # Update status with final message (this will log the timing automatically)
        self.update_status(final_message)

        # Add final total elapsed time entry to timing log
        if self.start_time:
            total_time = (datetime.now() - self.start_time).total_seconds()
            self._log_timing("Total elapsed time", f"Total elapsed time: {total_time:.3f} seconds")

        # Update button states
        self._update_button_states()

        # Auto-close behavior (only in production mode and on success)
        if self.auto_close and success:
            if self.dialog:
                self.dialog.after(2000, self.hide)  # 2 second delay to show final status
        else:
            # In development mode or on failure, stay open for review
            self._prepare_for_manual_close()

    def get_timing_report(self) -> str:
        """
        Get formatted timing report.

        Returns:
            Formatted timing report string
        """
        if not self.timing_log:
            return "No timing data available"

        report_lines = ["=== Timing Report ==="]

        for i, entry in enumerate(self.timing_log):
            timestamp = entry['timestamp']
            step = entry['step']
            message = entry['message']
            elapsed = entry['elapsed_total']
            step_time = entry['elapsed_step']

            if i == 0:
                report_lines.append(f"[{timestamp}] START: {message}")
            else:
                report_lines.append(
                    f"[{timestamp}] {step}: {message} "
                    f"(+{step_time:.3f}s, total: {elapsed:.3f}s)"
                )

        if self.start_time:
            total_elapsed = (datetime.now() - self.start_time).total_seconds()
            report_lines.append(f"\nTotal execution time: {total_elapsed:.3f} seconds")

        return "\n".join(report_lines)

    def _create_dialog(self) -> None:
        """Create the modal dialog window."""
        # Create toplevel dialog
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # Configure dialog
        dialog_width = 600 if self.development_mode else 400
        dialog_height = 400 if self.development_mode else 150

        # Center dialog on parent
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        x = max(0, parent_x + (parent_width - dialog_width) // 2)
        y = max(0, parent_y + (parent_height - dialog_height) // 2)

        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        self.dialog.minsize(300, 120)
        self.dialog.resizable(True, self.development_mode)

        # Prevent dialog from being closed except via cancel
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_close_requested)

        # Create UI elements
        self._setup_dialog_ui()

        # Focus on dialog
        self.dialog.focus_force()

    def _setup_dialog_ui(self) -> None:
        """Set up the dialog UI components."""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Status label
        self.status_var = tk.StringVar(value=self.status_text)
        self.status_label = ttk.Label(
            main_frame,
            textvariable=self.status_var,
            font=("default", 10),
            wraplength=350
        )
        self.status_label.pack(pady=(0, 15))

        # Progress bar with percentage
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        self.progress_var = tk.IntVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=self.max_progress,
            mode='determinate'
        )
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Percentage label
        self.percent_var = tk.StringVar(value="0%")
        percent_label = ttk.Label(progress_frame, textvariable=self.percent_var)
        percent_label.pack(side=tk.RIGHT, padx=(10, 0))

        # Development mode: timing log
        if self.development_mode:
            # Separator
            separator = ttk.Separator(main_frame, orient='horizontal')
            separator.pack(fill=tk.X, pady=(0, 10))

            # Timing log label
            timing_label = ttk.Label(main_frame, text="Timing Log:", font=("default", 9, "bold"))
            timing_label.pack(anchor=tk.W)

            # Scrollable text area for timing log
            timing_frame = ttk.Frame(main_frame)
            timing_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 15))

            self.timing_text = tk.Text(
                timing_frame,
                height=8,
                font=("Consolas", 8),
                wrap=tk.WORD,
                bg='#2b2b2b',  # Match darkly theme
                fg='#ffffff',
                insertbackground='#ffffff'
            )

            timing_scrollbar = ttk.Scrollbar(timing_frame, orient=tk.VERTICAL, command=self.timing_text.yview)
            self.timing_text.configure(yscrollcommand=timing_scrollbar.set)

            self.timing_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            timing_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Make text read-only
            self.timing_text.config(state=tk.DISABLED)

        # Buttons frame - always create
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # Cancel button: Always create, but only show if cancelable
        self.cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=self._on_cancel_clicked,
            style="danger.TButton"
        )
        if self.cancelable:
            self.cancel_button.pack(side=tk.LEFT, padx=(0, 5))

        # Close button - initially disabled
        self.close_button = ttk.Button(
            button_frame,
            text="Close",
            command=self._on_close_clicked,
            state='disabled'
        )
        self.close_button.pack(side=tk.RIGHT)

        # Set up keyboard shortcuts
        self.dialog.bind('<Escape>', self._on_escape_pressed)
        self.dialog.bind('<Return>', self._on_return_pressed)

        # Initial UI update
        self._update_ui()

    def _update_ui(self) -> None:
        """Update UI elements with current values."""
        if not self.dialog:
            return

        try:
            # Update status
            if self.status_var:
                self.status_var.set(self.status_text)

            # Update progress bar
            if self.progress_var:
                self.progress_var.set(self.current_progress)

            # Update percentage
            if hasattr(self, 'percent_var'):
                percentage = int((self.current_progress / self.max_progress) * 100) if self.max_progress > 0 else 0
                self.percent_var.set(f"{percentage}%")

            # Update timing log in development mode
            if self.development_mode and self.timing_text:
                self._update_timing_display()

        except tk.TclError:
            pass  # Dialog destroyed

    def _update_timing_display(self) -> None:
        """Update the timing log display."""
        if not self.timing_text or not self.timing_log:
            return

        # Show all entries instead of truncating to avoid losing information
        recent_entries = self.timing_log

        # Build display text
        lines = []
        for entry in recent_entries:
            timestamp = entry['timestamp']
            step = entry['step']
            message = entry['message']
            step_time = entry['elapsed_step']

            if step == 1:  # First entry
                lines.append(f"[{timestamp}] START: {message}")
            else:
                lines.append(f"[{timestamp}] +{step_time:.3f}s: {message}")

        display_text = "\n".join(lines)

        try:
            # Update text widget
            self.timing_text.config(state=tk.NORMAL)
            self.timing_text.delete(1.0, tk.END)
            self.timing_text.insert(tk.END, display_text)
            self.timing_text.config(state=tk.DISABLED)

            # Auto-scroll to bottom
            self.timing_text.see(tk.END)
        except tk.TclError:
            pass  # Widget destroyed

    def _log_timing(self, step_name: str, message: str) -> None:
        """
        Log timing information for benchmarking.

        Args:
            step_name: Name of the step
            message: Descriptive message
        """
        if not self.start_time:
            return

        now = datetime.now()
        elapsed_total = (now - self.start_time).total_seconds()
        elapsed_step = (now - self.last_step_time).total_seconds() if self.last_step_time else 0

        entry = {
            'timestamp': now.strftime("%H:%M:%S.%f")[:-3],  # Include milliseconds
            'step': len(self.timing_log) + 1,
            'step_name': step_name,
            'message': message,
            'elapsed_total': elapsed_total,
            'elapsed_step': elapsed_step
        }

        self.timing_log.append(entry)
        self.last_step_time = now

        # Print to console if in development mode
        if self.development_mode:
            if entry['step'] == 1:
                print(f"[PROGRESS] START: {message}")
            else:
                print(f"[PROGRESS] +{elapsed_step:.3f}s: {message} (total: {elapsed_total:.3f}s)")

    def _log_previous_step_completion(self, previous_step_name: str, previous_message: str) -> None:
        """
        Log completion of the previous step with accurate timing.

        Args:
            previous_step_name: Name of the previous step that just completed
            previous_message: Message of the previous step that just completed
        """
        if not self.start_time or not self.last_step_time:
            return

        now = datetime.now()
        elapsed_step = (now - self.last_step_time).total_seconds()
        elapsed_total = (now - self.start_time).total_seconds()

        # Update the last entry in timing log if it exists
        if self.timing_log and self.timing_log[-1]['step_name'] == previous_step_name:
            # Update the existing entry with completion timing
            self.timing_log[-1]['elapsed_step'] = elapsed_step
            self.timing_log[-1]['elapsed_total'] = elapsed_total
            self.timing_log[-1]['timestamp'] = now.strftime("%H:%M:%S.%f")[:-3]

            # Print completion to console if in development mode
            if self.development_mode:
                print(f"[PROGRESS] COMPLETED +{elapsed_step:.3f}s: {previous_message} (total: {elapsed_total:.3f}s)")
        else:
            # Create a new completion entry
            entry = {
                'timestamp': now.strftime("%H:%M:%S.%f")[:-3],
                'step': len(self.timing_log) + 1,
                'step_name': f"{previous_step_name} (completed)",
                'message': f"Completed: {previous_message}",
                'elapsed_total': elapsed_total,
                'elapsed_step': elapsed_step
            }
            self.timing_log.append(entry)

            # Print to console if in development mode
            if self.development_mode:
                print(f"[PROGRESS] COMPLETED +{elapsed_step:.3f}s: {previous_message} (total: {elapsed_total:.3f}s)")

        # Update last step time for next measurement
        self.last_step_time = now

    def _on_cancel_clicked(self) -> None:
        """Handle cancel button click."""
        # Show confirmation dialog
        if messagebox.askquestion(
            "Cancel Operation",
            "Are you sure you want to cancel this operation?",
            parent=self.dialog
        ) == 'yes':
            self._cancel_operation()

    def _on_close_requested(self) -> None:
        """Handle dialog close request."""
        if self.operation_completed:
            # Operation is complete, allow closing
            self.hide()
        elif self.cancelable:
            # Operation is running, try to cancel
            self._on_cancel_clicked()
        # Otherwise, ignore close request (modal behavior)

    def _cancel_operation(self) -> None:
        """Cancel the current operation."""
        self.is_cancelled.set()
        self._log_timing("Operation cancelled", "User requested cancellation")

        # Call cancel callback if set
        if self.cancel_callback:
            try:
                self.cancel_callback()
            except Exception as e:
                print(f"Error in cancel callback: {e}")

        # Mark operation as completed (cancelled) and update UI
        self.operation_completed = True
        if self.status_var:
            self.status_var.set("Operation cancelled")

        if self.cancel_button:
            self.cancel_button.config(state='disabled')

        # Update button states to enable close button
        self._update_button_states()

        # In development mode, stay open for review; in production, auto-close after delay
        if self.auto_close:
            if self.dialog:
                self.dialog.after(2000, self.hide)  # 2 second delay to show cancellation status
        else:
            # Prepare for manual close in development mode
            self._prepare_for_manual_close()

    def _update_button_states(self) -> None:
        """Update button states based on operation phase."""
        # Check if dialog is still active and widgets exist
        if not self.is_active or not self.dialog:
            return

        try:
            if self.operation_completed:
                # Operation completed - disable cancel, enable close
                if self.cancel_button:
                    self.cancel_button.config(state='disabled')
                if self.close_button:
                    self.close_button.config(state='normal')
                    self.close_button.focus_set()  # Focus on close button
            else:
                # Operation running - enable cancel (if applicable), disable close
                if self.cancel_button and self.cancelable:
                    self.cancel_button.config(state='normal')
                if self.close_button:
                    self.close_button.config(state='disabled')
        except tk.TclError:
            # Widgets have been destroyed, ignore
            pass

    def _prepare_for_manual_close(self) -> None:
        """Prepare dialog for manual close after operation completion."""
        # Focus on close button for keyboard accessibility
        if self.close_button:
            self.close_button.focus_set()

        # In development mode, auto-scroll to show timing summary
        if self.development_mode and self.timing_text:
            try:
                self.timing_text.see('end')
            except tk.TclError:
                pass  # Widget destroyed

    def _on_close_clicked(self) -> None:
        """Handle close button click."""
        if self.operation_completed:
            self.hide()
        # If not completed, button should be disabled, but safety check
        elif self.cancelable:
            self._on_cancel_clicked()

    def _on_escape_pressed(self, event) -> None:
        """Handle Escape key press."""
        if self.operation_completed:
            self.hide()
        elif self.cancelable:
            self._on_cancel_clicked()

    def _on_return_pressed(self, event) -> None:
        """Handle Return/Enter key press."""
        if self.operation_completed and self.close_button and self.close_button['state'] == 'normal':
            self.hide()


class AsyncOperationRunner:
    """
    Helper class for running operations with progress tracking.

    This class provides a simple interface for running long-running operations
    with automatic progress dialog management.
    """

    def __init__(self, parent, title: str = "Processing...",
                 cancelable: bool = False, development_mode: Optional[bool] = None):
        """
        Initialize the async operation runner.

        Args:
            parent: Parent window
            title: Dialog title
            cancelable: Whether operation can be cancelled
            development_mode: Enable development features
        """
        self.parent = parent
        self.progress_dialog = ProgressDialog(parent, title, cancelable, development_mode)
        self.operation_thread: Optional[Thread] = None

    def run_operation(self, operation_func: Callable,
                      callback_on_complete: Optional[Callable] = None,
                      callback_on_error: Optional[Callable] = None,
                      **operation_kwargs) -> None:
        """
        Run an operation asynchronously with progress tracking.

        Args:
            operation_func: Function to execute. Should accept 'progress_callback' parameter
            callback_on_complete: Called when operation completes successfully
            callback_on_error: Called when operation fails
            **operation_kwargs: Additional arguments for operation_func
        """

        def progress_callback(progress: int, status: str = "") -> bool:
            """Progress callback for the operation."""
            return self.progress_dialog.update_progress(progress, status)

        def status_callback(status: str) -> bool:
            """Status callback for the operation."""
            return self.progress_dialog.update_status(status)

        def operation_wrapper():
            """Wrapper to run operation and handle completion."""
            try:
                # Show progress dialog
                self.parent.after(0, lambda: self.progress_dialog.show("Initializing..."))

                # Run the operation
                result = operation_func(
                    progress_callback=progress_callback,
                    status_callback=status_callback,
                    **operation_kwargs
                )

                # Operation completed successfully
                if not self.progress_dialog.is_cancelled_flag():
                    self.parent.after(0, lambda: self._on_operation_complete(result, callback_on_complete))

            except Exception as e:
                # Operation failed
                self.parent.after(0, lambda error=e: self._on_operation_error(error, callback_on_error))

        # Set up cancellation if supported
        if self.progress_dialog.cancelable:
            def cancel_operation():
                # This would be called to cancel the operation
                # The operation_func should check progress_callback return value
                pass
            self.progress_dialog.set_cancel_callback(cancel_operation)

        # Start operation in background thread
        self.operation_thread = Thread(target=operation_wrapper, daemon=True)
        self.operation_thread.start()

    def _on_operation_complete(self, result: Any, callback: Optional[Callable]) -> None:
        """Handle operation completion."""
        self.progress_dialog.hide()

        if callback:
            try:
                callback(result)
            except Exception as e:
                print(f"Error in completion callback: {e}")
                messagebox.showerror("Callback Error", f"Error in completion callback: {e}")

    def _on_operation_error(self, error: Exception, callback: Optional[Callable]) -> None:
        """Handle operation error."""
        self.progress_dialog.hide()

        if callback:
            try:
                callback(error)
            except Exception as e:
                print(f"Error in error callback: {e}")
        else:
            # Default error handling
            messagebox.showerror("Operation Error", f"Operation failed: {error}")