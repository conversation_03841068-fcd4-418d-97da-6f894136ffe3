"""
Data Requirements module for SimpleDicomConverter.

This module defines the data requirements for each DICOM modality,
enabling selective data loading based on export needs.

The requirements are based on analysis of converter implementations:
- CT: Requires Patient + Plan + Planning CT (with pixel data)
- RTSTRUCT: Requires Patient + Plan + Planning CT + ROI data
- RTPLAN: Requires Patient + Plan + Trial data
- RTDOSE: Requires Patient + Plan + Trial + Planning CT + Dose data
"""

from dataclasses import dataclass
from typing import Dict


@dataclass
class ModalityRequirement:
    """Requirements for a specific DICOM modality."""
    
    name: str
    needs_patient: bool = True  # Always required
    needs_plan: bool = True     # Always required
    needs_trial: bool = False
    needs_planning_ct: bool = False
    needs_ct_pixel_data: bool = False
    needs_roi_data: bool = False
    needs_point_data: bool = False
    needs_dose_data: bool = False
    description: str = ""


# Modality requirement definitions based on converter analysis
MODALITY_REQUIREMENTS: Dict[str, ModalityRequirement] = {
    "CT": ModalityRequirement(
        name="CT",
        needs_planning_ct=True,
        needs_ct_pixel_data=True,
        description="CT Images require patient demographics, plan info, and planning CT with pixel data"
    ),
    
    "RTSTRUCT": ModalityRequirement(
        name="RTSTRUCT", 
        needs_planning_ct=True,
        needs_roi_data=True,
        needs_point_data=True,
        description="RT Structure Set requires patient demographics, plan info, planning CT for coordinate reference, and ROI/point data"
    ),
    
    "RTPLAN": ModalityRequirement(
        name="RTPLAN",
        needs_trial=True,
        description="RT Plan requires patient demographics, plan info, and trial data with beam parameters"
    ),
    
    "RTDOSE": ModalityRequirement(
        name="RTDOSE",
        needs_trial=True,
        needs_planning_ct=True,
        needs_dose_data=True,
        description="RT Dose requires patient demographics, plan info, trial data, planning CT for coordinate reference, and dose data"
    )
}


def get_consolidated_requirements(modalities: list[str]) -> Dict[str, bool]:
    """
    Get consolidated data requirements for multiple modalities.
    
    Args:
        modalities: List of DICOM modalities
        
    Returns:
        Dictionary of consolidated requirements (logical OR of all modality needs)
    """
    requirements = {
        "needs_patient": False,
        "needs_plan": False,
        "needs_trial": False,
        "needs_planning_ct": False,
        "needs_ct_pixel_data": False,
        "needs_roi_data": False,
        "needs_point_data": False,
        "needs_dose_data": False
    }
    
    for modality in modalities:
        modality = modality.upper()
        if modality in MODALITY_REQUIREMENTS:
            req = MODALITY_REQUIREMENTS[modality]
            
            # Combine using logical OR
            requirements["needs_patient"] |= req.needs_patient
            requirements["needs_plan"] |= req.needs_plan
            requirements["needs_trial"] |= req.needs_trial
            requirements["needs_planning_ct"] |= req.needs_planning_ct
            requirements["needs_ct_pixel_data"] |= req.needs_ct_pixel_data
            requirements["needs_roi_data"] |= req.needs_roi_data
            requirements["needs_point_data"] |= req.needs_point_data
            requirements["needs_dose_data"] |= req.needs_dose_data
    
    return requirements


def get_modality_dependencies() -> Dict[str, list[str]]:
    """
    Get dependency list for each modality.
    
    Returns:
        Dictionary mapping modalities to their data dependencies
    """
    dependencies = {}
    
    for modality, req in MODALITY_REQUIREMENTS.items():
        deps = ["Patient", "Plan"]  # Always required
        
        if req.needs_trial:
            deps.append("Trial")
        if req.needs_planning_ct:
            deps.append("Planning CT")
        if req.needs_ct_pixel_data:
            deps.append("CT Pixel Data")
        if req.needs_roi_data:
            deps.append("ROI Data")
        if req.needs_point_data:
            deps.append("Point Data")
        if req.needs_dose_data:
            deps.append("Dose Data")
            
        dependencies[modality] = deps
    
    return dependencies


def estimate_performance_improvement(modalities: list[str]) -> Dict[str, str]:
    """
    Estimate performance improvement for selective loading.
    
    Args:
        modalities: List of requested modalities
        
    Returns:
        Dictionary with performance estimates
    """
    all_modalities = set(MODALITY_REQUIREMENTS.keys())
    requested = set(m.upper() for m in modalities)
    skipped = all_modalities - requested
    
    estimates: Dict[str, str] = {
        "requested_modalities": len(requested),
        "total_modalities": len(all_modalities),
        "skipped_modalities": len(skipped),
    }
    
    # Rough performance improvement estimates from the plan
    if len(requested) == 1:
        if "CT" in requested:
            estimates["improvement"] = r"~60% faster (no ROI, trial, or dose loading)"
        elif "RTPLAN" in requested:
            estimates["improvement"] = r"~80% faster (no CT pixel data or dose loading)"
        elif "RTSTRUCT" in requested:
            estimates["improvement"] = r"~40% faster (no trial or dose loading)"
        elif "RTDOSE" in requested:
            estimates["improvement"] = r"~30% faster (no ROI loading)"
    elif len(requested) == 2:
        estimates["improvement"] = r"~30-50% faster (selective data loading)"
    elif len(requested) == 3:
        estimates["improvement"] = r"~20-30% faster (minimal optimization)"
    else:
        estimates["improvement"] = r"Full loading (all modalities requested)"
    
    return estimates