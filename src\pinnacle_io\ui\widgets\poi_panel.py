"""
POI panel widget for displaying and controlling POI visibility and rendering.
"""

import tkinter as tk
import ttkbootstrap as ttk
from typing import List, Optional, Callable, Dict, Any

from pinnacle_io.models.point import Point
from pinnacle_io.ui.constants import get_pinnacle_hex_color
from pinnacle_io.ui.widgets.base_overlay_panel import BaseOverlayPanel
from pinnacle_io.utils.coordinate_transforms import (
    transform_coordinates_for_points,
    needs_coordinate_transform
)


class POIPanel(BaseOverlayPanel):
    """Panel for displaying POIs (Points of Interest) with checkboxes to control visibility in CT viewer."""

    def __init__(self, parent, ct_viewer=None, on_poi_visibility_changed: Optional[Callable[[Point, bool], None]] = None):
        """
        Initialize the POI panel.

        Args:
            parent: Parent widget
            ct_viewer: Reference to CT viewer for coordinate system access
            on_poi_visibility_changed: Callback function called when POI visibility changes.
                                     Takes Point object and visibility boolean as parameters.
        """
        super().__init__(parent, ct_viewer)
        self.on_poi_visibility_changed = on_poi_visibility_changed

        # POI-specific data
        self.current_pois: List[Point] = []
        self.patient_position: Optional[str] = None
        self.poi_checkboxes: dict[int, tk.BooleanVar] = {}  # poi_id -> checkbox var
        self._transformation_applied: Optional[str] = None  # Track transformation state

    def _setup_ui(self):
        """Set up the POI panel UI components."""
        # Title
        title_label = ttk.Label(
            self,
            text="Points of Interest",
            font=("Arial", 10, "bold")
        )
        title_label.pack(fill=tk.X, padx=5, pady=(5, 10))

        # Create scrollable frame for POI list
        self._create_scrollable_poi_list()

        # Control buttons frame
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=5, pady=(5, 0))

        # Show All button
        show_all_btn = ttk.Button(
            control_frame,
            text="Show All",
            command=self._show_all_pois
        )
        show_all_btn.pack(side=tk.LEFT, padx=(0, 5))

        # Hide All button
        hide_all_btn = ttk.Button(
            control_frame,
            text="Hide All",
            command=self._hide_all_pois
        )
        hide_all_btn.pack(side=tk.LEFT)

    def _create_scrollable_poi_list(self):
        """Create a scrollable frame for the POI list."""
        # Create frame with scrollbar
        list_frame = ttk.Frame(self)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # Create canvas and scrollbar
        self.canvas = tk.Canvas(list_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.canvas.yview)

        # Create scrollable frame inside canvas
        self.scrollable_frame = ttk.Frame(self.canvas)

        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.canvas.bind("<Button-4>", self._on_mousewheel)
        self.canvas.bind("<Button-5>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        delta = 0
        if hasattr(event, 'delta'):
            delta = event.delta
        elif hasattr(event, 'num'):
            delta = -1 if event.num == 5 else 1

        if delta:
            self.canvas.yview_scroll(int(-1 * (delta / 120)), "units")

    def load_data(self, data: List[Point], patient_position: Optional[str] = None, **kwargs):
        """
        Load POI data into the panel.

        Args:
            data: List of Point objects to display
            patient_position: Patient position for coordinate transformation
            **kwargs: Additional parameters
        """
        self.current_pois = data
        self.patient_position = patient_position
        self.visibility_state.clear()
        self.color_state.clear()
        self.poi_checkboxes.clear()

        # Reset transformation state when new data is loaded
        self._transformation_applied = None

        # Apply coordinate transformations to current_pois
        self.apply_coordinate_transforms(patient_position)

        # Clear existing widgets
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # Create checkbox for each POI
        for poi in self.current_pois:
            self._create_poi_checkbox(poi)

        # Update canvas scroll region
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def _create_poi_checkbox(self, poi: Point):
        """Create a checkbox widget for a POI."""
        poi_id = poi.id if poi.id is not None else 0
        poi_name = self._get_poi_display_name(poi)

        # Create frame for this POI
        poi_frame = ttk.Frame(self.scrollable_frame)
        poi_frame.pack(fill=tk.X, pady=1)

        # Create checkbox variable
        checkbox_var = tk.BooleanVar(value=True)  # Default to visible
        self.poi_checkboxes[poi_id] = checkbox_var
        self.visibility_state[poi_id] = True

        # Parse and store POI color
        poi_color = self._parse_poi_color(poi)
        self.color_state[poi_id] = poi_color

        # Create checkbox
        checkbox = ttk.Checkbutton(
            poi_frame,
            text=poi_name,
            variable=checkbox_var,
            command=lambda p=poi: self._on_poi_checkbox_changed(p)
        )
        checkbox.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Add coordinate info if available
        if hasattr(poi, 'coordinates') and poi.coordinates:
            coord_info = self._get_coordinate_display(poi)
            if coord_info:
                coord_label = ttk.Label(
                    poi_frame,
                    text=coord_info,
                    font=("Arial", 8),
                    foreground="gray"
                )
                coord_label.pack(side=tk.RIGHT, padx=(5, 0))

        # Add color indicator if POI has color information
        if hasattr(poi, 'color') and poi.color:
            self._add_color_indicator(poi_frame, poi.color)

    def _get_poi_display_name(self, poi: Point) -> str:
        """Generate display name for POI."""
        if poi.name:
            base_name = poi.name
        elif poi.poi_interpreted_type:
            base_name = poi.poi_interpreted_type
        else:
            base_name = f"POI {poi.id}" if poi.id else "Unnamed POI"

        # Add type information if different from name
        if poi.poi_interpreted_type and poi.name and poi.poi_interpreted_type.lower() not in poi.name.lower():
            return f"{base_name} ({poi.poi_interpreted_type})"

        return base_name

    def _get_coordinate_display(self, poi: Point) -> str:
        """Generate coordinate display string for POI."""
        x, y, z = poi.coordinates
        if all(coord is not None for coord in [x, y, z]):
            return f"({x:.1f}, {y:.1f}, {z:.1f})"
        return ""

    def _add_color_indicator(self, parent_frame: ttk.Frame, color_str: str):
        """Add a color indicator for the POI."""
        try:
            color = get_pinnacle_hex_color(color_str)

            # Create small colored frame
            color_frame = tk.Frame(
                parent_frame,
                width=12,
                height=12,
                bg=color,
                relief="solid",
                borderwidth=1
            )
            color_frame.pack(side=tk.RIGHT, padx=(5, 0))
            color_frame.pack_propagate(False)

        except Exception:
            # If color parsing fails, just skip the color indicator
            pass

    def _on_poi_checkbox_changed(self, poi: Point):
        """Handle POI checkbox state change."""
        poi_id = poi.id if poi.id is not None else 0

        if poi_id in self.poi_checkboxes:
            is_visible = self.poi_checkboxes[poi_id].get()
            self.visibility_state[poi_id] = is_visible

            # Call callback if provided
            if self.on_poi_visibility_changed:
                self.on_poi_visibility_changed(poi, is_visible)

            # Request CT viewer refresh
            self.request_refresh()

    def _show_all_pois(self):
        """Show all POIs."""
        for poi_id, checkbox_var in self.poi_checkboxes.items():
            checkbox_var.set(True)
            self.visibility_state[poi_id] = True

        # Trigger callbacks for all POIs
        if self.on_poi_visibility_changed:
            for poi in self.current_pois:
                self.on_poi_visibility_changed(poi, True)

        # Request refresh
        self.request_refresh()

    def _hide_all_pois(self):
        """Hide all POIs."""
        for poi_id, checkbox_var in self.poi_checkboxes.items():
            checkbox_var.set(False)
            self.visibility_state[poi_id] = False

        # Trigger callbacks for all POIs
        if self.on_poi_visibility_changed:
            for poi in self.current_pois:
                self.on_poi_visibility_changed(poi, False)

        # Request refresh
        self.request_refresh()

    def is_poi_visible(self, poi: Point) -> bool:
        """
        Check if a POI is currently visible.

        Args:
            poi: Point object to check

        Returns:
            bool: True if POI is visible, False otherwise
        """
        poi_id = poi.id if poi.id is not None else 0
        return self.poi_visibility.get(poi_id, False)

    def get_visible_pois(self) -> List[Point]:
        """
        Get list of currently visible POIs.

        Returns:
            List[Point]: List of visible Point objects
        """
        visible_pois = []
        for poi in self.current_pois:
            if self.is_poi_visible(poi):
                visible_pois.append(poi)
        return visible_pois

    def get_poi_at_slice(self, poi: Point, z_coordinate: float, tolerance: float = 0.5) -> bool:
        """
        Check if POI is at a specific Z-coordinate slice.

        Args:
            poi: Point object to check
            z_coordinate: Z-coordinate of the slice in millimeters
            tolerance: Tolerance for Z-coordinate matching in millimeters

        Returns:
            bool: True if POI is within tolerance of the specified slice
        """
        if poi.z_coord is not None:
            return abs(poi.z_coord - z_coordinate) <= tolerance
        return False

    def get_pois_for_slice(self, z_coordinate: float, tolerance: float = 0.5) -> List[Point]:
        """
        Get POIs that are at a specific Z-coordinate slice.

        Args:
            z_coordinate: Z-coordinate of the slice in millimeters
            tolerance: Tolerance for Z-coordinate matching in millimeters

        Returns:
            List[Point]: List of Point objects at the specified slice
        """
        matching_pois = []
        for poi in self.current_pois:
            if self.is_poi_visible(poi) and self.get_poi_at_slice(poi, z_coordinate, tolerance):
                matching_pois.append(poi)
        return matching_pois

    # Methods required by BaseOverlayPanel interface

    def render_overlays(self, canvas: tk.Canvas, current_slice_z: float, display_params: Dict[str, Any]):
        """
        Render POI overlays on the CT viewer canvas.

        Args:
            canvas: Canvas to draw on
            current_slice_z: Current slice Z-coordinate
            display_params: Display parameters (scale, center, etc.)
        """
        # Performance optimization: check if display parameters changed
        params_changed = self._cache_display_params(display_params)

        # Use current_pois which now contains transformed data
        if not self.current_pois:
            return

        # Check if we have valid display parameters
        if not display_params or display_params['scale'] <= 0:
            return

        # Clear existing POI overlays
        self.clear_overlays(canvas, "poi_overlay")

        # Calculate tolerance for slice matching
        if 'pixel_spacing' in display_params:
            tolerance = display_params['pixel_spacing'][2] * 0.6  # Slightly more than half slice thickness
        else:
            tolerance = 0.5  # Default tolerance

        # Batch visible POIs for better performance
        visible_slice_pois = [
            (poi, poi.id if poi.id is not None else 0)
            for poi in self.current_pois
            if (poi.z_coord is not None and
                self.visibility_state.get(poi.id if poi.id is not None else 0, False) and
                abs(poi.z_coord - current_slice_z) <= tolerance)
        ]

        # Draw POIs that are close to the current slice
        for poi, poi_id in visible_slice_pois:
            poi_color = self.color_state.get(poi_id, "#0080ff")
            self._draw_poi_on_canvas(poi, display_params, poi_color, canvas)

    def update_visibility(self, item_id: Any, is_visible: bool):
        """
        Update visibility of specific POI.

        Args:
            item_id: POI ID or POI object
            is_visible: Whether POI should be visible
        """
        if isinstance(item_id, Point):
            poi_id = item_id.id if item_id.id is not None else 0
        else:
            poi_id = item_id

        self.visibility_state[poi_id] = is_visible

        # Update checkbox if it exists
        if poi_id in self.poi_checkboxes:
            self.poi_checkboxes[poi_id].set(is_visible)

        # Request refresh
        self.request_refresh()

    def get_visible_items(self) -> List[Point]:
        """
        Get list of currently visible POIs.

        Returns:
            List of visible Point objects
        """
        visible_pois = []

        for poi in self.current_pois:
            poi_id = poi.id if poi.id is not None else 0
            if self.visibility_state.get(poi_id, False):
                visible_pois.append(poi)

        return visible_pois

    def apply_coordinate_transforms(self, patient_position: Optional[str]):
        """
        Apply coordinate transformations for patient position.

        Args:
            patient_position: Patient position for coordinate transformation
        """
        # Skip if already transformed to this position (avoid redundant operations)
        if self._transformation_applied == patient_position:
            return

        self.patient_position = patient_position

        # Handle POI transformations
        if not self.current_pois or not patient_position:
            # No POIs or patient position available, keep current data unchanged
            return
        else:
            try:
                # Check if coordinate transformation is needed for this patient position
                if needs_coordinate_transform(patient_position):
                    print(f"Applying POI coordinate transformation for patient position: {patient_position}")
                    # NEW: Use in-place transformation instead of creating copies
                    transform_coordinates_for_points(self.current_pois, patient_position, inplace=True)
                    self._transformation_applied = patient_position
                else:
                    print(f"No POI coordinate transformation needed for patient position: {patient_position}")
                    # No transformation needed, but still track state
                    self._transformation_applied = patient_position

            except Exception as e:
                print(f"Error applying POI coordinate transformation: {e}")
                # Keep original data if transformation fails

    def _draw_poi_on_canvas(self, poi: Point, display_params: Dict[str, Any], color: str, canvas: tk.Canvas):
        """Draw a single POI on the canvas."""
        try:
            # Convert POI world coordinates to pixel coordinates
            if poi.x_coord is None or poi.y_coord is None:
                return

            display_coords = self.world_to_display_coordinates(
                poi.x_coord, poi.y_coord, poi.z_coord, display_params
            )
            if not display_coords:
                return

            display_x, display_y = display_coords

            # Determine POI size based on scale and type
            zoom_factor = display_params.get('zoom_factor', 1.0)
            poi_size = max(4, min(12, int(6 * zoom_factor)))
            crosshair_size = poi_size * 1.5

            # Draw different shapes based on POI type, but all include crosshairs
            poi_type = getattr(poi, 'poi_interpreted_type', 'POINT')

            if poi_type == 'ISOCENTER':
                # Draw larger crosshair for isocenter
                crosshair_size = poi_size * 2.5
                canvas.create_line(
                    display_x - crosshair_size, display_y,
                    display_x + crosshair_size, display_y,
                    fill=color, width=2, tags="poi_overlay"
                )
                canvas.create_line(
                    display_x, display_y - crosshair_size,
                    display_x, display_y + crosshair_size,
                    fill=color, width=2, tags="poi_overlay"
                )
                # Center circle
                canvas.create_oval(
                    display_x - poi_size, display_y - poi_size,
                    display_x + poi_size, display_y + poi_size,
                    outline=color, fill="", width=2, tags="poi_overlay"
                )
            elif poi_type in ['DOSE_REFERENCE', 'REFERENCE']:
                # Draw diamond with crosshair for dose reference points
                canvas.create_polygon(
                    display_x, display_y - poi_size,      # top
                    display_x + poi_size, display_y,      # right
                    display_x, display_y + poi_size,      # bottom
                    display_x - poi_size, display_y,      # left
                    outline=color, fill="", width=2, tags="poi_overlay"
                )
                # Add crosshair
                canvas.create_line(
                    display_x - crosshair_size, display_y,
                    display_x + crosshair_size, display_y,
                    fill=color, width=1, tags="poi_overlay"
                )
                canvas.create_line(
                    display_x, display_y - crosshair_size,
                    display_x, display_y + crosshair_size,
                    fill=color, width=1, tags="poi_overlay"
                )
            else:
                # Draw circle with crosshair for general POIs
                canvas.create_oval(
                    display_x - poi_size, display_y - poi_size,
                    display_x + poi_size, display_y + poi_size,
                    outline=color, fill="", width=2, tags="poi_overlay"
                )
                # Add crosshair
                canvas.create_line(
                    display_x - crosshair_size, display_y,
                    display_x + crosshair_size, display_y,
                    fill=color, width=1, tags="poi_overlay"
                )
                canvas.create_line(
                    display_x, display_y - crosshair_size,
                    display_x, display_y + crosshair_size,
                    fill=color, width=1, tags="poi_overlay"
                )

            # Add POI label if it has a name
            if hasattr(poi, 'name') and poi.name:
                # Position label slightly offset from POI
                label_x = display_x + poi_size + 5
                label_y = display_y - poi_size - 2

                # Create text with background for better readability
                canvas.create_text(
                    label_x, label_y,
                    text=poi.name,
                    fill=color,
                    font=("Arial", 8, "bold"),
                    anchor="w",
                    tags="poi_overlay"
                )

        except Exception as e:
            print(f"Error drawing POI: {e}")

    def _parse_poi_color(self, poi: Point) -> str:
        """Parse POI color from various possible formats."""
        try:
            if hasattr(poi, 'color') and poi.color:
                return get_pinnacle_hex_color(poi.color)
            else:
                # Default colors for POIs without color info - use blue tones
                poi_id = poi.id if poi.id is not None else 0
                default_colors = ["#0080ff", "#0040ff", "#0000ff", "#4080ff", "#8080ff", "#40c0ff"]
                return default_colors[poi_id % len(default_colors)]
        except Exception:
            return "#0080ff"  # Default to blue if parsing fails

    # Legacy compatibility methods
    def is_poi_visible(self, poi: Point) -> bool:
        """Legacy method: Check if a POI is currently visible."""
        poi_id = poi.id if poi.id is not None else 0
        return self.visibility_state.get(poi_id, False)

    def get_visible_pois(self) -> List[Point]:
        """Legacy method: Get list of currently visible POIs."""
        return self.get_visible_items()

    def clear_data(self):
        """Clear all data from the POI panel."""
        # Clear POI data
        self.current_pois = []
        self.patient_position = None
        self.poi_checkboxes.clear()
        self.visibility_state.clear()
        self.color_state.clear()
        self._transformation_applied = None  # Reset transformation state

        # Clear existing widgets
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # Update canvas scroll region
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))