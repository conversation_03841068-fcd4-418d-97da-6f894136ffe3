"""
Tests for the PinnacleReader API class.

This module contains comprehensive tests for the PinnacleReader class which provides
the unified interface for reading Pinnacle data from various sources.
"""

import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock, patch

from pinnacle_io.api import <PERSON><PERSON><PERSON><PERSON>eader
from pinnacle_io.services.file_reader import <PERSON><PERSON><PERSON>er
from pinnacle_io.services.tar_file_reader import TarFileReader
from pinnacle_io.services.zip_file_reader import ZipFileReader
from pinnacle_io.models.institution import Institution


class TestPinnacleReaderInitialization:
    """Test PinnacleReader initialization and service selection."""

    def test_init_with_directory_path(self):
        """Test initialization with directory path."""
        test_dir = "tests/test_data/01"
        if os.path.exists(test_dir):
            reader = PinnacleReader(test_dir)
            assert reader.path == Path(test_dir)
            assert isinstance(reader._reader_service, FileReader)

    def test_init_with_tar_file(self):
        """Test initialization with tar file."""
        tar_file = "tests/test_data/01.tar.gz"
        if os.path.exists(tar_file):
            reader = PinnacleReader(tar_file)
            assert reader.path == Path(tar_file)
            assert isinstance(reader._reader_service, TarFileReader)

    def test_init_with_zip_file(self):
        """Test initialization with zip file."""
        zip_file = "tests/test_data/01.zip"
        if os.path.exists(zip_file):
            reader = PinnacleReader(zip_file)
            assert reader.path == Path(zip_file)
            assert isinstance(reader._reader_service, ZipFileReader)

    def test_init_with_tgz_file(self):
        """Test initialization with .tgz file."""
        with tempfile.NamedTemporaryFile(suffix=".tgz", delete=False) as tmp:
            tmp_path = tmp.name
        try:
            # Create a minimal tar file for testing
            import tarfile

            with tarfile.open(tmp_path, "w:gz") as tar:
                pass

            reader = PinnacleReader(tmp_path)
            assert isinstance(reader._reader_service, TarFileReader)
        finally:
            os.unlink(tmp_path)

    def test_init_with_tar_file_extension(self):
        """Test initialization with .tar file."""
        with tempfile.NamedTemporaryFile(suffix=".tar", delete=False) as tmp:
            tmp_path = tmp.name
        try:
            # Create a minimal tar file for testing
            import tarfile

            with tarfile.open(tmp_path, "w") as tar:
                pass

            reader = PinnacleReader(tmp_path)
            assert isinstance(reader._reader_service, TarFileReader)
        finally:
            os.unlink(tmp_path)

    def test_init_with_pathlib_path(self):
        """Test initialization with pathlib.Path object."""
        test_dir = "tests/test_data/01"
        if os.path.exists(test_dir):
            reader = PinnacleReader(Path(test_dir))
            assert reader.path == Path(test_dir)
            assert isinstance(reader._reader_service, FileReader)

    def test_init_with_nonexistent_path(self):
        """Test initialization with non-existent path raises FileNotFoundError."""
        with pytest.raises(FileNotFoundError, match="Path not found"):
            PinnacleReader("/nonexistent/path")

    def test_init_with_unsupported_file_type(self):
        """Test initialization with unsupported file type raises ValueError."""
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp:
            tmp_path = tmp.name
        try:
            with pytest.raises(ValueError, match="Unsupported file format"):
                PinnacleReader(tmp_path)
        finally:
            os.unlink(tmp_path)


class TestPinnacleReaderCrossServiceConsistency:
    """Test that PinnacleReader provides consistent behavior across different services."""

    def test_get_institution_consistency(self):
        """Test that get_institution returns Institution objects consistently across services."""
        test_sources = []

        # Add available test sources
        if os.path.exists("tests/test_data/01"):
            test_sources.append(("Directory", "tests/test_data/01"))
        if os.path.exists("tests/test_data/01.tar.gz"):
            test_sources.append(("Tar", "tests/test_data/01.tar.gz"))
        if os.path.exists("tests/test_data/01.zip"):
            test_sources.append(("Zip", "tests/test_data/01.zip"))

        if not test_sources:
            pytest.skip("No test data available")

        institutions = {}
        for source_type, source_path in test_sources:
            try:
                reader = PinnacleReader(source_path)
                institution = reader.get_institution()
                institutions[source_type] = institution

                # Assert all return Institution objects
                assert isinstance(institution, Institution), f"{source_type} should return Institution object"
                assert hasattr(institution, "name"), f"{source_type} Institution should have name attribute"

            except Exception as e:
                pytest.fail(f"Failed to read from {source_type}: {e}")

        # Check that all sources return the same institution data
        if len(institutions) > 1:
            institution_names = [inst.name for inst in institutions.values()]
            assert all(
                name == institution_names[0] for name in institution_names
            ), f"Institution names should be consistent: {institution_names}"

    def test_get_patient_consistency(self):
        """Test that get_patient behavior is consistent across services."""
        test_sources = []

        # Add available test sources
        if os.path.exists("tests/test_data/01"):
            test_sources.append(("Directory", "tests/test_data/01"))
        if os.path.exists("tests/test_data/01.tar.gz"):
            test_sources.append(("Tar", "tests/test_data/01.tar.gz"))
        if os.path.exists("tests/test_data/01.zip"):
            test_sources.append(("Zip", "tests/test_data/01.zip"))

        if not test_sources:
            pytest.skip("No test data available")

        # Test with known patient IDs from test data
        institution_id = 1
        patient_id = 1
        mount_id = 0

        for source_type, source_path in test_sources:
            try:
                reader = PinnacleReader(source_path)
                patient = reader.get_patient(institution_id, patient_id, mount_id)

                # All services should return the same type of object
                assert patient is not None, f"{source_type} should return patient object"
                assert hasattr(
                    patient, "last_and_first_name"
                ), f"{source_type} Patient should have last_and_first_name attribute"

            except Exception as e:
                # Some services might not support this yet, so we'll note but not fail
                print(f"Note: {source_type} get_patient failed: {e}")

    def test_service_method_delegation(self):
        """Test that all PinnacleReader methods delegate to the underlying service."""
        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        reader = PinnacleReader(test_dir)

        # Mock the underlying service to verify delegation
        mock_service = Mock()
        reader._reader_service = mock_service

        # Test all delegated methods
        reader.get_institution()
        mock_service.get_institution.assert_called_once()

        reader.get_patient(1, 1)
        mock_service.get_patient.assert_called_once_with(1, 1, 0)

        reader.get_patient(2, 3, 1)
        mock_service.get_patient.assert_called_with(2, 3, 1)

        reader.get_image_set(1, 1, 0)
        mock_service.get_image_set.assert_called_once_with(1, 1, 0, 0)

        reader.get_image_info(1, 2, 0)
        mock_service.get_image_info.assert_called_once_with(1, 2, 0, 0)

        reader.get_trials(1, 1, 0)
        mock_service.get_trials.assert_called_once_with(1, 1, 0, 0)

        reader.get_rois(1, 1, 0)
        mock_service.get_rois.assert_called_once_with(1, 1, 0, 0)

        reader.get_points(1, 1, 0)
        mock_service.get_points.assert_called_once_with(1, 1, 0, 0)

        reader.get_patient_setup(1, 1, 0)
        mock_service.get_patient_setup.assert_called_once_with(1, 1, 0, 0)

        reader.get_machines(1, 1, 0)
        mock_service.get_machines.assert_called_once_with(1, 1, 0, 0)

        from pinnacle_io.models import Trial
        test_trial = Trial()
        reader.get_dose(1, 1, 0, test_trial)
        mock_service.get_dose.assert_called_once_with(1, 1, 0, test_trial, 0)


class TestPinnacleReaderImageSlice:
    """Test PinnacleReader get_image_slice method."""

    def test_get_image_slice_delegation(self):
        """Test that get_image_slice properly delegates to service layer."""
        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        reader = PinnacleReader(test_dir)

        # Mock the service layer
        import numpy as np

        mock_service = Mock()
        mock_slice_data = np.zeros((512, 512), dtype=np.uint16)
        mock_service.get_image_slice.return_value = mock_slice_data
        reader._reader_service = mock_service

        # Call get_image_slice
        result = reader.get_image_slice(institution=1, patient=1, image_set=0, slice_index=50)

        # Verify service was called with correct parameters
        mock_service.get_image_slice.assert_called_once_with(1, 1, 0, 50, None, 0)

        # Verify result
        assert result is mock_slice_data

    def test_get_image_slice_with_defaults(self):
        """Test get_image_slice with default arguments."""
        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        reader = PinnacleReader(test_dir)

        # Mock the service layer
        import numpy as np

        mock_service = Mock()
        mock_slice_data = np.zeros((512, 512), dtype=np.uint16)
        mock_service.get_image_slice.return_value = mock_slice_data
        reader._reader_service = mock_service

        # Call get_image_slice with defaults (mount_id defaults to 0)
        result = reader.get_image_slice(institution=1, patient=1, image_set=0, slice_index=0)

        # Verify service was called with defaults
        mock_service.get_image_slice.assert_called_once_with(1, 1, 0, 0, None, 0)

        # Verify result
        assert result is mock_slice_data

    def test_get_image_slice_error_propagation(self):
        """Test that errors from get_image_slice are properly propagated."""
        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        reader = PinnacleReader(test_dir)

        # Mock the service to raise an exception
        mock_service = Mock()
        mock_service.get_image_slice.side_effect = FileNotFoundError("Image set not found")
        reader._reader_service = mock_service

        # The exception should be propagated
        with pytest.raises(FileNotFoundError, match="Image set not found"):
            reader.get_image_slice(institution=99, patient=99, image_set=99, slice_index=0)

    def test_get_image_slice_integration(self):
        """Integration test for get_image_slice with real test data."""
        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        reader = PinnacleReader(test_dir)

        try:
            # Try to get an image slice from the test data
            slice_data = reader.get_image_slice(institution=1, patient=1, image_set=0, slice_index=0)

            # Verify result is a numpy array
            import numpy as np

            assert isinstance(slice_data, np.ndarray)
            assert slice_data.dtype == np.uint16
            assert len(slice_data.shape) == 2  # Should be 2D

        except (FileNotFoundError, ValueError) as e:
            # If the specific test data doesn't exist or doesn't have image data, that's okay
            pytest.skip(f"Test data doesn't contain required image data: {e}")


class TestPinnacleReaderErrorHandling:
    """Test error handling in PinnacleReader."""

    def test_service_initialization_error_propagation(self):
        """Test that service initialization errors are properly propagated."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a directory that exists but might cause service init issues
            reader = PinnacleReader(temp_dir)

            # The reader should initialize successfully even with empty directory
            assert reader._reader_service is not None
            assert isinstance(reader._reader_service, FileReader)

    def test_method_error_propagation(self):
        """Test that method errors from services are properly propagated."""
        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        reader = PinnacleReader(test_dir)

        # Mock the service to raise an exception
        mock_service = Mock()
        mock_service.get_institution.side_effect = FileNotFoundError("Test error")
        reader._reader_service = mock_service

        # The exception should be propagated
        with pytest.raises(FileNotFoundError, match="Test error"):
            reader.get_institution()

    def test_invalid_patient_path_handling(self):
        """Test handling of invalid patient paths."""
        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        reader = PinnacleReader(test_dir)

        # Test with invalid patient IDs
        with pytest.raises(FileNotFoundError):
            reader.get_patient(institution=999, patient=999)


class TestPinnacleReaderDefaultArguments:
    """Test PinnacleReader methods with default arguments."""

    def test_get_methods_with_defaults(self):
        """Test that all get methods work with their default arguments."""
        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        reader = PinnacleReader(test_dir)

        # Mock the service to avoid actual file operations
        mock_service = Mock()
        reader._reader_service = mock_service

        # Test methods with default arguments
        reader.get_patient(1, 1)
        mock_service.get_patient.assert_called_with(1, 1, 0)

        reader.get_image_set(1, 1, 0)
        mock_service.get_image_set.assert_called_with(1, 1, 0, 0)

        reader.get_image_info(1, 1, 0)
        mock_service.get_image_info.assert_called_with(1, 1, 0, 0)

        reader.get_trials(1, 1, 0)
        mock_service.get_trials.assert_called_with(1, 1, 0, 0)

        reader.get_rois(1, 1, 0)
        mock_service.get_rois.assert_called_with(1, 1, 0, 0)

        reader.get_points(1, 1, 0)
        mock_service.get_points.assert_called_with(1, 1, 0, 0)

        reader.get_patient_setup(1, 1, 0)
        mock_service.get_patient_setup.assert_called_with(1, 1, 0, 0)

        reader.get_machines(1, 1, 0)
        mock_service.get_machines.assert_called_with(1, 1, 0, 0)

        from pinnacle_io.models import Trial
        test_trial = Trial()
        reader.get_dose(1, 1, 0, test_trial)
        mock_service.get_dose.assert_called_with(1, 1, 0, test_trial, 0)

        # Test get_image_slice with defaults (requires special handling due to ImageSetReader)
        with patch("pinnacle_io.readers.image_set_reader.ImageSetReader") as mock_reader_class:
            import numpy as np

            mock_reader_class.read_image_slice.return_value = np.zeros((512, 512), dtype=np.uint16)
            reader.get_image_slice(1, 1, 0, 0)
            mock_service.get_image_slice.assert_called_with(1, 1, 0, 0, None, 0)


class TestPinnacleReaderIntegration:
    """Integration tests for PinnacleReader with real test data."""

    def test_full_workflow_directory(self):
        """Test complete workflow with directory source."""
        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        reader = PinnacleReader(test_dir)

        # Test institution loading
        institution = reader.get_institution()
        assert isinstance(institution, Institution)
        assert institution.name

        # Test patient loading (if patient data exists)
        if hasattr(institution, "patient_lite_list") and institution.patient_lite_list:
            patient_lite = institution.patient_lite_list[0]
            # Use patient_lite.patient_id and institution.institution_id
            if patient_lite.patient_id is not None and institution.institution_id is not None:
                patient = reader.get_patient(institution.institution_id, patient_lite.patient_id)
                assert patient is not None

    def test_full_workflow_tar_archive(self):
        """Test complete workflow with tar archive source."""
        tar_file = "tests/test_data/01.tar.gz"
        if not os.path.exists(tar_file):
            pytest.skip("Test data not available")

        reader = PinnacleReader(tar_file)

        # Test institution loading
        institution = reader.get_institution()
        assert isinstance(institution, Institution)
        assert institution.name

        # Verify service type
        assert isinstance(reader._reader_service, TarFileReader)

    def test_full_workflow_zip_archive(self):
        """Test complete workflow with zip archive source."""
        zip_file = "tests/test_data/01.zip"
        if not os.path.exists(zip_file):
            pytest.skip("Test data not available")

        reader = PinnacleReader(zip_file)

        # Test institution loading
        institution = reader.get_institution()
        assert isinstance(institution, Institution)
        assert institution.name

        # Verify service type
        assert isinstance(reader._reader_service, ZipFileReader)

    def test_archive_vs_directory_consistency(self):
        """Test that archive and directory sources produce consistent results."""
        sources = {
            "directory": "tests/test_data/01",
            "tar": "tests/test_data/01.tar.gz",
            "zip": "tests/test_data/01.zip",
        }

        available_sources = {k: v for k, v in sources.items() if os.path.exists(v)}

        if len(available_sources) < 2:
            pytest.skip("Need at least 2 data sources for consistency testing")

        readers = {}
        institutions = {}

        for source_type, source_path in available_sources.items():
            readers[source_type] = PinnacleReader(source_path)
            institutions[source_type] = readers[source_type].get_institution()

        # All should return Institution objects
        for source_type, institution in institutions.items():
            assert isinstance(institution, Institution), f"{source_type} should return Institution"

        # All should have the same institution name
        names = [inst.name for inst in institutions.values()]
        assert all(name == names[0] for name in names), f"Institution names should match: {names}"

        # Test service types are correct
        if "directory" in readers:
            assert isinstance(readers["directory"]._reader_service, FileReader)
        if "tar" in readers:
            assert isinstance(readers["tar"]._reader_service, TarFileReader)
        if "zip" in readers:
            assert isinstance(readers["zip"]._reader_service, ZipFileReader)


class TestPinnacleReaderPerformance:
    """Basic performance tests for PinnacleReader."""

    def test_initialization_performance(self):
        """Test that initialization is fast."""
        import time

        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        start_time = time.time()
        reader = PinnacleReader(test_dir)
        init_time = time.time() - start_time

        # Initialization should be under 100ms
        assert init_time < 0.1, f"Initialization took {init_time:.3f}s, expected < 0.1s"

        # Service should be initialized
        assert reader._reader_service is not None

    def test_repeated_access_performance(self):
        """Test that repeated access to the same data is efficient."""
        test_dir = "tests/test_data/01"
        if not os.path.exists(test_dir):
            pytest.skip("Test data not available")

        reader = PinnacleReader(test_dir)

        import time

        # First access
        start_time = time.time()
        institution1 = reader.get_institution()
        first_time = time.time() - start_time

        # Second access
        start_time = time.time()
        institution2 = reader.get_institution()
        second_time = time.time() - start_time

        # Both should return Institution objects
        assert isinstance(institution1, Institution)
        assert isinstance(institution2, Institution)

        # Should have same name
        assert institution1.name == institution2.name

        # Performance should be reasonable (under 1 second each)
        assert first_time < 1.0, f"First access took {first_time:.3f}s"
        assert second_time < 1.0, f"Second access took {second_time:.3f}s"
