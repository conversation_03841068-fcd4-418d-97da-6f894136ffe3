"""
SQLAlchemy composite types for spatial coordinates.

This module provides composite column types for handling 3D spatial data in SQLAlchemy models.
Each type maps three individual database columns to a single Python object that represents
a point or dimension in 3D space.
"""

from typing import Any, Generic, TypeVar, cast, override
from collections.abc import Sequence
from sqlalchemy import Integer, Float as SQLFloat
from sqlalchemy.types import TypeDecorator, TypeEngine

# from sqlalchemy.ext.mutable import MutableComposite

from pinnacle_io.models.types import (
    Dimension,
    VoxelSize,
    Coordinate,
)

# Type variable for the spatial type classes
T = TypeVar("T", Dimension, VoxelSize, Coordinate)
V = TypeVar("V", int, float)


class SpatialComposite(TypeDecorator[T], Generic[T]):
    """
    Base class for composite types that handle 3D spatial data.

    This class provides common functionality for mapping between database columns
    and Python spatial objects (Dimension, VoxelSize, Coordinate). It handles
    type conversion, comparison operations, and proper None handling.

    Type Parameters:
        T: The spatial type to use (Dimension, VoxelSize, or Coordinate)

    Attributes:
        x_col_name (str): Name of the X component column
        y_col_name (str): Name of the Y component column
        z_col_name (str): Name of the Z component column
        value_type (Type): SQLAlchemy type for the components (Integer or Float)
        spatial_type (type[T]): Python type for the spatial object
    """

    impl: TypeEngine[Any] | type[TypeEngine[Any]] = SQLFloat[Any]

    def __init__(
        self,
        x_col_name: str,
        y_col_name: str,
        z_col_name: str,
        value_type: type[Integer] | type[SQLFloat[Any]],
        spatial_type: type[T],
    ) -> None:
        """
        Initialize the composite type.

        Args:
            x_col_name: Name of the X component column
            y_col_name: Name of the Y component column
            z_col_name: Name of the Z component column
            value_type: SQLAlchemy type for the components (Integer or Float)
            spatial_type: Python type for the spatial object
        """
        super().__init__()
        self.x_col_name: str = x_col_name
        self.y_col_name: str = y_col_name
        self.z_col_name: str = z_col_name
        self.value_type: type[Integer] | type[SQLFloat[Any]] = value_type
        self.spatial_type: type[T] = spatial_type

    @override
    def process_bind_param(self, value: T | None, dialect: Any) -> tuple[int | float | None, int | float | None, int | float | None]:
        """Process Python value into a format suitable for the database."""
        if value is None:
            return None, None, None
        return value.x, value.y, value.z

    @override
    def process_result_value(self, value: Any, dialect: Any) -> T | None:
        """Process database value into Python format."""
        if not isinstance(value, Sequence):
            return None
        value_seq = cast(Sequence[Any], value)
        if len(value_seq) != 3 or None in value_seq or not all(isinstance(k, (int, float)) for k in value_seq):
            return None

        # Safe type conversion since we checked all values are int or float
        x, y, z = cast(tuple[int | float, int | float, int | float], value_seq)

        try:
            # Convert to correct type based on spatial_type
            if self.spatial_type == Dimension:
                # For Dimension, we need to ensure positive integers
                # First convert to float to handle both int and float inputs
                x_float = float(x)
                y_float = float(y)
                z_float = float(z)

                # Round to nearest integer and ensure positive
                x_int = max(1, int(round(x_float)))
                y_int = max(1, int(round(y_float)))
                z_int = max(1, int(round(z_float)))

                # Create Dimension with validated integer values
                dim = Dimension(x_int, y_int, z_int)
                return cast(T, dim)
            else:
                # Cast x, y, z to int or float based on self.value_type
                if self.value_type == Integer:
                    return self.spatial_type(int(x), int(y), int(z))  # type: ignore
                else:
                    return self.spatial_type(float(x), float(y), float(z))  # type: ignore
        except (ValueError, TypeError):
            # If validation fails, return None
            return None

    @override
    def compare_values(self, x: Any, y: Any) -> bool:
        """Compare two values for equality."""
        if x is None or y is None:
            return x is y
        return x == y


class DimensionComposite(SpatialComposite[Dimension]):
    """
    Composite type for mapping three Integer columns to a Dimension object.

    This type handles the conversion between database columns (DimensionX, DimensionY, DimensionZ)
    and Python Dimension objects, ensuring proper type validation and None handling.
    """

    def __init__(self) -> None:
        """Initialize the DimensionComposite type."""
        super().__init__(
            "DimensionX",
            "DimensionY",
            "DimensionZ",
            Integer,
            Dimension,
        )


class VoxelSizeComposite(SpatialComposite[VoxelSize]):
    """
    Composite type for mapping three Float columns to a VoxelSize object.

    This type handles the conversion between database columns (VoxelSizeX, VoxelSizeY, VoxelSizeZ)
    and Python VoxelSize objects, ensuring proper type validation and None handling.
    """

    def __init__(self) -> None:
        """Initialize the VoxelSizeComposite type."""
        super().__init__(
            "VoxelSizeX",
            "VoxelSizeY",
            "VoxelSizeZ",
            SQLFloat[Any],
            VoxelSize,
        )


class CoordinateComposite(SpatialComposite[Coordinate]):
    """
    Composite type for mapping three Float columns to a Coordinate object.

    This type handles the conversion between database columns and Python Coordinate objects,
    ensuring proper type validation and None handling.
    """

    def __init__(self, prefix: str = "") -> None:
        """
        Initialize the CoordinateComposite type.

        Args:
            prefix: Optional prefix for the column names (e.g., "Origin" for "OriginX")
        """
        super().__init__(
            f"{prefix}X",
            f"{prefix}Y",
            f"{prefix}Z",
            SQLFloat[Any],
            Coordinate,
        )
