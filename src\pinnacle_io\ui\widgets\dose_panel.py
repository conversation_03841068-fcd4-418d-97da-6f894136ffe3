"""
Dose panel widget for displaying and controlling isodose line visibility and rendering.
"""

import tkinter as tk
import ttkbootstrap as ttk
from typing import List, Optional, Callable, Dict, Any
import numpy as np
from skimage import measure

from pinnacle_io.models.dose import Dose
from pinnacle_io.utils.coordinate_transforms import transform_dose
from pinnacle_io.ui.widgets.base_overlay_panel import BaseOverlayPanel


class DosePanel(BaseOverlayPanel):
    """Panel for displaying isodose levels with checkboxes to control visibility in CT viewer."""

    def __init__(self, parent, ct_viewer=None, on_isodose_visibility_changed: Optional[Callable[[float, bool], None]] = None, app=None):
        """
        Initialize the dose panel.

        Args:
            parent: Parent widget
            ct_viewer: Reference to CT viewer for coordinate system access
            on_isodose_visibility_changed: Callback function called when isodose visibility changes.
                                         Takes relative dose level (float) and visibility boolean as parameters.
            app: Main application instance for CT viewer communication
        """
        self.on_isodose_visibility_changed = on_isodose_visibility_changed
        self.app = app

        # Dose-specific data
        self.current_dose: Optional[Dose] = None
        self.patient_position: Optional[str] = None
        self.max_dose_value: float = 100.0  # Default max dose in cGy
        self.absolute_dose_value: float = 100.0  # Current absolute dose reference in cGy
        self._transformation_applied: Optional[str] = None  # Track transformation state

        # Isodose levels (relative percentages)
        self.isodose_levels = [110, 105, 100, 98, 95, 90, 80, 70, 50, 30]
        self.isodose_checkboxes: dict[float, tk.BooleanVar] = {}  # level -> checkbox var
        self.isodose_colors = {
            110: "#ff0000",  # Red for hot spots
            105: "#ff8000",  # Orange
            100: "#ffff00",  # Yellow for prescription dose
            98: "#80ff00",   # Yellow-green
            95: "#00ff00",   # Green
            90: "#00ff80",   # Green-cyan
            80: "#00ffff",   # Cyan
            70: "#0080ff",   # Blue-cyan
            50: "#0000ff",   # Blue
            30: "#8000ff",   # Purple
        }

        # Initialize base overlay panel, which calls the _setup_ui method below
        super().__init__(parent, ct_viewer)

    def _setup_ui(self):
        """Set up the dose panel UI components."""
        # Title
        title_label = ttk.Label(
            self,
            text="Dose",
            font=("Arial", 10, "bold")
        )
        title_label.pack(fill=tk.X, padx=5, pady=(5, 10))

        # Dose grid information frame
        self.dose_grid_frame = ttk.LabelFrame(self, text="Dose Grid Information", padding=5)
        self.dose_grid_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Create dose grid info labels
        self._create_dose_grid_info_labels()

        # Absolute dose input frame
        dose_input_frame = ttk.LabelFrame(self, text="Absolute Dose Reference", padding=5)
        dose_input_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Dose input with label
        input_container = ttk.Frame(dose_input_frame)
        input_container.pack(fill=tk.X)

        ttk.Label(input_container, text="Dose (cGy):").pack(side=tk.LEFT)

        # Dose entry field
        self.dose_var = tk.StringVar(value=str(self.absolute_dose_value))
        self.dose_entry = ttk.Entry(
            input_container,
            textvariable=self.dose_var,
            width=8,
            justify='right'
        )
        self.dose_entry.pack(side=tk.LEFT, padx=(5, 10))

        # Update button
        update_btn = ttk.Button(
            input_container,
            text="Update",
            command=self._on_dose_value_changed
        )
        update_btn.pack(side=tk.LEFT)

        # Info label
        self.dose_info_var = tk.StringVar(value="Max dose: ")
        info_label = ttk.Label(
            dose_input_frame,
            textvariable=self.dose_info_var,
            font=("Arial", 8)
        )
        info_label.pack(fill=tk.X, pady=(5, 0))

        # Isodose lines frame
        isodose_frame = ttk.LabelFrame(self, text="Isodose Lines", padding=5)
        isodose_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # Create scrollable frame for isodose list within the isodose frame
        self._create_scrollable_isodose_list(isodose_frame)

        # Control buttons frame within the isodose frame
        control_frame = ttk.Frame(isodose_frame)
        control_frame.pack(fill=tk.X, pady=(5, 0))

        # Show All button
        show_all_btn = ttk.Button(
            control_frame,
            text="Show All",
            command=self._show_all_isodoses
        )
        show_all_btn.pack(side=tk.LEFT, padx=(0, 5))

        # Hide All button
        hide_all_btn = ttk.Button(
            control_frame,
            text="Hide All",
            command=self._hide_all_isodoses
        )
        hide_all_btn.pack(side=tk.LEFT)

        # Bind Enter key to dose entry
        self.dose_entry.bind('<Return>', lambda e: self._on_dose_value_changed())

    def _create_dose_grid_info_labels(self):
        """Create labels to display dose grid information in table format."""
        # Header row with column labels
        header_frame = ttk.Frame(self.dose_grid_frame)
        header_frame.pack(fill=tk.X, pady=(0, 2))

        ttk.Label(header_frame, text="", width=14).pack(side=tk.LEFT)  # Empty space for row labels
        ttk.Label(header_frame, text="X", width=10, anchor="center", font=("Arial", 9, "bold")).pack(side=tk.LEFT, padx=2)
        ttk.Label(header_frame, text="Y", width=10, anchor="center", font=("Arial", 9, "bold")).pack(side=tk.LEFT, padx=2)
        ttk.Label(header_frame, text="Z", width=10, anchor="center", font=("Arial", 9, "bold")).pack(side=tk.LEFT, padx=2)

        # Origin row
        origin_frame = ttk.Frame(self.dose_grid_frame)
        origin_frame.pack(fill=tk.X, pady=(0, 1))

        ttk.Label(origin_frame, text="Origin (cm):", width=14, anchor="w").pack(side=tk.LEFT)
        self.origin_x_var = tk.StringVar(value="")
        self.origin_y_var = tk.StringVar(value="")
        self.origin_z_var = tk.StringVar(value="")
        ttk.Label(origin_frame, textvariable=self.origin_x_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        ttk.Label(origin_frame, textvariable=self.origin_y_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        ttk.Label(origin_frame, textvariable=self.origin_z_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)

        # End coordinates row
        end_frame = ttk.Frame(self.dose_grid_frame)
        end_frame.pack(fill=tk.X, pady=(0, 1))

        ttk.Label(end_frame, text="End (cm):", width=14, anchor="w").pack(side=tk.LEFT)
        self.end_x_var = tk.StringVar(value="")
        self.end_y_var = tk.StringVar(value="")
        self.end_z_var = tk.StringVar(value="")
        ttk.Label(end_frame, textvariable=self.end_x_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        ttk.Label(end_frame, textvariable=self.end_y_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        ttk.Label(end_frame, textvariable=self.end_z_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)

        # Voxel size row
        voxel_frame = ttk.Frame(self.dose_grid_frame)
        voxel_frame.pack(fill=tk.X, pady=(0, 1))

        ttk.Label(voxel_frame, text="Voxel Size (cm):", width=14, anchor="w").pack(side=tk.LEFT)
        self.voxel_x_var = tk.StringVar(value="")
        self.voxel_y_var = tk.StringVar(value="")
        self.voxel_z_var = tk.StringVar(value="")
        ttk.Label(voxel_frame, textvariable=self.voxel_x_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        ttk.Label(voxel_frame, textvariable=self.voxel_y_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        ttk.Label(voxel_frame, textvariable=self.voxel_z_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)

        # Number of voxels row
        num_voxels_frame = ttk.Frame(self.dose_grid_frame)
        num_voxels_frame.pack(fill=tk.X)

        ttk.Label(num_voxels_frame, text="Voxels:", width=14, anchor="w").pack(side=tk.LEFT)
        self.num_voxels_x_var = tk.StringVar(value="")
        self.num_voxels_y_var = tk.StringVar(value="")
        self.num_voxels_z_var = tk.StringVar(value="")
        ttk.Label(num_voxels_frame, textvariable=self.num_voxels_x_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        ttk.Label(num_voxels_frame, textvariable=self.num_voxels_y_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
        ttk.Label(num_voxels_frame, textvariable=self.num_voxels_z_var, width=10, anchor="center", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)

    def _update_dose_grid_info(self, dose: Optional[Dose]):
        """Update the dose grid information display."""
        if dose is None or not hasattr(dose, 'dose_grid') or dose.dose_grid is None:
            # Set all values to 
            self.origin_x_var.set("")
            self.origin_y_var.set("")
            self.origin_z_var.set("")
            self.end_x_var.set("")
            self.end_y_var.set("")
            self.end_z_var.set("")
            self.voxel_x_var.set("")
            self.voxel_y_var.set("")
            self.voxel_z_var.set("")
            self.num_voxels_x_var.set("")
            self.num_voxels_y_var.set("")
            self.num_voxels_z_var.set("")
            return

        dose_grid = dose.dose_grid

        try:
            # Origin coordinates
            if hasattr(dose_grid, 'origin') and dose_grid.origin:
                self.origin_x_var.set(f"{dose_grid.origin.x:.2f}")
                self.origin_y_var.set(f"{dose_grid.origin.y:.2f}")
                self.origin_z_var.set(f"{dose_grid.origin.z:.2f}")
            else:
                self.origin_x_var.set("")
                self.origin_y_var.set("")
                self.origin_z_var.set("")

            # End coordinates (calculated from origin + dimension * voxel_size)
            if (hasattr(dose_grid, 'origin') and dose_grid.origin and
                hasattr(dose_grid, 'dimension') and dose_grid.dimension and
                hasattr(dose_grid, 'voxel_size') and dose_grid.voxel_size):

                end_x = dose_grid.origin.x + (dose_grid.dimension.x - 1) * dose_grid.voxel_size.x
                end_y = dose_grid.origin.y + (dose_grid.dimension.y - 1) * dose_grid.voxel_size.y
                end_z = dose_grid.origin.z + (dose_grid.dimension.z - 1) * dose_grid.voxel_size.z
                self.end_x_var.set(f"{end_x:.2f}")
                self.end_y_var.set(f"{end_y:.2f}")
                self.end_z_var.set(f"{end_z:.2f}")
            else:
                self.end_x_var.set("")
                self.end_y_var.set("")
                self.end_z_var.set("")

            # Voxel size
            if hasattr(dose_grid, 'voxel_size') and dose_grid.voxel_size:
                self.voxel_x_var.set(f"{dose_grid.voxel_size.x:.3f}")
                self.voxel_y_var.set(f"{dose_grid.voxel_size.y:.3f}")
                self.voxel_z_var.set(f"{dose_grid.voxel_size.z:.3f}")
            else:
                self.voxel_x_var.set("")
                self.voxel_y_var.set("")
                self.voxel_z_var.set("")

            # Number of voxels
            if hasattr(dose_grid, 'dimension') and dose_grid.dimension:
                self.num_voxels_x_var.set(f"{dose_grid.dimension.x}")
                self.num_voxels_y_var.set(f"{dose_grid.dimension.y}")
                self.num_voxels_z_var.set(f"{dose_grid.dimension.z}")
            else:
                self.num_voxels_x_var.set("")
                self.num_voxels_y_var.set("")
                self.num_voxels_z_var.set("")

        except Exception as e:
            print(f"Error updating dose grid info: {e}")
            # Set all values to Error
            self.origin_x_var.set("Error")
            self.origin_y_var.set("Error")
            self.origin_z_var.set("Error")
            self.end_x_var.set("Error")
            self.end_y_var.set("Error")
            self.end_z_var.set("Error")
            self.voxel_x_var.set("Error")
            self.voxel_y_var.set("Error")
            self.voxel_z_var.set("Error")
            self.num_voxels_x_var.set("Error")
            self.num_voxels_y_var.set("Error")
            self.num_voxels_z_var.set("Error")

    def _create_scrollable_isodose_list(self, parent_frame=None):
        """Create a scrollable frame for the isodose list."""
        if parent_frame is None:
            parent_frame = self

        # Create frame with scrollbar
        list_frame = ttk.Frame(parent_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # Create canvas and scrollbar
        self.canvas = tk.Canvas(list_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.canvas.yview)

        # Create scrollable frame inside canvas
        self.scrollable_frame = ttk.Frame(self.canvas)

        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.canvas.bind("<Button-4>", self._on_mousewheel)
        self.canvas.bind("<Button-5>", self._on_mousewheel)

        # Create isodose level checkboxes
        self._create_isodose_checkboxes()

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        delta = 0
        if hasattr(event, 'delta'):
            delta = event.delta
        elif hasattr(event, 'num'):
            delta = -1 if event.num == 5 else 1

        if delta:
            self.canvas.yview_scroll(int(-1 * (delta / 120)), "units")

    def _create_isodose_checkboxes(self):
        """Create checkbox widgets for each isodose level."""
        self.visibility_state.clear()
        self.isodose_checkboxes.clear()

        # Clear existing widgets
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # Create checkbox for each isodose level
        for level in self.isodose_levels:
            self._create_isodose_checkbox(level)

        # Update canvas scroll region
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def _create_isodose_checkbox(self, level: float):
        """Create a checkbox widget for an isodose level."""
        # Create frame for this isodose level
        isodose_frame = ttk.Frame(self.scrollable_frame)
        isodose_frame.pack(fill=tk.X, pady=1)

        # Create checkbox variable
        checkbox_var = tk.BooleanVar(value=True)  # Default to visible
        self.isodose_checkboxes[level] = checkbox_var
        self.visibility_state[level] = True

        # Store isodose color
        self.color_state[level] = self.isodose_colors.get(level, "#ffffff")

        # Calculate absolute dose for this level
        absolute_dose = (level / 100.0) * self.absolute_dose_value

        # Create checkbox
        checkbox_text = f"{level}% ({absolute_dose:.1f} cGy)"
        checkbox = ttk.Checkbutton(
            isodose_frame,
            text=checkbox_text,
            variable=checkbox_var,
            command=lambda l=level: self._on_isodose_checkbox_changed(l)
        )
        checkbox.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Add color indicator
        self._add_color_indicator(isodose_frame, self.isodose_colors.get(level, "#ffffff"))

    def _add_color_indicator(self, parent_frame: ttk.Frame, color: str):
        """Add a color indicator for the isodose level."""
        try:
            # Create small colored frame
            color_frame = tk.Frame(
                parent_frame,
                width=12,
                height=12,
                bg=color,
                relief="solid",
                borderwidth=1
            )
            color_frame.pack(side=tk.RIGHT, padx=(5, 0))
            color_frame.pack_propagate(False)

        except Exception:
            # If color parsing fails, just skip the color indicator
            pass

    def _on_dose_value_changed(self):
        """Handle dose value change."""
        try:
            new_dose = float(self.dose_var.get())
            if new_dose > 0:
                self.absolute_dose_value = new_dose
                # Recreate checkboxes with updated dose values
                self._create_isodose_checkboxes()
                print(f"Updated absolute dose reference to {new_dose} cGy")

                # Trigger callback for visible isodose levels to update display
                if self.on_isodose_visibility_changed:
                    for level, is_visible in self.visibility_state.items():
                        if is_visible:
                            self.on_isodose_visibility_changed(level, True)
            else:
                # Reset to previous valid value
                self.dose_var.set(str(self.absolute_dose_value))
        except ValueError:
            # Reset to previous valid value if input is invalid
            self.dose_var.set(str(self.absolute_dose_value))
            print("Invalid dose value entered")

    def _on_isodose_checkbox_changed(self, level: float):
        """Handle isodose checkbox state change."""
        if level in self.isodose_checkboxes:
            is_visible = self.isodose_checkboxes[level].get()
            self.visibility_state[level] = is_visible

            # Call callback if provided
            if self.on_isodose_visibility_changed:
                self.on_isodose_visibility_changed(level, is_visible)

            # Request CT viewer refresh
            self.request_refresh()

    def _show_all_isodoses(self):
        """Show all isodose levels."""
        for level, checkbox_var in self.isodose_checkboxes.items():
            checkbox_var.set(True)
            self.visibility_state[level] = True

        # Trigger callbacks for all isodose levels
        if self.on_isodose_visibility_changed:
            for level in self.isodose_levels:
                self.on_isodose_visibility_changed(level, True)

        # Request refresh
        self.request_refresh()

    def _hide_all_isodoses(self):
        """Hide all isodose levels."""
        for level, checkbox_var in self.isodose_checkboxes.items():
            checkbox_var.set(False)
            self.visibility_state[level] = False

        # Trigger callbacks for all isodose levels
        if self.on_isodose_visibility_changed:
            for level in self.isodose_levels:
                self.on_isodose_visibility_changed(level, False)

        # Request refresh
        self.request_refresh()

    def load_data(self, data: Optional[Dose], patient_position: Optional[str] = None, prescription_isodose_cgy: Optional[float] = None, **kwargs):
        """
        Load dose data into the panel.

        Args:
            data: Dose object to display
            patient_position: Patient position for coordinate transformation
            prescription_isodose_cgy: Prescription dose in cGy for the 100% isodose line.
            **kwargs: Additional parameters
        """
        self.current_dose = data
        self.patient_position = patient_position
        self.visibility_state.clear()
        self.color_state.clear()

        # Reset transformation state when new data is loaded
        self._transformation_applied = None

        if data is None:
            self.max_dose_value = 100.0
            self.dose_info_var.set("Max dose: ")
            self._update_dose_grid_info(None)
            return

        # Apply coordinate transformations to current_dose
        self.apply_coordinate_transforms(patient_position)

        # Extract maximum dose from transformed data
        max_dose = self.current_dose.pixel_data.max()

        if max_dose is not None:
            self.max_dose_value = float(max_dose)
            self.dose_info_var.set(f"Max dose: {self.max_dose_value:.1f} cGy")
        else:
            self.max_dose_value = 100.0
            self.dose_info_var.set("Max dose: ")

        # Set the prescription / normalization dose
        if prescription_isodose_cgy is not None:
            self.absolute_dose_value = prescription_isodose_cgy
            self.dose_var.set(str(round(prescription_isodose_cgy, 1)))
        else:
            # Set default absolute dose to max dose
            self.absolute_dose_value = self.max_dose_value
            self.dose_var.set(str(round(self.max_dose_value, 1)))

        # Recreate checkboxes with updated dose values
        self._create_isodose_checkboxes()

        # Update dose grid information
        self._update_dose_grid_info(self.current_dose)

        print(f"Loaded dose data: max dose = {self.max_dose_value:.1f} cGy")

        # Notify CT viewer to refresh dose display
        self._notify_ct_viewer_dose_loaded()

    def is_isodose_visible(self, level: float) -> bool:
        """
        Check if an isodose level is currently visible.

        Args:
            level: Isodose level percentage to check

        Returns:
            bool: True if isodose level is visible, False otherwise
        """
        return self.visibility_state.get(level, False)

    def get_visible_isodose_levels(self) -> List[float]:
        """
        Get list of currently visible isodose levels.

        Returns:
            List[float]: List of visible isodose level percentages
        """
        visible_levels = []
        for level in self.isodose_levels:
            if self.is_isodose_visible(level):
                visible_levels.append(level)
        return visible_levels

    def get_isodose_contours_for_slice(self, slice_index: int, orientation: str = "axial") -> dict:
        """
        Get isodose contours for a specific slice in the given orientation.

        Args:
            slice_index: Slice index in the dose grid
            orientation: View orientation ("axial", "sagittal", "coronal")

        Returns:
            dict: Dictionary mapping isodose levels to contour lists
        """
        dose = self.current_dose
        if not dose or not hasattr(dose, 'pixel_data') or dose.pixel_data is None:
            return {}

        try:
            # Get the slice data based on orientation
            slice_data = self._get_dose_slice_data_for_orientation(slice_index, orientation)
            if slice_data is None:
                return {}

            # Calculate contours for visible isodose levels
            contours = {}
            for level in self.get_visible_isodose_levels():
                absolute_dose = (level / 100.0) * self.absolute_dose_value

                # Find contours at this dose level
                try:
                    contour_list = measure.find_contours(slice_data, absolute_dose)
                    if contour_list:
                        # Convert contours to world coordinates
                        world_contours = []
                        for contour in contour_list:
                            world_contour = self._convert_dose_contour_to_world_coordinates(
                                contour, slice_index, orientation
                            )
                            if world_contour is not None:
                                world_contours.append(world_contour)

                        if world_contours:
                            contours[level] = world_contours

                except Exception as e:
                    print(f"Error computing contour for {level}% isodose: {e}")
                    continue

            return contours

        except Exception as e:
            print(f"Error getting isodose contours for slice {slice_index}: {e}")
            return {}

    def _get_dose_slice_data_for_orientation(self, slice_index: int, orientation: str) -> Optional[np.ndarray]:
        """
        Get dose slice data for the specified orientation.

        Args:
            slice_index: Slice index in the dose grid
            orientation: View orientation ("axial", "sagittal", "coronal")

        Returns:
            np.ndarray: 2D slice data or None if extraction fails
        """
        if not self.current_dose or not hasattr(self.current_dose, 'pixel_data') or self.current_dose.pixel_data is None:
            return None

        pixel_data = self.current_dose.pixel_data

        try:
            if orientation == "axial":
                # Axial: normal Z-slice (slice through Z dimension)
                if slice_index >= pixel_data.shape[0]:
                    return None
                return pixel_data[slice_index, :, :]

            elif orientation == "sagittal":
                # Sagittal: slice through X dimension (viewing from side)
                if slice_index >= pixel_data.shape[2]:
                    return None
                # Extract X slice for upright patient (Y, Z orientation)
                return pixel_data[:, :, slice_index]

            elif orientation == "coronal":
                # Coronal: slice through Y dimension (viewing from front/back)
                if slice_index >= pixel_data.shape[1]:
                    return None
                # Extract Y slice
                return pixel_data[:, slice_index, :]

            return None

        except Exception as e:
            print(f"Error extracting dose slice data for orientation {orientation}: {e}")
            return None

    def _convert_dose_contour_to_world_coordinates(self, contour: np.ndarray, slice_index: int, orientation: str = "axial") -> Optional[np.ndarray]:
        """
        Convert dose contour from dose grid coordinates to world coordinates.

        Args:
            contour: Contour points in dose grid coordinates (N, 2)
            slice_index: Slice index in dose grid for the given orientation
            orientation: View orientation ("axial", "sagittal", "coronal")

        Returns:
            np.ndarray: Contour points in world coordinates (N, 3) or None if conversion fails
        """
        if not self.current_dose or not hasattr(self.current_dose, 'dose_grid'):
            return None

        dose_grid = self.current_dose.dose_grid
        if not dose_grid:
            return None

        try:
            # Get dose grid parameters
            origin = dose_grid.origin
            voxel_size = dose_grid.voxel_size

            if not origin or not voxel_size:
                return None

            # Convert contour points to world coordinates based on orientation
            world_points = []
            for point in contour:
                # Note: contour points are in (row, col) format from the 2D slice
                if orientation == "axial":
                    # Axial: contour is in (Y, X) slice, Z is fixed
                    world_x = origin.x + point[1] * voxel_size.x
                    world_y = origin.y + point[0] * voxel_size.y
                    world_z = origin.z + slice_index * voxel_size.z

                elif orientation == "sagittal":
                    # Sagittal: contour is from pixel_data[:, :, slice_index] which gives (Z, Y) shape
                    # So point[0] (row) maps to Z, point[1] (col) maps to Y
                    world_x = origin.x + slice_index * voxel_size.x
                    world_y = origin.y + point[1] * voxel_size.y
                    world_z = origin.z + point[0] * voxel_size.z

                elif orientation == "coronal":
                    # Coronal: contour is in (Z, X) slice, Y is fixed
                    world_x = origin.x + point[1] * voxel_size.x
                    world_y = origin.y + slice_index * voxel_size.y
                    world_z = origin.z + point[0] * voxel_size.z

                else:
                    # Default to axial
                    world_x = origin.x + point[1] * voxel_size.x
                    world_y = origin.y + point[0] * voxel_size.y
                    world_z = origin.z + slice_index * voxel_size.z

                world_points.append([world_x, world_y, world_z])

            return np.array(world_points)

        except Exception as e:
            print(f"Error converting dose contour to world coordinates: {e}")
            return None

    def get_isodose_color(self, level: float) -> str:
        """
        Get the color for an isodose level.

        Args:
            level: Isodose level percentage

        Returns:
            str: Hex color string
        """
        return self.isodose_colors.get(level, "#ffffff")

    def _notify_ct_viewer_dose_loaded(self):
        """Notify the CT viewer that dose data has been loaded."""
        if self.app and hasattr(self.app, 'ct_viewer'):
            self.app.ct_viewer.refresh_dose_display()

    # Methods required by BaseOverlayPanel interface

    def render_overlays(self, canvas: tk.Canvas, current_slice_z: float, display_params: Dict[str, Any]):
        """
        Render isodose overlays on the CT viewer canvas.

        Args:
            canvas: Canvas to draw on
            current_slice_z: Current slice Z-coordinate
            display_params: Display parameters (scale, center, etc.)
        """
        # Clear existing dose overlays
        self.clear_overlays(canvas, "isodose_overlay")

        if not self.current_dose:
            return

        # Check if we have valid display parameters
        if not display_params or display_params['scale'] <= 0:
            return

        # Get current view orientation from CT viewer
        orientation = getattr(self.ct_viewer, 'view_orientation', 'axial')

        # Get dose slice index that corresponds to the current CT slice based on orientation
        dose_slice_index = self._get_dose_slice_index_for_coordinate(current_slice_z, orientation)
        if dose_slice_index is None:
            return

        # Get isodose contours for this slice in the current orientation
        contours = self.get_isodose_contours_for_slice(dose_slice_index, orientation)

        # Draw each visible isodose level
        for level, contour_list in contours.items():
            if self.visibility_state.get(level, False):
                color = self.color_state.get(level, "#ffffff")
                for contour in contour_list:
                    self._draw_isodose_contour_on_canvas(contour, color, display_params, canvas)

    def update_visibility(self, item_id: Any, is_visible: bool):
        """
        Update visibility of specific isodose level.

        Args:
            item_id: Isodose level (float)
            is_visible: Whether isodose level should be visible
        """
        level = float(item_id)
        self.visibility_state[level] = is_visible

        # Update checkbox if it exists
        if level in self.isodose_checkboxes:
            self.isodose_checkboxes[level].set(is_visible)

        # Request refresh
        self.request_refresh()

    def get_visible_items(self) -> List[float]:
        """
        Get list of currently visible isodose levels.

        Returns:
            List of visible isodose level percentages
        """
        visible_levels = []
        for level in self.isodose_levels:
            if self.visibility_state.get(level, False):
                visible_levels.append(level)
        return visible_levels

    def apply_coordinate_transforms(self, patient_position: Optional[str]):
        """
        Apply coordinate transformations for patient position.

        Args:
            patient_position: Patient position for coordinate transformation
        """
        # Skip if already transformed to this position (avoid redundant operations)
        if self._transformation_applied == patient_position:
            return

        self.patient_position = patient_position

        # Apply coordinate transformation to dose if needed
        if patient_position and self.current_dose:
            try:
                from pinnacle_io.utils.coordinate_transforms import transform_dose, needs_coordinate_transform
                if needs_coordinate_transform(patient_position):
                    print(f"Applying dose coordinate transformation for patient position: {patient_position}")
                    # NEW: Use in-place transformation instead of creating copy
                    transform_dose(self.current_dose, patient_position, inplace=True)
                    self._transformation_applied = patient_position
                else:
                    print(f"No dose coordinate transformation needed for patient position: {patient_position}")
                    # No transformation needed, but still track state
                    self._transformation_applied = patient_position
            except Exception as e:
                print(f"Error applying dose coordinate transformation: {e}")
                # Keep original data if transformation fails

    def _get_dose_slice_index_for_coordinate(self, coord: float, orientation: str) -> Optional[int]:
        """
        Get the dose slice index that corresponds to a coordinate in the given orientation.

        Args:
            coord: Coordinate value (could be x, y, or z depending on orientation)
            orientation: View orientation ("axial", "sagittal", "coronal")

        Returns:
            Slice index in dose grid for the given orientation
        """
        if not self.current_dose or not hasattr(self.current_dose, 'dose_grid'):
            return None

        dose_grid = self.current_dose.dose_grid
        if not dose_grid or not hasattr(dose_grid, 'origin') or not hasattr(dose_grid, 'voxel_size'):
            return None

        try:
            # Get the appropriate origin and voxel size based on orientation
            if orientation == "axial":
                # Axial: slicing through Z dimension
                origin_coord = dose_grid.origin.z
                voxel_size_coord = dose_grid.voxel_size.z
                max_slices = getattr(dose_grid.dimension, 'z', None) if hasattr(dose_grid, 'dimension') else None
            elif orientation == "sagittal":
                # Sagittal: slicing through X dimension
                origin_coord = dose_grid.origin.x
                voxel_size_coord = dose_grid.voxel_size.x
                max_slices = getattr(dose_grid.dimension, 'x', None) if hasattr(dose_grid, 'dimension') else None
            elif orientation == "coronal":
                # Coronal: slicing through Y dimension
                origin_coord = dose_grid.origin.y
                voxel_size_coord = dose_grid.voxel_size.y
                max_slices = getattr(dose_grid.dimension, 'y', None) if hasattr(dose_grid, 'dimension') else None
            else:
                # Default to axial
                origin_coord = dose_grid.origin.z
                voxel_size_coord = dose_grid.voxel_size.z
                max_slices = getattr(dose_grid.dimension, 'z', None) if hasattr(dose_grid, 'dimension') else None

            if voxel_size_coord <= 0:
                return None

            # Calculate slice index in dose grid
            dose_slice_index = int(round((coord - origin_coord) / voxel_size_coord))

            # Check bounds
            if max_slices is not None:
                if dose_slice_index < 0 or dose_slice_index >= max_slices:
                    return None

            return dose_slice_index

        except Exception as e:
            print(f"Error calculating dose slice index for {orientation} orientation: {e}")
            return None

    def _draw_isodose_contour_on_canvas(self, contour: np.ndarray, color: str, display_params: Dict[str, Any], canvas: tk.Canvas):
        """Draw a single isodose contour on the canvas."""
        try:
            if len(contour) < 3:  # Need at least 3 points for a contour
                return

            # Convert world coordinates to display coordinates
            display_points = []
            for point in contour:
                display_coords = self.world_to_display_coordinates(
                    point[0], point[1], point[2], display_params
                )
                if display_coords:
                    display_points.extend(display_coords)

            # Draw the contour as a line
            if len(display_points) >= 6:  # At least 3 points (6 coordinates)
                canvas.create_polygon(
                    display_points,
                    outline=color,
                    fill="",
                    width=2,
                    tags="isodose_overlay"
                )

        except Exception as e:
            print(f"Error drawing isodose contour: {e}")

    # Legacy compatibility methods
    def _get_dose_slice_index_for_z_coordinate(self, z_coord: float) -> Optional[int]:
        """Legacy method: Get the dose slice index that corresponds to a Z coordinate (axial only)."""
        return self._get_dose_slice_index_for_coordinate(z_coord, "axial")

    def is_isodose_visible(self, level: float) -> bool:
        """Legacy method: Check if an isodose level is currently visible."""
        return self.visibility_state.get(level, False)

    def get_visible_isodose_levels(self) -> List[float]:
        """Legacy method: Get list of currently visible isodose levels."""
        return self.get_visible_items()

    def clear_data(self):
        """Clear all data from the dose panel."""
        # Clear dose data
        self.current_dose = None
        self.patient_position = None
        self.max_dose_value = 100.0
        self.absolute_dose_value = 100.0
        self.isodose_checkboxes.clear()
        self.visibility_state.clear()
        self.color_state.clear()
        self._transformation_applied = None  # Reset transformation state

        # Reset dose entry field
        self.dose_var.set(str(self.absolute_dose_value))

        # Update dose info
        self.dose_info_var.set("Max dose: ")

        # Update dose grid information
        self._update_dose_grid_info(None)

        # Clear existing widgets and recreate empty checkboxes
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # Recreate empty isodose checkboxes
        self._create_isodose_checkboxes()

        # Update canvas scroll region
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))