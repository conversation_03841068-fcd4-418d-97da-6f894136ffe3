"""
Tests for pinnacle_io.utils.converters module.
"""

import pytest
from datetime import datetime
from pinnacle_io.utils.value_converters import (
    convert_integer,
    convert_float,
    convert_string,
    convert_boolean,
    convert_datetime,
)


class TestConvertInteger:
    """Test cases for convert_integer function."""

    def test_convert_none(self):
        """Test conversion of None value."""
        assert convert_integer(None) is None

    def test_convert_integer_value(self):
        """Test conversion of integer values."""
        assert convert_integer(42) == 42
        assert convert_integer(0) == 0
        assert convert_integer(-5) == -5

    def test_convert_float_value(self):
        """Test conversion of float values."""
        assert convert_integer(25.0) == 25
        assert convert_integer(25.7) == 25
        assert convert_integer(-10.9) == -10

    def test_convert_string_number(self):
        """Test conversion of string numbers."""
        assert convert_integer("42") == 42
        assert convert_integer("0") == 0
        assert convert_integer("-5") == -5
        assert convert_integer("25.0") == 25
        assert convert_integer("25.7") == 25

    def test_convert_string_with_whitespace(self):
        """Test conversion of strings with whitespace."""
        assert convert_integer("  42  ") == 42
        assert convert_integer("\t25\n") == 25

    def test_convert_string_with_commas(self):
        """Test conversion of strings with comma separators."""
        assert convert_integer("1,234") == 1234
        assert convert_integer("1,234.0") == 1234
        assert convert_integer("10,000,000") == 10000000

    def test_convert_empty_string(self):
        """Test conversion of empty strings."""
        assert convert_integer("") is None
        assert convert_integer("   ") is None

    def test_convert_null_strings(self):
        """Test conversion of null-like strings."""
        assert convert_integer("null") is None
        assert convert_integer("NULL") is None
        assert convert_integer("none") is None
        assert convert_integer("NONE") is None

    def test_convert_invalid_string(self):
        """Test conversion of invalid strings."""
        assert convert_integer("abc") is None
        assert convert_integer("12.34.56") is None
        assert convert_integer("not_a_number") is None

    def test_convert_invalid_type(self):
        """Test conversion of invalid types."""
        assert convert_integer([1, 2, 3]) is None
        assert convert_integer({"key": "value"}) is None


class TestConvertFloat:
    """Test cases for convert_float function."""

    def test_convert_none(self):
        """Test conversion of None value."""
        assert convert_float(None) is None

    def test_convert_float_value(self):
        """Test conversion of float values."""
        assert convert_float(3.14) == 3.14
        assert convert_float(0.0) == 0.0
        assert convert_float(-2.5) == -2.5

    def test_convert_integer_value(self):
        """Test conversion of integer values."""
        assert convert_float(42) == 42.0
        assert convert_float(0) == 0.0
        assert convert_float(-5) == -5.0

    def test_convert_string_number(self):
        """Test conversion of string numbers."""
        assert convert_float("3.14") == 3.14
        assert convert_float("42") == 42.0
        assert convert_float("-2.5") == -2.5

    def test_convert_string_with_whitespace(self):
        """Test conversion of strings with whitespace."""
        assert convert_float("  3.14  ") == 3.14
        assert convert_float("\t42.0\n") == 42.0

    def test_convert_string_with_commas(self):
        """Test conversion of strings with comma separators."""
        assert convert_float("1,234.56") == 1234.56
        assert convert_float("10,000.0") == 10000.0

    def test_convert_empty_string(self):
        """Test conversion of empty strings."""
        assert convert_float("") is None
        assert convert_float("   ") is None

    def test_convert_null_strings(self):
        """Test conversion of null-like strings."""
        assert convert_float("null") is None
        assert convert_float("NULL") is None
        assert convert_float("none") is None
        assert convert_float("NONE") is None

    def test_convert_invalid_string(self):
        """Test conversion of invalid strings."""
        assert convert_float("abc") is None
        assert convert_float("12.34.56") is None
        assert convert_float("not_a_number") is None

    def test_convert_invalid_type(self):
        """Test conversion of invalid types."""
        assert convert_float([1, 2, 3]) is None
        assert convert_float({"key": "value"}) is None


class TestConvertString:
    """Test cases for convert_string function."""

    def test_convert_none(self):
        """Test conversion of None value."""
        assert convert_string(None, "test_field") is None

    def test_convert_null_strings(self):
        """Test conversion of null-like strings."""
        assert convert_string("null", "test_field") is None
        assert convert_string("NULL", "test_field") is None
        assert convert_string("none", "test_field") is None
        assert convert_string("NONE", "test_field") is None

    def test_convert_basic_string(self):
        """Test conversion of basic string values."""
        assert convert_string("hello", "test_field") == "hello"
        assert convert_string("world", "name") == "world"

    def test_convert_numeric_values(self):
        """Test conversion of numeric values to strings."""
        assert convert_string(42, "test_field") == "42"
        assert convert_string(3.14, "test_field") == "3.14"
        assert convert_string(True, "test_field") == "True"

    def test_convert_email_field(self):
        """Test conversion for email fields."""
        assert convert_string("<EMAIL>", "email") == "<EMAIL>"
        assert convert_string("  <EMAIL>  ", "user_email") == "<EMAIL>"
        assert convert_string("<EMAIL>", "email_address") == "<EMAIL>"

    def test_convert_phone_field(self):
        """Test conversion for phone fields."""
        assert convert_string("(*************", "phone") == "5551234567"
        assert convert_string("******-555-0123", "phone_number") == "18005550123"
        assert convert_string("************ ext 123", "mobile_phone") == "5551234567123"

    def test_convert_other_fields(self):
        """Test conversion for other field types."""
        # Regular fields should not be modified (commented out transformations)
        assert convert_string("john doe", "name") == "john doe"
        assert convert_string("software engineer", "title") == "software engineer"
        assert convert_string("abc123", "user_code") == "abc123"
        assert convert_string("xyz789", "code_reference") == "xyz789"


class TestConvertBoolean:
    """Test cases for convert_boolean function."""

    def test_convert_none(self):
        """Test conversion of None value."""
        assert convert_boolean(None) is None

    def test_convert_boolean_values(self):
        """Test conversion of boolean values."""
        assert convert_boolean(True) is True
        assert convert_boolean(False) is False

    def test_convert_integer_values(self):
        """Test conversion of integer values."""
        assert convert_boolean(1) is True
        assert convert_boolean(0) is False
        assert convert_boolean(42) is True  # Non-zero is truthy
        assert convert_boolean(-1) is True

    def test_convert_true_strings(self):
        """Test conversion of true-like strings."""
        true_values = ["true", "TRUE", "True", "1", "yes", "YES", "on", "ON", "active", "ACTIVE", "enabled", "ENABLED"]
        for value in true_values:
            assert convert_boolean(value) is True

    def test_convert_false_strings(self):
        """Test conversion of false-like strings."""
        false_values = ["false", "FALSE", "False", "0", "no", "NO", "off", "OFF", "inactive", "INACTIVE", "disabled", "DISABLED", ""]
        for value in false_values:
            assert convert_boolean(value) is False

    def test_convert_string_with_whitespace(self):
        """Test conversion of strings with whitespace."""
        assert convert_boolean("  true  ") is True
        assert convert_boolean("\tfalse\n") is False

    def test_convert_invalid_string(self):
        """Test conversion of invalid strings raises ValueError."""
        with pytest.raises(ValueError, match="Cannot convert 'maybe' to boolean"):
            convert_boolean("maybe")

        with pytest.raises(ValueError, match="Cannot convert 'invalid' to boolean"):
            convert_boolean("invalid")


class TestConvertDatetime:
    """Test cases for convert_datetime function."""

    def test_convert_none(self):
        """Test conversion of None value."""
        assert convert_datetime(None) is None

    def test_convert_datetime_object(self):
        """Test conversion of datetime objects."""
        dt = datetime(2023, 12, 25, 10, 30, 45)
        assert convert_datetime(dt) == dt

    def test_convert_empty_string(self):
        """Test conversion of empty strings."""
        assert convert_datetime("") is None
        assert convert_datetime("   ") is None

    def test_convert_null_strings(self):
        """Test conversion of null-like strings."""
        assert convert_datetime("null") is None
        assert convert_datetime("NULL") is None
        assert convert_datetime("none") is None
        assert convert_datetime("NONE") is None

    def test_convert_string_with_whitespace(self):
        """Test conversion of strings with whitespace."""
        result = convert_datetime("  2023-12-25 10:30:45  ")
        expected = datetime(2023, 12, 25, 10, 30, 45)
        assert result == expected

    def test_convert_standard_formats(self):
        """Test conversion of standard datetime formats."""
        test_cases = [
            ("2023-12-25 10:30:45", datetime(2023, 12, 25, 10, 30, 45)),
            ("2023-12-25.10:30:45", datetime(2023, 12, 25, 10, 30, 45)),
            ("2023-12-25 10:30:45.123456", datetime(2023, 12, 25, 10, 30, 45, 123456)),
            ("2023-12-25", datetime(2023, 12, 25)),
            ("12/25/2023", datetime(2023, 12, 25)),
            ("12/25/2023 10:30:45", datetime(2023, 12, 25, 10, 30, 45)),
            ("25/12/2023", datetime(2023, 12, 25)),
            ("2023-12-25T10:30:45", datetime(2023, 12, 25, 10, 30, 45)),
            ("2023-12-25T10:30:45.123456", datetime(2023, 12, 25, 10, 30, 45, 123456)),
        ]

        for date_string, expected in test_cases:
            result = convert_datetime(date_string)
            assert result == expected, f"Failed to convert {date_string}"

    def test_convert_unix_timestamp(self):
        """Test conversion of Unix timestamps."""
        # Test with integer timestamp
        timestamp = 1640444400  # 2021-12-25 15:00:00 UTC
        result = convert_datetime(timestamp)
        expected = datetime.fromtimestamp(timestamp)
        assert result == expected

        # Test with float timestamp
        timestamp_float = 1640444400.123
        result = convert_datetime(timestamp_float)
        expected = datetime.fromtimestamp(timestamp_float)
        assert result == expected

    def test_convert_invalid_string_format(self):
        """Test conversion of invalid string formats raises ValueError."""
        with pytest.raises(ValueError, match="Cannot parse datetime 'invalid-date'"):
            convert_datetime("invalid-date")

        with pytest.raises(ValueError, match="Cannot parse datetime 'not-a-date'"):
            convert_datetime("not-a-date")

    def test_convert_unsupported_type(self):
        """Test conversion of unsupported types."""
        # Should return the value as-is for unsupported types
        test_list = [1, 2, 3]
        assert convert_datetime(test_list) == test_list

        test_dict = {"key": "value"}
        assert convert_datetime(test_dict) == test_dict