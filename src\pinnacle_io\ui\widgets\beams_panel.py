"""
Beams panel widget for displaying and controlling beam visibility and rendering.
"""

import tkinter as tk
import ttkbootstrap as ttk
from typing import List, Optional, Callable, Dict, Any, Tuple
import math

from pinnacle_io.models.beam import Beam
from pinnacle_io.models.point import Point
from pinnacle_io.ui.constants import get_pinnacle_hex_color
from pinnacle_io.ui.widgets.base_overlay_panel import BaseOverlayPanel


class BeamsPanel(BaseOverlayPanel):
    """Panel for displaying beams with checkboxes to control visibility in CT viewer."""

    def __init__(
        self, parent, ct_viewer=None, on_beam_visibility_changed: Optional[Callable[[Beam, bool], None]] = None
    ):
        """
        Initialize the beams panel.

        Args:
            parent: Parent widget
            ct_viewer: Reference to CT viewer for coordinate system access
            on_beam_visibility_changed: Callback function called when beam visibility changes.
                                       Takes Beam object and visibility boolean as parameters.
        """
        super().__init__(parent, ct_viewer)
        self.on_beam_visibility_changed = on_beam_visibility_changed

        # Beam-specific data
        self.current_beams: List[Beam] = []
        self.current_points: List[Point] = []
        self.patient_position: Optional[str] = None
        self.beam_checkboxes: dict[str, tk.BooleanVar] = {}  # beam_id -> checkbox var

    def _setup_ui(self):
        """Set up the beams panel UI components."""
        # Title
        title_label = ttk.Label(self, text="Beams", font=("Arial", 10, "bold"))
        title_label.pack(fill=tk.X, padx=5, pady=(5, 10))

        # Create scrollable frame for beam list
        self._create_scrollable_beam_list()

        # Control buttons frame
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=5, pady=(5, 0))

        # Show All button
        show_all_btn = ttk.Button(control_frame, text="Show All", command=self._show_all_beams)
        show_all_btn.pack(side=tk.LEFT, padx=(0, 5))

        # Hide All button
        hide_all_btn = ttk.Button(control_frame, text="Hide All", command=self._hide_all_beams)
        hide_all_btn.pack(side=tk.LEFT)

    def _create_scrollable_beam_list(self):
        """Create a scrollable frame for the beam list."""
        # Create frame with scrollbar
        list_frame = ttk.Frame(self)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))

        # Create canvas and scrollbar
        self.canvas = tk.Canvas(list_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.canvas.yview)

        # Create scrollable frame inside canvas
        self.scrollable_frame = ttk.Frame(self.canvas)

        # Configure scrolling
        self.scrollable_frame.bind("<Configure>", lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all")))

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.canvas.bind("<Button-4>", self._on_mousewheel)
        self.canvas.bind("<Button-5>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        delta = 0
        if hasattr(event, "delta"):
            delta = event.delta
        elif hasattr(event, "num"):
            delta = -1 if event.num == 5 else 1

        if delta:
            self.canvas.yview_scroll(int(-1 * (delta / 120)), "units")

    def load_data(self, data: List[Beam], points: List[Point] = None, patient_position: Optional[str] = None, **kwargs):
        """
        Load beam data into the panel.

        Args:
            data: List of Beam objects to display
            points: List of Point objects for isocenter coordinates
            patient_position: Patient position for coordinate transformation
            **kwargs: Additional parameters
        """
        self.current_beams = data
        self.patient_position = patient_position
        self.visibility_state.clear()
        self.color_state.clear()
        self.beam_checkboxes.clear()

        # Apply coordinate transformations to points and store as main data
        self.current_points = points or []
        self.apply_coordinate_transforms(patient_position)

        # Clear existing widgets
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # Create checkbox for each beam
        for beam in data:
            self._create_beam_checkbox(beam)

        # Update canvas scroll region
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def _create_beam_checkbox(self, beam: Beam):
        """Create a checkbox widget for a beam."""
        beam_id = beam.field_id if beam.field_id is not None else "0"
        beam_number = beam.beam_number if beam.beam_number is not None else 0
        beam_name = beam.name if beam.name else f"Beam {beam_number}"

        # Create frame for this beam
        beam_frame = ttk.Frame(self.scrollable_frame)
        beam_frame.pack(fill=tk.X, pady=1)

        # Create checkbox variable
        checkbox_var = tk.BooleanVar(value=True)  # Default to visible
        self.beam_checkboxes[beam_id] = checkbox_var
        self.visibility_state[beam_id] = True

        # Parse and store beam color
        beam_color = self._parse_beam_color(beam)
        self.color_state[beam_id] = beam_color

        # Create checkbox
        checkbox = ttk.Checkbutton(
            beam_frame, text=beam_name, variable=checkbox_var, command=lambda b=beam: self._on_beam_checkbox_changed(b)
        )
        checkbox.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Add color indicator if beam has color information
        if hasattr(beam, "color") and beam.color:
            self._add_color_indicator(beam_frame, beam.color)

        # Add beam info label
        info_text = self._get_beam_info_text(beam)
        if info_text:
            info_label = ttk.Label(beam_frame, text=info_text, font=("Arial", 8), foreground="gray")
            info_label.pack(side=tk.RIGHT, padx=(5, 0))

    def _get_beam_info_text(self, beam: Beam) -> str:
        """Get info text for beam display."""
        info_parts = []

        # Add modality if available
        if beam.modality:
            info_parts.append(beam.modality)

        # Add energy if available
        if beam.machine_energy_name:
            info_parts.append(beam.machine_energy_name)

        # Add gantry angle from first control point
        if beam.control_point_list and len(beam.control_point_list) > 0:
            first_cp = beam.control_point_list[0]
            if first_cp.gantry is not None:
                info_parts.append(f"G{first_cp.gantry:.0f}°")

        return " | ".join(info_parts)

    def _add_color_indicator(self, parent_frame: ttk.Frame, color_str: str):
        """Add a color indicator for the beam."""
        try:
            color = get_pinnacle_hex_color(color_str)

            # Create small colored frame
            color_frame = tk.Frame(parent_frame, width=12, height=12, bg=color, relief="solid", borderwidth=1)
            color_frame.pack(side=tk.RIGHT, padx=(5, 0))
            color_frame.pack_propagate(False)

        except Exception:
            # If color parsing fails, just skip the color indicator
            pass

    def _on_beam_checkbox_changed(self, beam: Beam):
        """Handle beam checkbox state change."""
        beam_id = beam.field_id if beam.field_id is not None else "0"

        if beam_id in self.beam_checkboxes:
            is_visible = self.beam_checkboxes[beam_id].get()
            self.visibility_state[beam_id] = is_visible

            # Call callback if provided
            if self.on_beam_visibility_changed:
                self.on_beam_visibility_changed(beam, is_visible)

            # Request CT viewer refresh
            self.request_refresh()

    def _show_all_beams(self):
        """Show all beams."""
        for beam_id, checkbox_var in self.beam_checkboxes.items():
            checkbox_var.set(True)
            self.visibility_state[beam_id] = True

        # Trigger callbacks for all beams
        if self.on_beam_visibility_changed:
            for beam in self.current_beams:
                self.on_beam_visibility_changed(beam, True)

        # Request refresh
        self.request_refresh()

    def _hide_all_beams(self):
        """Hide all beams."""
        for beam_id, checkbox_var in self.beam_checkboxes.items():
            checkbox_var.set(False)
            self.visibility_state[beam_id] = False

        # Trigger callbacks for all beams
        if self.on_beam_visibility_changed:
            for beam in self.current_beams:
                self.on_beam_visibility_changed(beam, False)

        # Request refresh
        self.request_refresh()

    def is_beam_visible(self, beam: Beam) -> bool:
        """
        Check if a beam is currently visible.

        Args:
            beam: Beam object to check

        Returns:
            bool: True if beam is visible, False otherwise
        """
        beam_id = beam.id if beam.id is not None else 0
        return self.beam_visibility.get(beam_id, False)

    def get_visible_beams(self) -> List[Beam]:
        """
        Get list of currently visible beams.

        Returns:
            List[Beam]: List of visible beam objects
        """
        visible_beams = []
        for beam in self.current_beams:
            if self.is_beam_visible(beam):
                visible_beams.append(beam)
        return visible_beams

    def get_beam_isocenter_coordinates(self, beam: Beam) -> Optional[tuple[float, float, float]]:
        """
        Get the isocenter coordinates for a beam by looking up the isocenter POI.

        Args:
            beam: Beam object

        Returns:
            tuple[float, float, float] | None: (x, y, z) coordinates in mm, or None if not found
        """
        if not beam.isocenter_name or not self.current_points:
            return None

        # Find the point with matching name
        for point in self.current_points:
            if point.name == beam.isocenter_name:
                if point.x is not None and point.y is not None and point.z is not None:
                    return (point.x, point.y, point.z)

        return None

    def get_beam_gantry_angle(self, beam: Beam) -> Optional[float]:
        """
        Get the gantry angle for a beam from its first control point.

        Args:
            beam: Beam object

        Returns:
            float | None: Gantry angle in degrees, or None if not available
        """
        if beam.control_point_list and len(beam.control_point_list) > 0:
            first_cp = beam.control_point_list[0]
            return first_cp.gantry
        return None

    def get_beam_visualization_data(self, beam: Beam) -> Optional[Dict[str, Any]]:
        """
        Get visualization data for a beam including isocenter and beam direction.

        Args:
            beam: Beam object

        Returns:
            dict | None: Dictionary with visualization data or None if insufficient data
        """
        isocenter = self.get_beam_isocenter_coordinates(beam)
        gantry_angle = self.get_beam_gantry_angle(beam)

        if isocenter is None or gantry_angle is None:
            return None

        # Convert gantry angle to direction vector (50cm line)
        # Gantry 0° = up (negative Y), 90° = right (positive X), etc.
        angle_rad = math.radians(gantry_angle)
        beam_length = 500.0  # 50cm in mm

        # Calculate end point of beam line
        end_x = isocenter[0] + beam_length * math.sin(angle_rad)
        end_y = isocenter[1] - beam_length * math.cos(angle_rad)  # Negative because Y increases downward
        end_z = isocenter[2]

        return {
            "isocenter": isocenter,
            "beam_end": (end_x, end_y, end_z),
            "gantry_angle": gantry_angle,
            "beam_number": beam.beam_number,
            "beam_name": beam.name,
            "modality": beam.modality,
            "energy": beam.machine_energy_name,
            "color": beam.color if hasattr(beam, "color") else None,
        }

    # Methods required by BaseOverlayPanel interface

    def render_overlays(self, canvas: tk.Canvas, current_slice_z: float, display_params: Dict[str, Any]):
        """
        Render beam overlays on the CT viewer canvas.

        Args:
            canvas: Canvas to draw on
            current_slice_z: Current slice Z-coordinate
            display_params: Display parameters (scale, center, etc.)
        """
        # Clear existing beam overlays
        self.clear_overlays(canvas, "beam_overlay")

        if not self.current_beams:
            return

        # Check if we have valid display parameters
        if not display_params or display_params["scale"] <= 0:
            return

        # Only draw beams on axial view
        if self.ct_viewer and hasattr(self.ct_viewer, "view_orientation"):
            if self.ct_viewer.view_orientation != "axial":
                return

        # Draw beams that are visible
        for beam in self.current_beams:
            beam_id = beam.field_id if beam.field_id is not None else "0"

            # Skip if beam is not visible
            if not self.visibility_state.get(beam_id, False):
                continue

            beam_color = self.color_state.get(beam_id, "#00ff00")
            self._draw_beam_on_canvas(beam, display_params, beam_color, canvas)

    def update_visibility(self, item_id: Any, is_visible: bool):
        """
        Update visibility of specific beam.

        Args:
            item_id: Beam field_id or Beam object
            is_visible: Whether beam should be visible
        """
        if isinstance(item_id, Beam):
            beam_id = item_id.field_id if item_id.field_id is not None else "0"
        else:
            beam_id = str(item_id)

        self.visibility_state[beam_id] = is_visible

        # Update checkbox if it exists
        if beam_id in self.beam_checkboxes:
            self.beam_checkboxes[beam_id].set(is_visible)

        # Request refresh
        self.request_refresh()

    def get_visible_items(self) -> List[Beam]:
        """
        Get list of currently visible beams.

        Returns:
            List of visible Beam objects
        """
        visible_beams = []
        for beam in self.current_beams:
            beam_id = beam.field_id if beam.field_id is not None else "0"
            if self.visibility_state.get(beam_id, False):
                visible_beams.append(beam)

        return visible_beams

    def apply_coordinate_transforms(self, patient_position: Optional[str]):
        """
        Apply coordinate transformations for patient position.

        Args:
            patient_position: Patient position for coordinate transformation
        """
        self.patient_position = patient_position

        # Handle POI transformations for isocenter coordinates
        if not self.current_points or not patient_position:
            # No POIs or patient position available, keep current data unchanged
            return
        else:
            try:
                # Import coordinate transformation functions
                from pinnacle_io.utils.coordinate_transforms import (
                    transform_coordinates_for_points,
                    needs_coordinate_transform,
                )

                # Check if coordinate transformation is needed for this patient position
                if needs_coordinate_transform(patient_position):
                    print(f"Applying beam isocenter coordinate transformation for patient position: {patient_position}")
                    # Apply coordinate transformations to POI data and replace current_points
                    self.current_points = transform_coordinates_for_points(self.current_points, patient_position)
                else:
                    print(
                        f"No beam isocenter coordinate transformation needed for patient position: {patient_position}"
                    )
                    # No transformation needed, keep current data

            except Exception as e:
                print(f"Error applying beam isocenter coordinate transformation: {e}")
                # Keep original data if transformation fails

    def _draw_beam_on_canvas(self, beam: Beam, display_params: Dict[str, Any], color: str, canvas: tk.Canvas):
        """Draw a single beam on the canvas."""
        try:
            # Get beam visualization data
            isocenter_coords = self._get_beam_isocenter_coordinates(beam)
            gantry_angle = self._get_beam_gantry_angle(beam)

            if isocenter_coords is None or gantry_angle is None:
                return

            # Calculate beam end point in world coordinates (20cm from isocenter)
            angle_rad = math.radians(gantry_angle)
            beam_length = 20.0  # 20cm

            # Calculate beam end point in world coordinates
            # Gantry 0° = up (negative Y), 90° = right (positive X), etc.
            beam_end_world_x = isocenter_coords[0] + beam_length * math.sin(angle_rad)
            beam_end_world_y = isocenter_coords[1] - beam_length * math.cos(angle_rad)
            beam_end_world_z = 0  # Not currently used

            beam_label_world_x = isocenter_coords[0] + (beam_length + 0.5) * math.sin(angle_rad)
            beam_label_world_y = isocenter_coords[1] - (beam_length + 0.5) * math.cos(angle_rad)
            beam_label_world_z = 0  # Not currently used

            # Convert both isocenter and beam end from world coordinates to display coordinates
            iso_display_coords = self.world_to_display_coordinates(
                isocenter_coords[0], isocenter_coords[1], isocenter_coords[2], display_params
            )

            beam_end_display_coords = self.world_to_display_coordinates(
                beam_end_world_x, beam_end_world_y, beam_end_world_z, display_params
            )

            beam_label_display_coords = self.world_to_display_coordinates(
                beam_label_world_x, beam_label_world_y, beam_label_world_z, display_params
            )

            if iso_display_coords is None or beam_end_display_coords is None or beam_label_display_coords is None:
                return

            iso_display_x, iso_display_y = iso_display_coords
            end_display_x, end_display_y = beam_end_display_coords
            label_display_x, label_display_y = beam_label_display_coords

            # Draw isocenter circle
            zoom_factor = display_params.get("zoom_factor", 1.0)
            iso_radius = max(3, min(8, int(5 * zoom_factor)))
            canvas.create_oval(
                iso_display_x - iso_radius,
                iso_display_y - iso_radius,
                iso_display_x + iso_radius,
                iso_display_y + iso_radius,
                outline=color,
                fill="",
                width=2,
                tags="beam_overlay",
            )

            # Draw beam line
            canvas.create_line(
                iso_display_x, iso_display_y, end_display_x, end_display_y, fill=color, width=3, tags="beam_overlay"
            )

            # Add beam label
            if hasattr(beam, "name") and beam.name:
                # Position label near the end of the beam line
                label_x = label_display_x
                label_y = label_display_y

                canvas.create_text(
                    label_x,
                    label_y,
                    text=beam.name,
                    fill=color,
                    font=("Arial", 8, "bold"),
                    anchor="w" if 0 < gantry_angle % 360 < 180 else "e",
                    tags="beam_overlay",
                )

        except Exception as e:
            print(f"Error drawing beam: {e}")

    def _get_beam_isocenter_coordinates(self, beam: Beam) -> Optional[Tuple[float, float, float]]:
        """Get the isocenter coordinates for a beam by looking up the isocenter POI."""
        if not beam.isocenter_name:
            return None

        # Use current_points which now contains transformed data
        if not self.current_points:
            return None

        # Find the point with matching name
        for point in self.current_points:
            if point.name == beam.isocenter_name:
                if point.x_coord is not None and point.y_coord is not None and point.z_coord is not None:
                    return (point.x_coord, point.y_coord, point.z_coord)

        return None

    def _get_beam_gantry_angle(self, beam: Beam) -> Optional[float]:
        """Get the gantry angle for a beam from its first control point."""
        if beam.control_point_list and len(beam.control_point_list) > 0:
            first_cp = beam.control_point_list[0]
            return first_cp.gantry
        return None

    def _parse_beam_color(self, beam: Beam) -> str:
        """Parse beam color from various possible formats."""
        try:
            if hasattr(beam, "color") and beam.color:
                return get_pinnacle_hex_color(beam.color)
            else:
                # Default colors for beams without color info - use green tones
                beam_id = beam.field_id if beam.field_id is not None else "0"
                default_colors = ["#00ff00", "#40ff40", "#80ff80", "#00c000", "#60ff60", "#20ff20"]
                return default_colors[hash(beam_id) % len(default_colors)]
        except Exception:
            return "#00ff00"  # Default to green if parsing fails

    # Legacy compatibility methods
    def is_beam_visible(self, beam: Beam) -> bool:
        """Legacy method: Check if a beam is currently visible."""
        beam_id = beam.field_id if beam.field_id is not None else "0"
        return self.visibility_state.get(beam_id, False)

    def get_visible_beams(self) -> List[Beam]:
        """Legacy method: Get list of currently visible beams."""
        return self.get_visible_items()

    def clear_data(self):
        """Clear all data from the beams panel."""
        # Clear beam data
        self.current_beams = []
        self.current_points = []
        self.patient_position = None
        self.beam_checkboxes.clear()
        self.visibility_state.clear()
        self.color_state.clear()

        # Clear existing widgets
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # Update canvas scroll region
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
