"""
Main window for the Pinnacle IO UI application.
"""

import os
import tkinter as tk
from tkinter import messagebox
from pathlib import Path
from typing import Optional
import threading
import ttkbootstrap as ttk
from ttkbootstrap import Window

from pinnacle_io.models.plan import Plan
from pinnacle_io.models.trial import Trial

from ..api import Pinnacle<PERSON>eader
from .widgets.menu_bar import MenuBar
from .widgets.left_sidebar import LeftSidebar
from .widgets.right_sidebar import RightSidebar
from .widgets.ct_viewer import CTViewer
from .progress import ProgressManager


class MainWindow:
    """Main application window for Pinnacle IO UI."""

    def __init__(self, data_path: Optional[str] = None, export_path: Optional[str] = None):
        """
        Initialize the main window.

        Args:
            data_path: Path to Pinnacle data directory. If None, defaults to test data.
            export_path: Path to export directory. If None, defaults to current directory.
        """
        self.data_path = data_path
        self.export_path = export_path or os.getcwd()
        self.pinnacle_reader: Optional[PinnacleReader] = None

        # Track last selections to prevent duplicate processing
        self._last_patient_selection = None
        self._last_plan_selection = None
        self._last_trial_selection = None

        # Auto-selection preferences for when loading from specific subfolders
        self.institution = None
        self.auto_select_patient = None
        self.auto_select_plan = None

        # Progress management
        self.progress_manager: Optional[ProgressManager] = None

        # Initialize UI
        self._setup_window()
        self._setup_layout()
        if data_path and Path(data_path).exists():
            self.load_data(data_path)
            if export_path and Path(export_path).exists():
                self._save_dicom_files(export_path)

    def _setup_window(self):
        """Set up the main window with ttkbootstrap."""
        # Create the main window with darkly theme
        self.root = Window(themename="darkly")

        # Configure window
        self.root.title("Pinnacle IO - Radiotherapy Treatment Planning Viewer")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        # For maximized window:
        try:
            self.root.state('zoomed')
        except Exception:
            self.root.attributes('-zoomed', True)

        # # Center the window
        # self.root.update_idletasks()
        # x = (self.root.winfo_screenwidth() // 2) - (1400 // 2)
        # y = (self.root.winfo_screenheight() // 2) - (900 // 2)
        # self.root.geometry(f"1400x900+{x}+{y}")

        # # Center the window specifically on my laptop screen
        # self.root.geometry("1800x900+60+400")

        # Configure close behavior
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def _setup_layout(self):
        """Create the main layout with menu, sidebars, and CT viewer."""
        # Create menu bar
        self.menu_bar = MenuBar(
            self.root,
            self,
            self.load_data_with_auto_selection,
            self._save_dicom_files,
            self._export_dicom_files
        )

        # Create main container frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create left sidebar
        self.left_sidebar = LeftSidebar(self.main_frame, self)
        self.left_sidebar.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))

        # Create right sidebar
        self.right_sidebar = RightSidebar(self.main_frame, self)
        self.right_sidebar.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))

        # Create CT viewer in the center
        self.ct_viewer = CTViewer(self.main_frame, self)
        self.ct_viewer.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

        # Set up callbacks for bidirectional control synchronization
        self._setup_ct_viewer_callbacks()

        # Register overlay panels with CT viewer now that all widgets are created
        self._register_overlay_panels()

    def _setup_ct_viewer_callbacks(self):
        """Set up callbacks for bidirectional control synchronization."""
        if hasattr(self, 'ct_viewer') and hasattr(self, 'right_sidebar'):
            # Set up callbacks so CT viewer can notify CT panel of internal changes
            self.ct_viewer.on_slice_changed_callback = self.right_sidebar.ct_panel.update_slice
            self.ct_viewer.on_window_width_changed_callback = self.right_sidebar.ct_panel.update_window_width
            self.ct_viewer.on_window_level_changed_callback = self.right_sidebar.ct_panel.update_window_level
            self.ct_viewer.on_zoom_changed_callback = self.right_sidebar.ct_panel.update_zoom

    def _register_overlay_panels(self):
        """Register overlay panels with the CT viewer."""
        if hasattr(self, 'ct_viewer') and hasattr(self, 'right_sidebar'):
            self.ct_viewer.register_overlay_panel(self.right_sidebar.ct_panel)
            self.ct_viewer.register_overlay_panel(self.right_sidebar.roi_panel)
            self.ct_viewer.register_overlay_panel(self.right_sidebar.poi_panel)
            self.ct_viewer.register_overlay_panel(self.right_sidebar.beams_panel)
            self.ct_viewer.register_overlay_panel(self.right_sidebar.dose_panel)
            print("Registered overlay panels with CT viewer")

    def load_data_with_auto_selection(self, data_path: str,
                                      patient_id: int = None,
                                      plan_id: int = None):
        """
        Load Pinnacle data with optional auto-selection of specific items.

        Args:
            data_path: Path to Pinnacle data directory
            patient_id: Patient ID to auto-select (from Patient_#### folder)
            plan_id: Plan ID to auto-select (from Plan_## folder)
        """
        # Store auto-selection preferences
        self.auto_select_patient = patient_id
        self.auto_select_plan = plan_id

        # Load data normally
        self.load_data(data_path)

    def load_data(self, data_path):
        """Load Pinnacle data and populate the UI."""
        try:
            # Initialize PinnacleReader
            self.data_path = data_path
            self.pinnacle_reader = PinnacleReader(str(self.data_path))

            # Load initial data
            self._load_initial_data()

        except Exception as e:
            # Show error dialog
            messagebox.showerror(
                "Data Loading Error",
                f"Failed to load Pinnacle data from {self.data_path}:\\n{str(e)}"
            )

    def _load_initial_data(self):
        """Load and select the first patient, plan, and trial automatically."""
        if not self.pinnacle_reader:
            return

        # Start unified progress operation for the entire data loading chain
        progress_manager = self.start_progress_operation(
            operation_id="load_pinnacle_data_chain",
            title="Loading Pinnacle Data...",
            cancelable=True
        )

        def _load_data_thread():
            """Background thread function for data loading."""
            try:
                # Show progress dialog
                self.root.after(0, lambda: progress_manager.show("Initializing data loading...", max_progress=100))

                # Register stages for the entire chain of operations
                print("Registering stages for the entire chain of operations")
                progress_manager.register_stage("Loading Institution Data", weight=10, name="Loading Institution Data")
                progress_manager.register_stage("Populating Patients", weight=5, name="Populating Patients")
                progress_manager.register_stage("Loading Patient Data", weight=15, name="Loading Patient Data")
                progress_manager.register_stage("Loading Plan Data", weight=45, name="Loading Plan Data")
                progress_manager.register_stage("Loading Trial Data", weight=25, name="Loading Trial Data")
                print("Stages registered.")

                # Stage 1: Load institution
                progress_manager.set_current_stage("Loading Institution Data")
                if not progress_manager.update_progress(50, "Loading institution data..."):
                    return  # Operation was cancelled

                self.institution = self.pinnacle_reader.get_institution()

                if not progress_manager.update_progress(100, "Institution data loaded"):
                    return
                progress_manager.complete_stage("Loading Institution Data")

                # Stage 2: Populate left sidebar with patients
                progress_manager.set_current_stage("Populating Patients")
                if not progress_manager.update_progress(50, "Populating patient list..."):
                    return

                # Update UI on main thread
                self.root.after(0, lambda: self.left_sidebar.populate_patients(self.institution.patient_lite_list))

                if not progress_manager.update_progress(100, "Patient list populated"):
                    return
                progress_manager.complete_stage("Populating Patients")

                # Determine target patient for auto-selection
                target_patient_id = None
                if self.auto_select_patient is not None:
                    # Find patient with matching ID
                    for patient_lite in self.institution.patient_lite_list:
                        if patient_lite.patient_id == self.auto_select_patient:
                            target_patient_id = patient_lite.patient_id
                            break
                    if target_patient_id is None:
                        print(f"Warning: Could not find patient with ID {self.auto_select_patient}, using first available")

                # Use first patient if no specific selection or target not found
                if target_patient_id is None and self.institution.patient_lite_list:
                    target_patient_id = self.institution.patient_lite_list[0].patient_id

                if target_patient_id is not None:
                    # Continue with cascading selection on main thread
                    self.root.after(0, lambda: self.left_sidebar.select_patient(target_patient_id))

            except Exception as e:
                print(f"Error loading initial data: {e}")
                self.root.after(0, lambda e=e: progress_manager.complete_operation(
                    success=False,
                    final_message=f"Failed to load initial data: {str(e)}"
                ))
                self.root.after(0, lambda e=e: messagebox.showerror(
                    "Data Loading Error",
                    f"Failed to load initial data:\n\n{e}"
                ))

            finally:
                # Clear auto-selection preferences after use
                self.auto_select_patient = None
                self.auto_select_plan = None

        # Start the background thread
        threading.Thread(target=_load_data_thread, daemon=True).start()

    def on_patient_selected(self, patient_id: int):
        """Handle patient selection from left sidebar."""
        # Check if this is a duplicate selection
        if self._last_patient_selection == patient_id:
            return  # No change, don't proceed with data loading

        # Reset all selection tracking when patient changes
        self._last_patient_selection = patient_id
        self._last_plan_selection = None
        self._last_trial_selection = None

        if self.institution is None:
            messagebox.showerror(
                "Data Loading Error...",
                "Failed to load patient data: No institution found. Reopen the Pinnacle archive and try selecting the patient again."
            )
            return

        # Get existing progress manager (should be from the chain started in _load_initial_data)
        progress_manager = self.get_progress_manager()

        # If no existing progress manager, create one (for standalone patient selection)
        if not progress_manager or not progress_manager.is_active():
            progress_manager = self.start_progress_operation(
                operation_id="load_patient_data_standalone",
                title="Loading Patient Data...",
                cancelable=True
            )
            progress_manager.show("Loading patient data...", max_progress=100)
            progress_manager.register_stage("Loading Patient Data", weight=100, name="Loading Patient Data")

        def _load_patient_thread():
            """Background thread function for patient loading."""
            try:
                # Continue with patient loading stage
                progress_manager.set_current_stage("Loading Patient Data")
                if not progress_manager.update_progress(25, f"Loading patient {patient_id} data..."):
                    return  # Operation was cancelled

                patient = self.pinnacle_reader.get_patient(institution=self.institution.institution_id, patient=patient_id)

                if not progress_manager.update_progress(50, "Updating patient information..."):
                    return

                # Update right sidebar with patient info on main thread
                self.root.after(0, lambda: self.right_sidebar.update_patient_info(patient))

                if not progress_manager.update_progress(75, "Populating plan list..."):
                    return

                # Populate plans in left sidebar on main thread
                self.root.after(0, lambda: self.left_sidebar.populate_plans(patient))

                if not progress_manager.update_progress(100, f"{patient.last_and_first_name} loaded"):
                    return

                progress_manager.complete_stage("Loading Patient Data")

                # Determine target plan for auto-selection
                target_plan_id = None
                if self.auto_select_plan is not None:
                    # Find plan with matching ID
                    for plan in patient.plan_list:
                        if plan.plan_id == self.auto_select_plan:
                            target_plan_id = plan.plan_id
                            break
                    if target_plan_id is None:
                        print(f"Warning: Could not find plan with ID {self.auto_select_plan}, using first available")

                # Use first plan if no specific selection or target not found
                if target_plan_id is None and patient.plan_list:
                    target_plan_id = patient.plan_list[0].plan_id

                if target_plan_id is not None:
                    # Continue with cascading selection on main thread
                    self.root.after(0, lambda: self.left_sidebar.select_plan(target_plan_id))
                else:
                    # No plan to select, complete the chain if this was part of initial data loading
                    operation_id = getattr(progress_manager, 'operation_id', '')
                    if operation_id == "load_pinnacle_data_chain":
                        self.root.after(0, lambda: progress_manager.complete_operation(
                            success=True,
                            final_message="Data loading completed - no plans available"
                        ))

            except Exception as e:
                print(f"Error loading patient {patient_id}: {e}")
                if progress_manager:
                    self.root.after(0, lambda e=e: progress_manager.complete_operation(
                        success=False,
                        final_message=f"Failed to load patient {patient_id}: {str(e)}"
                    ))
                self.root.after(0, lambda e=e: messagebox.showerror(
                    "Patient Loading Error",
                    f"Failed to load patient {patient_id}:\n\n{e}"
                ))

        # Start the background thread
        threading.Thread(target=_load_patient_thread, daemon=True).start()

    def on_plan_selected(self, patient_id: int, plan: Plan):
        """Handle plan selection from left sidebar."""
        # Create a unique identifier for this plan selection
        plan_selection_id = (patient_id, plan.plan_id if plan else None)

        # Check if this is a duplicate selection
        if self._last_plan_selection == plan_selection_id:
            return  # No change, don't proceed with data loading

        # Reset trial selection tracking when plan changes
        self._last_plan_selection = plan_selection_id
        self._last_trial_selection = None

        if self.institution is None:
            messagebox.showerror(
                "Data Loading Error...",
                "Failed to load plan data: No institution found. Reopen the Pinnacle archive and try selecting the plan again."
            )
            return

        # Get existing progress manager (should be from the chain started in _load_initial_data)
        progress_manager = self.get_progress_manager()

        # If no existing progress manager, create one (for standalone plan selection)
        if not progress_manager or not progress_manager.is_active():
            progress_manager = self.start_progress_operation(
                operation_id="load_plan_data_standalone",
                title="Loading Plan Data...",
                cancelable=True
            )
            progress_manager.show("Loading plan data...", max_progress=100)
            progress_manager.register_stage("Loading Plan Data", weight=100, name="Loading Plan Data")

        def _load_plan_thread():
            """Background thread function for plan loading."""
            try:
                # Continue with Loading Plan Data stage
                progress_manager.set_current_stage("Loading Plan Data")
                if not progress_manager.update_progress(5, "Clearing existing data..."):
                    return  # Operation was cancelled

                # Clear all widgets before loading new plan data on main thread
                self.root.after(0, lambda: self.ct_viewer.clear_data())
                self.root.after(0, lambda: self.right_sidebar.clear_data())

                if not progress_manager.update_progress(10, "Loading trial data..."):
                    return

                trials = self.pinnacle_reader.get_trials(institution=self.institution.institution_id, patient=patient_id, plan=plan.plan_id)

                if not progress_manager.update_progress(25, "Loading ROI data..."):
                    return

                rois = self.pinnacle_reader.get_rois(institution=self.institution.institution_id, patient=patient_id, plan=plan.plan_id)

                if not progress_manager.update_progress(40, "Loading POI data..."):
                    return

                points = self.pinnacle_reader.get_points(institution=self.institution.institution_id, patient=patient_id, plan=plan.plan_id)

                if not progress_manager.update_progress(55, "Loading image set..."):
                    return

                # Load CT images for this plan first so patient_position is available
                try:
                    image_set = self.pinnacle_reader.get_image_set(institution=self.institution.institution_id, patient=patient_id, image_set=0)

                    if not progress_manager.update_progress(70, "Loading image set into CT viewer..."):
                        return

                    # Update CT viewer on main thread
                    self.root.after(0, lambda: self.ct_viewer.load_image_set(image_set))

                    # Update right sidebar with image set info on main thread
                    self.root.after(0, lambda: self.right_sidebar.update_image_set_info(image_set))

                except Exception as e:
                    print(f"Error loading image set: {e}")
                    self.root.after(0, lambda e=e: messagebox.showerror(
                        "Image Set Loading Error",
                        f"Failed to load image set:\n\n{e}"
                    ))
                    # Clear image set info on error
                    self.root.after(0, lambda: self.right_sidebar.update_image_set_info(None))

                if not progress_manager.update_progress(85, "Updating plan information..."):
                    return

                # Update left sidebar with trials on main thread
                self.root.after(0, lambda: self.left_sidebar.populate_trials(trials))

                # Update right sidebar with plan info and ROIs/POIs on main thread
                self.root.after(0, lambda: self.right_sidebar.update_plan_info(plan, rois, points))

                if not progress_manager.update_progress(100, f"Plan '{plan.name}' loaded"):
                    return

                progress_manager.complete_stage("Loading Plan Data")

                # Auto-select first trial if available on main thread
                if trials:
                    self.root.after(0, lambda: self.left_sidebar.select_trial(trials[0].trial_id))
                else:
                    # No trial to select, complete the chain if this was part of initial data loading
                    operation_id = getattr(progress_manager, 'operation_id', '')
                    if operation_id == "load_pinnacle_data_chain":
                        self.root.after(0, lambda: progress_manager.complete_operation(
                            success=True,
                            final_message="Data loading completed - no trials available"
                        ))

            except Exception as e:
                print(f"Error loading plan {plan.plan_id}: {e}")
                if progress_manager:
                    self.root.after(0, lambda e=e: progress_manager.complete_operation(
                        success=False,
                        final_message=f"Failed to load plan {plan.plan_id}: {str(e)}"
                    ))
                self.root.after(0, lambda e=e: messagebox.showerror(
                    "Plan Loading Error",
                    f"Failed to load plan {plan.plan_id}:\n\n{e}"
                ))

        # Start the background thread
        threading.Thread(target=_load_plan_thread, daemon=True).start()

    def on_trial_selected(self, patient_id: int, plan: Plan, trial: Trial):
        """Handle trial selection from left sidebar."""
        if self.institution is None:
            messagebox.showerror(
                "Data Loading Error...",
                "Failed to load trial data: No institution found. Reopen the Pinnacle archive and try selecting the trial again."
            )
            return

        # Create a unique identifier for this trial selection
        selection_id = (patient_id, plan.plan_id if plan else None, trial.trial_id if trial else None)

        # Check if this is a duplicate call
        if self._last_trial_selection == selection_id:
            return

        # Update the last selection
        self._last_trial_selection = selection_id

        # Get existing progress manager (should be from the chain started in _load_initial_data)
        progress_manager = self.get_progress_manager()

        # If no existing progress manager, create one (for standalone trial selection)
        if not progress_manager or not progress_manager.is_active():
            progress_manager = self.start_progress_operation(
                operation_id="load_trial_data_standalone",
                title="Loading Trial Data...",
                cancelable=True
            )
            progress_manager.show("Loading trial data...", max_progress=100)
            progress_manager.register_stage("Loading Trial Data", weight=100, name="Loading Trial Data")

        def _load_trial_thread():
            """Background thread function for trial loading."""
            try:
                # Continue with trial loading stage (final stage of the chain)
                progress_manager.set_current_stage("Loading Trial Data")
                if not progress_manager.update_progress(25, f"Updating trial {trial.trial_id} info..."):
                    return

                # Update right sidebar with trial info on main thread
                self.root.after(0, lambda: self.right_sidebar.update_trial_info(trial))

                if not progress_manager.update_progress(50, "Loading dose data..."):
                    return

                # Load dose data
                try:
                    dose = self.pinnacle_reader.get_dose(
                        institution=self.institution.institution_id,
                        patient=patient_id,
                        plan=plan.plan_id,
                        trial=trial
                    )

                    if not progress_manager.update_progress(85, "Processing dose data..."):
                        return

                    # Update dose info on main thread
                    self.root.after(0, lambda: self.right_sidebar.update_dose_info(dose))

                    if not progress_manager.update_progress(100, f"Trial {trial.trial_id} and dose data loaded"):
                        return

                except Exception as e:
                    print(f"Error loading dose data: {e}")
                    progress_manager.update_progress(100, "Dose data loading failed")
                    self.root.after(0, lambda e=e: messagebox.showerror(
                        "Dose Loading Error",
                        f"Failed to load dose data:\n\n{e}"
                    ))

                progress_manager.complete_stage("Loading Trial Data")

                # Complete the entire operation chain
                self.root.after(0, lambda: progress_manager.complete_operation(
                    success=True,
                    final_message=f"Pinnacle data loading completed successfully. Trial '{trial.name}' ready."
                ))

            except Exception as e:
                print(f"Error loading trial: {e}")
                if progress_manager:
                    self.root.after(0, lambda e=e: progress_manager.complete_operation(
                        success=False,
                        final_message=f"Failed to load trial: {str(e)}"
                    ))
                self.root.after(0, lambda e=e: messagebox.showerror(
                    "Trial Loading Error",
                    f"Failed to load trial:\n\n{e}"
                ))

        # Start the background thread
        threading.Thread(target=_load_trial_thread, daemon=True).start()

    def close_data(self):
        """Close all loaded Pinnacle data and reset the UI."""
        try:
            # Clear the PinnacleReader
            self.pinnacle_reader = None

            # Reset all selection tracking
            self._last_patient_selection = None
            self._last_plan_selection = None
            self._last_trial_selection = None

            # Clear left sidebar data (patients, plans, trials)
            self.left_sidebar.clear_data()

            # Clear CT viewer
            self.ct_viewer.clear_data()

            # Clear right sidebar data (all panels)
            self.right_sidebar.clear_data()

            # Re-establish callbacks and panel registrations after clearing
            self._setup_ct_viewer_callbacks()
            self._register_overlay_panels()

            print("All Pinnacle data cleared from memory")

        except Exception as e:
            print(f"Error clearing data: {e}")
            messagebox.showerror(
                "Data Clearing Error",
                f"Failed to clear data properly:\n\n{e}"
            )

    def start_progress_operation(self, operation_id: str, title: str, cancelable: bool = True) -> ProgressManager:
        """
        Initialize progress tracking for multi-stage operation.

        Args:
            operation_id: Unique identifier for this operation
            title: Dialog title to display
            cancelable: Whether the operation can be cancelled

        Returns:
            ProgressManager instance for coordinating progress across stages
        """
        self.progress_manager = ProgressManager.get_or_create_instance(
            parent=self.root,
            operation_id=operation_id,
            title=title,
            cancelable=cancelable,
            development_mode=True  # Enable development mode for detailed timing
        )
        return self.progress_manager

    def get_progress_manager(self) -> Optional[ProgressManager]:
        """
        Get current progress manager instance.

        Returns:
            Current ProgressManager instance or None if no operation is active
        """
        return self.progress_manager

    def on_closing(self):
        """Handle window closing."""
        self.root.destroy()

    def _save_dicom_files(self, save_dir: str) -> None:
        """
        Save DICOM files to the specified directory.

        Args:
            save_dir: Directory path where DICOM files should be saved
        """
        import threading
        from tkinter import messagebox
        from pinnacle_io.converters.dicom_export_manager import DicomExportManager

        try:
            # Validate that we have the necessary data loaded
            if not self._validate_current_selection():
                return

            # Get current selections
            current_patient, current_plan, current_trial = self._get_current_selections()

            if not all([current_patient, current_plan]):
                messagebox.showerror("Export Error", "Please select a patient and plan before exporting.")
                return

            # Show progress dialog (if available)
            # For now, just print progress
            print("Exporting DICOM files...")

            def do_export():
                try:
                    # Create export manager
                    export_manager = DicomExportManager(self.pinnacle_reader)

                    # Export all modalities
                    result = export_manager.export_modalities(
                        modalities=["CT", "RTSTRUCT", "RTPLAN", "RTDOSE"],
                        institution=self.institution,
                        patient=current_patient,
                        plan=current_plan,
                        trial=current_trial,
                        output_path=save_dir,
                    )

                    # Show results on main thread
                    self.root.after(0, lambda: self._show_export_results(result, "Save"))

                except Exception as e:
                    error_msg = f"Failed to export DICOM files: {str(e)}"
                    self.root.after(0, lambda: messagebox.showerror("Export Error", error_msg))
                    print(f"Export error: {e}")

            # Run export in background thread
            threading.Thread(target=do_export, daemon=True).start()

        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to start DICOM export: {str(e)}")
            print(f"Export initialization error: {e}")

    def _export_dicom_files(self, destination: dict) -> None:
        """
        Export DICOM files to a network destination via C-STORE.

        Args:
            destination: Dictionary with keys: name, host, port, ae_title, called_ae_title, etc.
        """
        import threading
        import tempfile
        from tkinter import messagebox
        from pinnacle_io.converters.dicom_export_manager import DicomExportManager

        try:
            # Validate that we have the necessary data loaded
            if not self._validate_current_selection():
                return

            # Get current selections
            current_patient, current_plan, current_trial = self._get_current_selections()

            if not all([current_patient, current_plan]):
                messagebox.showerror("Export Error", "Please select a patient and plan before exporting.")
                return

            print(f"Exporting DICOM files to {destination.get('name', 'Unknown destination')}...")

            def do_export():
                try:
                    # First, create DICOM files in a temporary directory
                    temp_dir = tempfile.mkdtemp(prefix="pinnacle_io_export_")

                    # Create export manager
                    export_manager = DicomExportManager(self.pinnacle_reader)

                    # Export all modalities to temp directory
                    result = export_manager.export_modalities(
                        modalities=["CT", "RTSTRUCT", "RTPLAN", "RTDOSE"],
                        institution=self.institution,
                        patient=current_patient,
                        plan=current_plan,
                        trial=current_trial,
                        output_path=temp_dir,
                    )

                    if result.success and result.files:
                        # Send files to network destination using C-STORE
                        self._send_dicom_files_to_destination(result.files, destination)

                        # Show success message
                        dest_name = destination.get('name', 'Network destination')
                        success_msg = f"Successfully exported DICOM files to {dest_name}"
                        self.root.after(0, lambda: messagebox.showinfo("Export Complete", success_msg))
                    else:
                        error_msg = result.error or "Failed to create DICOM files for network export"
                        self.root.after(0, lambda: messagebox.showerror("Export Error", error_msg))

                    # Clean up temporary directory
                    import shutil
                    try:
                        shutil.rmtree(temp_dir)
                    except Exception:
                        pass  # Best effort cleanup

                except Exception as e:
                    error_msg = f"Failed to export DICOM files to network: {str(e)}"
                    self.root.after(0, lambda: messagebox.showerror("Export Error", error_msg))
                    print(f"Network export error: {e}")

            # Run export in background thread
            threading.Thread(target=do_export, daemon=True).start()

        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to start network DICOM export: {str(e)}")
            print(f"Network export initialization error: {e}")

    def _validate_current_selection(self) -> bool:
        """
        Validate that the current selection is sufficient for DICOM export.

        Returns:
            True if current selection is valid for export, False otherwise
        """
        if not self.pinnacle_reader:
            messagebox.showerror("Export Error", "No data loaded. Please open a Pinnacle dataset first.")
            return False

        current_patient, current_plan, current_trial = self._get_current_selections()

        if not current_patient:
            messagebox.showerror("Export Error", "No patient selected. Please select a patient.")
            return False

        if not current_plan:
            messagebox.showerror("Export Error", "No plan selected. Please select a treatment plan.")
            return False

        return True

    def _get_current_selections(self) -> tuple:
        """
        Get the currently selected patient, plan, and trial.

        Returns:
            Tuple of (patient, plan, trial) or (None, None, None) if not available
        """
        try:
            # Get current selections from the sidebars
            # Left sidebar stores IDs and object dictionaries
            current_plan_id = getattr(self.left_sidebar, 'current_plan_id', None)
            current_trial_id = getattr(self.left_sidebar, 'current_trial_id', None)

            # Get the actual objects
            current_patient = getattr(self.right_sidebar, 'current_patient', None)

            current_plan = None
            if current_plan_id is not None and hasattr(self.left_sidebar, 'current_plans'):
                current_plan = self.left_sidebar.current_plans.get(current_plan_id)

            current_trial = None
            if current_trial_id is not None and hasattr(self.left_sidebar, 'current_trials'):
                current_trial = self.left_sidebar.current_trials.get(current_trial_id)

            return current_patient, current_plan, current_trial

        except Exception as e:
            print(f"Error getting current selections: {e}")
            messagebox.showerror(
                "Selection Error",
                f"Failed to get current selections:\n\n{e}"
            )
            return None, None, None

    def _show_export_results(self, result, operation_type: str = "Export") -> None:
        """
        Show the results of a DICOM export operation.

        Args:
            result: DicomExportResult object
            operation_type: Type of operation ("Save" or "Export")
        """
        from tkinter import messagebox

        if result.success:
            # Group files by modality type and create summary
            file_summary = []
            ct_slice_count = 0

            for file_key, path in result.files.items():
                if not path:
                    continue

                if file_key.startswith("CT_slice_"):
                    ct_slice_count += 1
                elif file_key in ["RTSTRUCT", "RTPLAN", "RTDOSE"]:
                    file_summary.append(f"• {file_key}: {os.path.basename(path)}")

            # Add CT summary if there are slices
            if ct_slice_count > 0:
                file_summary.insert(0, f"• CT: {ct_slice_count} slices exported")

            files_text = "\n".join(file_summary)
            message = f"Successfully {operation_type.lower()}ed {len(result.exported_modalities)} DICOM modalities:\n\n{files_text}"

            if result.warnings:
                warnings_text = "\n".join([f"⚠ {warning}" for warning in result.warnings])
                message += f"\n\nWarnings:\n{warnings_text}"

            messagebox.showinfo(f"{operation_type} Complete", message)
        else:
            error_message = f"Failed to {operation_type.lower()} DICOM files:\n\n{result.error or 'Unknown error occurred'}"

            if result.warnings:
                warnings_text = "\n".join([f"⚠ {warning}" for warning in result.warnings])
                error_message += f"\n\nWarnings:\n{warnings_text}"

            messagebox.showerror(f"{operation_type} Failed", error_message)

    def _send_dicom_files_to_destination(self, files: dict, destination: dict) -> None:
        """
        Send DICOM files to a network destination using C-STORE.

        Args:
            files: Dictionary mapping modality to file path
            destination: Network destination configuration
        """
        try:
            # Import pynetdicom for C-STORE operations
            from pynetdicom import AE #, debug_logger
            from pydicom import dcmread
            from pydicom.uid import ImplicitVRLittleEndian

            # Configure association entity
            ae = AE()
            ae.add_requested_context('1.2.840.10008.1.1')  # Verification SOP Class

            # Add storage contexts for all modalities
            storage_contexts = [
                '1.2.840.10008.*******.1.2',      # CT Image Storage
                '1.2.840.10008.*******.1.481.3',  # RT Structure Set Storage
                '1.2.840.10008.*******.1.481.5',  # RT Plan Storage
                '1.2.840.10008.*******.1.481.2',  # RT Dose Storage
            ]

            for context in storage_contexts:
                ae.add_requested_context(context, ImplicitVRLittleEndian)

            # Extract destination parameters
            host = destination.get('host', 'localhost')
            port = int(destination.get('port', 104))
            ae_title = destination.get('ae_title', 'PINNACLE_IO')
            called_ae_title = destination.get('called_ae_title', 'ANY-SCP')

            # Establish association
            assoc = ae.associate(host, port, ae_title=ae_title, called_ae_title=called_ae_title)

            if assoc.is_established:
                print(f"Association established with {host}:{port}")

                # Send each DICOM file
                for modality, file_path in files.items():
                    if file_path and os.path.exists(file_path):
                        try:
                            dataset = dcmread(file_path)
                            status = assoc.send_c_store(dataset)

                            if status:
                                print(f"Successfully sent {modality}: {os.path.basename(file_path)}")
                            else:
                                print(f"Failed to send {modality}: {os.path.basename(file_path)}")

                        except Exception as e:
                            print(f"Error sending {modality}: {e}")
                            # Note: This error is handled at a higher level with messagebox

                # Release association
                assoc.release()
                print("Association released")

            else:
                raise ConnectionError(f"Could not establish association with {host}:{port}")

        except ImportError:
            error_msg = "pynetdicom is required for network DICOM export but is not installed."
            messagebox.showerror("Import Error", error_msg)
            raise
        except Exception as e:
            print(f"Network export error: {e}")
            # Note: This error is re-raised and handled at a higher level with messagebox
            raise

    def run(self):
        """Start the application main loop."""
        self.root.mainloop()


def main():
    """Main entry point for the application."""
    app = MainWindow()
    app.run()


if __name__ == "__main__":
    main()