"""
Reader for Pinnacle plan.Trial files.
"""

from typing import List, Any
from pinnacle_io.models import Trial
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.patient_setup_reader import PatientSetupReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class TrialReader(BasePinnacleReader):
    """
    Reader for Pinnacle plan.Trial files.
    """

    @staticmethod
    def read_from_ids(
        institution_id: int, 
        patient_id: int, 
        plan_id: int, 
        mount_id: int = 0, 
        file_service: Any = None
    ) -> List[Trial]:
        """
        Read a Pinnacle plan.Trial file using institution, patient, and plan IDs.

        Args:
            institution_id: Institution ID number
            patient_id: Patient ID number
            plan_id: Plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)
            file_service: File service object with open_file method

        Returns:
            List of Trial models populated with data from the file

        Usage:
            trials = TrialReader.read_from_ids(1, 5, 0)
            trials = TrialReader.read_from_ids(1, 5, 0, mount_id=2)
            trials = TrialReader.read_from_ids(1, 5, 0, file_service=file_service)
        """
        # Construct standard Pinnacle path format
        plan_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"
        
        # Delegate to path-based method
        return TrialReader.read_from_path(plan_path, file_service)


    @staticmethod
    def read_from_path(plan_path: str, file_service: Any = None) -> List[Trial]:
        """
        Read a Pinnacle plan.Trial file and return the Trial models.
        The patient setup information is also processed and attached to the Trial models.

        Args:
            plan_path: Path to the Pinnacle plan.Trial file or directory
            file_service: File service object with open_file method

        Returns:
            List of Trial models populated with data from the file
        
        Usage:
            trials = TrialReader.read("/path/to/Patient_0/Plan_0/plan.Trial")
            trials = TrialReader.read("/path/to/Patient_0/Plan_0")
            trials = TrialReader.read("/path/to/Patient_0/Plan_0", file_service=file_service)
        """
        # Resolve file path and name
        file_path, file_name = TrialReader._resolve_file_path(plan_path, "plan.Trial")
        
        # Read file content using base class utility
        content_lines = TrialReader._read_file_lines(file_path, file_name, file_service)
        trials = TrialReader.parse(content_lines)

        # Try to read patient setup - use the same file service or create one if needed
        service = TrialReader._get_file_service(file_service, file_path)
        try:
            patient_position = PatientSetupReader.read(file_path, file_service=service)
            for trial in trials:
                trial._patient_position = patient_position
        except FileNotFoundError:
            # Patient setup is optional
            pass
        
        return trials

    @staticmethod
    def parse(content_lines: list[str]) -> List[Trial]:
        """
        Parse a Pinnacle plan.Trial file content and create Trial models.
        The patient setup information is NOT processed by this method.

        Args:
            content_lines: Lines from a Pinnacle plan.Trial file

        Returns:
            List of Trial models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        trials = [Trial(**trial, trial_id=i) for i, trial in enumerate(data.get("TrialList", []))]
        return trials
